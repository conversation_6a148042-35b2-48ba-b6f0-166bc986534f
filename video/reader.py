import cv2


class Reader(object):
    """Uses opencv to read a video file image by image."""

    def __init__(self, file_name):
        self.__file_name = file_name
        self.__video = cv2.VideoCapture(self.__file_name)
        self.__fps = self.__video.get(cv2.CAP_PROP_FPS)
        self.__width = self.__video.get(cv2.CAP_PROP_FRAME_WIDTH)
        self.__height = self.__video.get(cv2.CAP_PROP_FRAME_HEIGHT)

    def read_frame(self):
        """Reads and returns the current image/frame in the video.

        In addition this will tick the video to the next frame.

        Returns:
            bool: status, true if frame is read else false.
            image: image in the video.

        """
        return self.__video.read()

    def skip_to_frame(self, frame):
        """Skips to the given frame id. """
        self.__video.set(cv2.CAP_PROP_POS_FRAMES, frame)

    def skip_to_msec(self, msec):
        """Skips to the given millisecond offset in the video. """
        self.__video.set(cv2.CAP_PROP_POS_MSEC, msec)

    @property
    def opened(self):
        return self.__video.isOpened()

    @property
    def frame_id(self):
        return self.__video.get(cv2.CAP_PROP_POS_FRAMES)

    @property
    def msec(self):
        ret,frame=self.__video.read() #https://github.com/opencv/opencv/issues/15749
        return self.__video.get(cv2.CAP_PROP_POS_MSEC)

    @property
    def fps(self):
        return self.__fps

    @property
    def width(self):
        return self.__width

    @property
    def height(self):
        return self.__height
