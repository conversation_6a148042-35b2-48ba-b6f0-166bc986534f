import unittest
import os
from video.reader import Reader

video_path = os.path.dirname(__file__)+'/test_data/rockets_warriors.mp4'


class TestVideoReader(unittest.TestCase):
    def test_open(self):
        reader = Reader(video_path)
        self.assertTrue(reader.opened)

    def test_open_failute(self):
        reader = Reader('/home/<USER>/data/blah.mp4')
        self.assertFalse(reader.opened)

    def test_read(self):
        reader = Reader(video_path)
        self.assertEqual(reader.frame_id, 0)
        success, frame = reader.read_frame()
        self.assertTrue(success)
        self.assertIsNotNone(frame)
        self.assertEqual(reader.frame_id, 1)

    def test_get_and_skip_frame(self):
        reader = Reader(video_path)
        self.assertEqual(reader.frame_id, 0)
        reader.skip_to_frame(5)
        self.assertEqual(reader.frame_id, 5)

    def test_get_and_skip_msec(self):
        reader = Reader(video_path)
        self.assertEqual(reader.msec, 0.0)
        reader.skip_to_msec(1000)
        self.assertAlmostEqual(reader.msec, 1000.0, delta=50.0)

    def test_fps(self):
        reader = Reader(video_path)
        self.assertAlmostEqual(reader.fps, 30.0, delta=1.0)

    def test_width(self):
        reader = Reader(video_path)
        self.assertEqual(reader.width, 1280)

    def test_height(self):
        reader = Reader(video_path)
        self.assertEqual(reader.height, 720)


if __name__ == '__main__':
    unittest.main()