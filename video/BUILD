package(default_visibility = ["//visibility:public"])

py_library(
    name = "video",
    deps = [
        ":reader",
        ":writer",
    ],
)

py_library(
    name = "reader",
    srcs = ["reader.py"],
    deps = [
        "//third_party:numpy",
        "//third_party:opencv",
    ],
)

py_library(
    name = "writer",
    srcs = ["writer.py"],
    deps = [
        "//third_party:numpy",
        "//third_party:opencv",
    ],
)

py_test(
    name = "reader_test",
    size = "small",
    srcs = [
        "reader_test.py",
    ],
    data = [
        "test_data/rockets_warriors.mp4",
    ],
    deps = [
        "//video:reader",
    ],
)

py_test(
    name = "writer_test",
    size = "small",
    srcs = [
        "writer_test.py",
    ],
    data = [
        "test_data/rockets_warriors.mp4",
    ],
    deps = [
        "//video:reader",
        "//video:writer",
    ],
)
