import unittest
import os
from video.reader import Reader
from video.writer import Writer

video_path = os.path.dirname(__file__)+'/test_data/rockets_warriors.mp4'
temp_path = '/tmp/test.mp4'


class TestVideoWriter(unittest.TestCase):
    def test_open(self):
        writer = Writer(temp_path, 30, 1280, 720)
        self.assertTrue(writer.opened)
        self.assertTrue(os.path.isfile(temp_path))

    def test_write(self):
        # We're going to duplicate a video as a test
        reader = Reader(video_path)
        self.assertTrue(reader.opened)
        writer = Writer(temp_path, 30, 1280, 720)
        self.assertTrue(writer.opened)

        while reader.opened :
            ret, frame = reader.read_frame()
            if ret:
                writer.write(frame)
            else:
                break

        writer.close()
        self.assertFalse(writer.opened)
        # Video should be > 1 meg
        self.assertGreater(os.path.getsize(temp_path), 1024*1000)


if __name__ == '__main__':
    unittest.main()