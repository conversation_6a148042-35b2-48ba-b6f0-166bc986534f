import cv2


class Writer(object):
    """Uses opencv to write a video file image by image."""

    def __init__(self, file_name, fps, width, height):
        self.__file_name = file_name
        self.__video = cv2.VideoWriter(self.__file_name, cv2.VideoWriter_fourcc('m', 'p', '4', 'v'), fps,
                                             (width, height))

    def close(self):
        self.__video.release()

    def write(self, frame):
        self.__video.write(frame)

    @property
    def opened(self):
        return self.__video.isOpened()

