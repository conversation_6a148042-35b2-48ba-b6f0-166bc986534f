from PIL import Image, ImageFont, ImageDraw, ImageOps
import pi.eink.epd2in7 as epd2in7


class WaveshareWrapper(object):
    def __init__(self):
        print("Get display")
        self._display = epd2in7.EPD()
        print("init")
        self._display.init()
        print("Create image")
        self._image = Image.new(
            "1", (self.width, self.height), 255
        )  # 255: clear the frame
        self._draw = ImageDraw.Draw(self._image)
        # Piboto is a good default_font
        self._font = ImageFont.truetype("Piboto-Bold.ttf", 42)

    @property
    def image(self):
        return self._image

    @property
    def width(self):
        return self._display.height

    @property
    def height(self):
        return self._display.width

    @property
    def black(self):
        return 0

    @property
    def white(self):
        return 255

    @property
    def red(self):
        return 0

    def add_text(self, coordinates, message, colour=None):
        if colour is None:
            colour = self.black
        self._draw.text(coordinates, message, colour, self._font)
        return self._font.getsize(message)

    def set_font(self, font):
        self._font = font

    def message_dimensions(self, message):
        return self._font.getsize(message)

    def show(self):
        print("Display")
        self._display.display(self._display.getbuffer(self._image))
        print("Sleep")
        self._display.sleep()
        print("Done")
