from PIL import Image, ImageOps
from inky.auto import auto

class InkyImpression(object):
    def __init__(self):
        self._device = auto()

    @property
    def device(self):
        return self._device

    @property
    def width(self):
        return self._device.WIDTH

    @property
    def height(self):
        return self._device.HEIGHT

    def show(self, image, flip = True):
        resized = image.resize((self.width, self.height))
        if flip:
            resized = ImageOps.mirror(ImageOps.flip(resized))
        self._device.set_image(resized)
        self._device.show()