package(default_visibility = ["//visibility:public"])

py_binary(
    name = "inky_test",
    srcs = ["inky_test.py"],
    deps = [
        ":inky_wrapper",
    ],
)

py_library(
    name = "inky_wrapper",
    srcs = ["inky_wrapper.py"],
    deps = [
        "//third_party:inky",
    ],
)

py_library(
    name = "inky_impression",
    srcs = ["inky_impression.py"],
    deps = [
        "//third_party:inky",
    ],
)

py_binary(
    name = "waveshare_test",
    srcs = ["waveshare_test.py"],
    deps = [
        ":waveshare_wrapper",
    ],
)

py_library(
    name = "waveshare_wrapper",
    srcs = ["waveshare_wrapper.py",
    "epd2in7.py",
    "epdconfig.py"],
)
