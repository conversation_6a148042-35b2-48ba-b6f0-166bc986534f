from inky import InkyWHAT
from PIL import Image, ImageFont, ImageDraw, ImageOps


class InkyWrapper(object):
    def __init__(self, colour="black"):
        self._display = InkyWHAT(colour=colour)
        self._display.set_border(self._display.WHITE)
        self._image = Image.new("P", (self.width, self.height))
        self._draw = ImageDraw.Draw(self._image)
        # Piboto is a good default_font
        self._font = ImageFont.truetype("Piboto-Bold.ttf", 42)

    @property
    def image(self):
        return self._image

    @property
    def width(self):
        return self._display.WIDTH

    @property
    def height(self):
        return self._display.HEIGHT

    @property
    def black(self):
        return self._display.BLACK

    @property
    def WHITE(self):
        return self._display.WHITE

    @property
    def red(self):
        return self._display.RED

    def add_text(self, coordinates, message, colour=None):
        if colour is None:
            colour = self.black
        self._draw.text(coordinates, message, colour, self._font)
        return self._font.getsize(message)

    def set_font(self, font):
        self._font = font

    def message_dimensions(self, message):
        return self._font.getsize(message)

    def show(self):
        self._display.set_image(ImageOps.mirror(ImageOps.flip(self._image)))
        self._display.show()
