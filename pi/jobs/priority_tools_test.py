import unittest

import os
import shutil
import priority_tools


class PriorityToolsTest(unittest.TestCase):
    def setUp(self) -> None:
        self._test_path = "/tmp/priority_test/" + self._testMethodName
        if not os.path.exists(self._test_path):
            os.makedirs(self._test_path)

    def tearDown(self) -> None:
        if os.path.exists(self._test_path):
            shutil.rmtree(self._test_path, ignore_errors=True)

    def create_test_files(self, file_paths: list):
        for file in file_paths:
            with open(file, "w") as fp:
                pass
        return file_paths

    def test_load_image_paths(self):
        test_files = self.create_test_files(
            [
                os.path.join(self._test_path, "50_a.png"),
                os.path.join(self._test_path, "75_b.png"),
            ]
        )

        files = priority_tools.load_all_image_paths(path=self._test_path)
        self.assertEqual(len(files), 2)
        for f in test_files:
            self.assertIn(f, files)

    def test_load_image_paths_wrong_format(self):
        self.create_test_files(
            [
                os.path.join(self._test_path, "a_b.png"),
            ]
        )

        files = priority_tools.load_all_image_paths(path=self._test_path)
        self.assertEqual(len(files), 0)

    def test_get_highest_priority_paths_only_1(self):
        test_files = self.create_test_files(
            [
                os.path.join(self._test_path, "25_a.png"),
                os.path.join(self._test_path, "50_b.png"),
            ]
        )
        files = priority_tools.get_highest_priority_paths(path=self._test_path)
        self.assertEqual(len(files), 1)
        self.assertEqual(files[0], test_files[1])

    def test_get_highest_priority_paths_multiple(self):
        test_files = self.create_test_files(
            [
                os.path.join(self._test_path, "50_a.png"),
                os.path.join(self._test_path, "25_b.png"),
                os.path.join(self._test_path, "50_c.png"),
            ]
        )
        files = priority_tools.get_highest_priority_paths(path=self._test_path)
        self.assertEqual(len(files), 2)
        self.assertIn(test_files[0], files)
        self.assertIn(test_files[2], files)

    def test_get_single_highest_priority_path_only_1(self):
        test_files = self.create_test_files(
            [
                os.path.join(self._test_path, "25_a.png"),
                os.path.join(self._test_path, "50_b.png"),
            ]
        )
        file = priority_tools.get_single_highest_priority_path(path=self._test_path)
        self.assertEqual(file, test_files[1])

    def test_get_single_highest_priority_path_multiple_options(self):
        test_files = self.create_test_files(
            [
                os.path.join(self._test_path, "50_a.png"),
                os.path.join(self._test_path, "25_b.png"),
                os.path.join(self._test_path, "50_c.png"),
            ]
        )
        # Lets sample 100 times we should see each option at least once. I know
        # technically this test can fail but it won't in reality
        count_a = 0
        count_c = 0
        for x in range(0, 100):
            file = priority_tools.get_single_highest_priority_path(path=self._test_path)
            count_a = count_a + 1 if file == test_files[0] else count_a
            count_c = count_c + 1 if file == test_files[2] else count_c
        self.assertGreater(count_a, 0)
        self.assertGreater(count_c, 0)

    def test_select_contains_true(self):
        test_files = self.create_test_files(
            [
                os.path.join(self._test_path, "50_a.png"),
                os.path.join(self._test_path, "25_b.png"),
            ]
        )

        file = priority_tools.select_random_image_if_priority_list_contains(
            test_files[0]
        )
        self.assertEqual(file, test_files[0])

    def select_random_image_if_priority_list_contains_none(self):
        test_files = self.create_test_files(
            [
                os.path.join(self._test_path, "50_a.png"),
                os.path.join(self._test_path, "25_b.png"),
            ]
        )

        file = priority_tools.select_random_image_if_priority_list_contains(
            test_files[1]
        )
        self.assertIsNone(file)


if __name__ == "__main__":
    unittest.main()
