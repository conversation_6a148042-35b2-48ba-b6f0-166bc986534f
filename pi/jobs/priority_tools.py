from collections import defaultdict
import glob
from pathlib import Path
import random
import re
from typing import Optional


def load_all_image_paths(path: str = "/tmp") -> list:
    return glob.glob("%s/[0-9]*_*.png" % (path))


def get_highest_priority_paths(path: str = "/tmp") -> list:
    files = load_all_image_paths(path=path)
    priority_tasks = defaultdict(list)
    for file_path in files:
        file_name = Path(file_path).name
        priority_tasks[(int(re.findall("\d+", file_name)[0]))].append(file_path)
    if len(priority_tasks) == 0:
        return []

    max_priority = max(k for k, v in priority_tasks.items())
    return priority_tasks[max_priority]


def get_single_highest_priority_path(path: str = "/tmp") -> Optional[str]:
    files = get_highest_priority_paths(path=path)
    if len(files) == 0:
        return None
    return random.choice(files)


def select_random_image_if_priority_list_contains(file_name: str) -> Optional[str]:
    dir_path = Path(file_name).parent
    files = get_highest_priority_paths(path=dir_path)
    if file_name in files:
        return get_single_highest_priority_path(path=dir_path)
    else:
        return None
