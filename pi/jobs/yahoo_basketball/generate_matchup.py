from typing import Dict, List, Tuple
from PIL import ImageFont, ImageDraw, Image
import math
from pytz import timezone
from datetime import date, datetime
import os
import shutil

from sports.yahoo_fantasy.yahoo_token import fetch_token
import yahoo_fantasy_api as yfa

BORDER_PADDING = 5
IMAGE_WIDTH = 120
IMAGE_HEIGHT = 448

ROBOTO_PATH = os.path.dirname(__file__) + "/../../eink/roboto/"
OUTPUT_IMAGE = "/tmp/%s_yahoo_basketball.png"


def refresh_token():
    return fetch_token()


def get_current_matchup(token, sport="nba", year=2021, league="410.l.22889"):
    game = yfa.Game(token, sport)
    league = game.to_league(league)
    team_id = league.team_key()
    matchups = league.matchups()
    team_0 = {}
    team_1 = {}
    for key, value in matchups["fantasy_content"]["league"][1]["scoreboard"]["0"][
        "matchups"
    ].items():
        try:
            team_0["id"] = value["matchup"]["0"]["teams"]["0"]["team"][0][0]["team_key"]
            team_1["id"] = value["matchup"]["0"]["teams"]["1"]["team"][0][0]["team_key"]
        except:
            continue
        if team_0["id"] != team_id and team_1["id"] != team_id:
            continue

        team_0["name"] = value["matchup"]["0"]["teams"]["0"]["team"][0][2]["name"]
        team_1["name"] = value["matchup"]["0"]["teams"]["1"]["team"][0][2]["name"]

        team_0_stats = value["matchup"]["0"]["teams"]["0"]["team"][1]
        team_1_stats = value["matchup"]["0"]["teams"]["1"]["team"][1]

        team_0["points"] = team_0_stats["team_points"]["total"]
        team_1["points"] = team_1_stats["team_points"]["total"]

        stats = league.stat_categories()
        index = 0
        for stat in team_0_stats["team_stats"]["stats"]:
            stat_id = stat["stat"]["stat_id"]
            stat_value = stat["stat"]["value"]
            if stat_id == "9004003" or stat_id == "6":
                continue
            team_0[stats[index]["display_name"]] = stat_value
            index += 1

        index = 0
        for stat in team_1_stats["team_stats"]["stats"]:
            stat_id = stat["stat"]["stat_id"]
            stat_value = stat["stat"]["value"]
            if stat_id == "9004003" or stat_id == "6":
                continue
            team_1[stats[index]["display_name"]] = stat_value
            index += 1

        # Always return me as team 0
        if team_1["id"] == team_id:
            return team_1, team_0
        return team_0, team_1


def message_dimensions(font: ImageFont, message: str) -> Tuple[int, int]:
    return font.getsize(message)


def add_text(
    draw: ImageDraw,
    coordinates: Tuple[int, int],
    message: str,
    font: ImageFont,
    colour: Tuple[int, int, int] = None,
) -> Tuple[int, int]:
    if colour is None:
        colour = (0, 0, 0)
    w, h = message_dimensions(font, message)
    draw.text(((IMAGE_WIDTH - w) / 2, coordinates[1]), message, colour, font)
    return w, h


def generate_matchup_image(team_0, team_1, image_priority: int = 0) -> None:
    image = Image.new("RGB", (IMAGE_WIDTH, IMAGE_HEIGHT), (255, 255, 255))
    draw = ImageDraw.Draw(image)

    score_font = ImageFont.truetype(
        ROBOTO_PATH + "Roboto-BoldItalic.ttf", int(image.height / 20)
    )

    stat_font = ImageFont.truetype(
        ROBOTO_PATH + "Roboto-BoldItalic.ttf", int(image.height / 28)
    )

    # Iterate over games
    x = BORDER_PADDING
    y = BORDER_PADDING
    padding = 5

    w, h = add_text(
        draw, (x, y), "%s   -   %s" % (team_0["points"], team_1["points"]), score_font
    )
    y = y + h + padding

    w, h = add_text(
        draw, (x, y), "%s FG%% %s" % (team_0["FG%"], team_1["FG%"]), stat_font
    )
    y = y + h + padding

    w, h = add_text(
        draw, (x, y), "%s FT %s" % (team_0["FTM"], team_1["FTM"]), stat_font
    )
    y = y + h + padding

    w, h = add_text(
        draw, (x, y), "%s FT%% %s" % (team_0["FT%"], team_1["FT%"]), stat_font
    )
    y = y + h + padding

    w, h = add_text(
        draw, (x, y), "%s 3PTM %s" % (team_0["3PTM"], team_1["3PTM"]), stat_font
    )
    y = y + h + padding

    w, h = add_text(
        draw, (x, y), "%s PTS %s" % (team_0["PTS"], team_1["PTS"]), stat_font
    )
    y = y + h + padding

    w, h = add_text(
        draw, (x, y), "%s OREB %s" % (team_0["OREB"], team_1["OREB"]), stat_font
    )
    y = y + h + padding

    w, h = add_text(
        draw, (x, y), "%s DREB %s" % (team_0["DREB"], team_1["DREB"]), stat_font
    )
    y = y + h + padding

    w, h = add_text(
        draw, (x, y), "%s AST %s" % (team_0["AST"], team_1["AST"]), stat_font
    )
    y = y + h + padding

    w, h = add_text(draw, (x, y), "%s ST %s" % (team_0["ST"], team_1["ST"]), stat_font)
    y = y + h + padding

    w, h = add_text(
        draw, (x, y), "%s BLK %s" % (team_0["BLK"], team_1["BLK"]), stat_font
    )
    y = y + h + padding

    w, h = add_text(draw, (x, y), "%s TO %s" % (team_0["TO"], team_1["TO"]), stat_font)
    y = y + h + padding

    w, h = add_text(draw, (x, y), "%s PF %s" % (team_0["PF"], team_1["PF"]), stat_font)
    y = y + h + padding

    w, h = add_text(
        draw, (x, y), "%s TECH %s" % (team_0["TECH"], team_1["TECH"]), stat_font
    )
    y = y + h + padding

    image.save(OUTPUT_IMAGE % (image_priority), "PNG")
