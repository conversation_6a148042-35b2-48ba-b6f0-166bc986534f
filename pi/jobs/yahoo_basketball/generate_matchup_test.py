import unittest

import os
import json
import glob
from PIL import ImageFont, ImageDraw, Image
import datetime

import generate_matchup


class TestGenerateMatchup(unittest.TestCase):
    def setUp(self) -> None:
        self._sc = generate_matchup.refresh_token()
        return super().setUp()

    def test_get_current_matchup(self):
        team_0, team_1 = generate_matchup.get_current_matchup(token=self._sc)
        generate_matchup.generate_matchup_image(team_0, team_1)


if __name__ == "__main__":
    unittest.main()
