load("@pip//:requirements.bzl", "requirement")

package(default_visibility = ["//visibility:public"])

py_library(
    name = "generate_matchup",
    srcs = ["generate_matchup.py"],
    data = [
        "//pi/eink/roboto:roboto_resources",
    ],  
    deps = [
       "//sports/yahoo_fantasy:yahoo_token",
       requirement("yahoo-fantasy-api"),
       requirement("matplotlib"),
    ],
)

py_test(
    name = "generate_matchup_test",
    size = "small",
    srcs = [
        "generate_matchup_test.py",
    ],
    data = [
        "//pi/eink/roboto:roboto_resources",
    ],  
    deps = [
        ":generate_matchup"
    ],
)
