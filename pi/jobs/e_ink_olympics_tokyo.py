import warnings

warnings.filterwarnings("ignore")

from pi.eink.waveshare_wrapper import WaveshareWrapper

from PIL import ImageFont, ImageDraw
import math
import pandas as pd
import pytz
from datetime import date, datetime, timedelta
from crontab import CronTab
import os
import pwd
import sys

from sports.olympics.tokyo.schedule import Schedule
from dateutil import tz

BORDER_PADDING = 5


def calculate_grid_dimensions(num_items):
    sqr = math.sqrt(num_items)
    max_row_count = math.ceil(sqr)

    if sqr == max_row_count:
        row_count = max_row_count
        col_count = row_count
    elif (num_items % 2) == 0:
        row_count = min(num_items / 2, max_row_count)
        col_count = math.ceil(num_items / row_count)
    elif num_items > 1:
        row_count = min((num_items + 1) / 2, max_row_count)
        col_count = math.ceil((num_items + 1) / row_count)

    if row_count * num_items < num_items:
        row_count += 1

    # Max/Min is because I want to bias towards more columns
    return int(max(col_count, row_count)), int(min(col_count, row_count))


def time_string(utc_string, finished, in_progress):
    if finished:
        return "Finish"
    if in_progress:
        return "Live"

    utc = datetime.strptime(utc_string, "%Y-%m-%dT%H:%M:%S")
    from_zone = tz.tzutc()
    to_zone = tz.tzlocal()
    return utc.replace(tzinfo=from_zone).astimezone(to_zone).strftime("%H:%M")  # @ %d')


def extract_country_games(schedule_json, country="AUS"):
    games = []
    for sports in schedule_json["SportList"]:
        sport_name = sports["Sport"]["c_Short"]
        for event in sports["EventPhaseMatchList"]:
            game = dict()
            gender = event["Gender"]["c_Short"]
            local_time = time_string(
                event["DateTimes"]["Start"]["c_UTC"],
                event["b_Finished"],
                event["b_InProgress"],
            )
            match = event["Match"]
            if match is None:
                continue
            competitor1 = match["Competitor1"]["c_Short"]
            result1 = match["Competitor1"]["c_Result"]
            competitor2 = match["Competitor2"]["c_Short"]
            result2 = match["Competitor2"]["c_Result"]
            if competitor1 == country or competitor2 == country:
                game["gameStatusText"] = sport_name + " " + gender + " " + local_time
                game["gameTime"] = local_time
                game["team1"] = competitor1
                game["result1"] = "  " if result1 is None else result1
                game["team2"] = competitor2
                game["result2"] = "  " if result2 is None else result2
                games.append(game)
    return games


if __name__ == "__main__":
    # sc = Scoreboard(date.today())
    JST = pytz.timezone("Asia/Tokyo")
    today_in_japan = datetime.now(JST).date()

    games = extract_country_games(Schedule(today_in_japan).json)
    NUM_COLS, NUM_ROWS = calculate_grid_dimensions(len(games))

    # Setup display
    inky = WaveshareWrapper()
    row_height = int((inky.height - (NUM_ROWS * 2 - 1) * BORDER_PADDING) / NUM_ROWS)
    column_width = int((inky.width - (NUM_COLS * 2 - 1) * BORDER_PADDING) / NUM_COLS)

    # Iterate over games
    x = BORDER_PADDING
    y = BORDER_PADDING
    padding = 1
    for index, game in enumerate(games):
        col_index = index % NUM_COLS
        row_index = math.floor(index / NUM_COLS)
        x = BORDER_PADDING + col_index * (2 * BORDER_PADDING + column_width)
        y = BORDER_PADDING + row_index * (2 * BORDER_PADDING + row_height)

        # Draw grid lines
        if col_index > 0:
            inky._draw.line(
                (
                    x - 1.5 * BORDER_PADDING,
                    BORDER_PADDING,
                    x - 1.5 * BORDER_PADDING,
                    inky.height - BORDER_PADDING,
                ),
                inky.black,
                width=1,
            )
        if row_index > 0:
            inky._draw.line(
                (
                    BORDER_PADDING,
                    y - 1.5 * BORDER_PADDING,
                    inky.width - BORDER_PADDING,
                    y - 1.5 * BORDER_PADDING,
                ),
                inky.black,
                width=1,
            )
        MAX_DIM = max(NUM_ROWS, NUM_COLS)

        text_colour = inky.black
        game_type = game["gameStatusText"]
        inky.set_font(
            ImageFont.truetype("Piboto-BoldItalic.ttf", int(inky.height / 5 / MAX_DIM))
        )
        w, h = inky.add_text((x, y), game_type, colour=text_colour)
        y = y + h + padding
        # game_time = game['gameTime']
        # inky.set_font(ImageFont.truetype('Piboto-BoldItalic.ttf', int(inky.height/6/MAX_DIM)))
        # w, h = inky.add_text((x, y), game_time, colour=text_colour)
        # y = y+h+padding

        inky.set_font(
            ImageFont.truetype("Piboto-Bold.ttf", int(inky.width / 5 / MAX_DIM))
        )
        inky.add_text((x, y), game["team1"], colour=text_colour)
        score_or_record = game["result1"]
        w, h = inky.message_dimensions(score_or_record)
        inky.add_text(
            (x + column_width - w - BORDER_PADDING, y),
            score_or_record,
            colour=text_colour,
        )
        y = y + h + 2 * padding

        inky.add_text((x, y), game["team2"], colour=text_colour)
        score_or_record = game["result2"]
        w, h = inky.message_dimensions(score_or_record)
        inky.add_text(
            (x + column_width - w - BORDER_PADDING, y),
            score_or_record,
            colour=text_colour,
        )
        y = y + h + 2 * padding

    inky.show()
