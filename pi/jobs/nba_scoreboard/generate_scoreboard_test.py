import unittest

import os
import json
import glob
import requests_mock
from PIL import ImageFont, ImageDraw, Image
import datetime

import generate_scoreboard
from generate_scoreboard import ScoreboardRow, ScoreboardCell
from sports.yahoo_fantasy.data_objects import Matchup, MatchupTeam, Team

with open(os.path.dirname(__file__) + "/test_data/scoreboard.json", "r") as input_file:
    JSON_RESPONSE = json.loads(input_file.read())

with open(
    os.path.dirname(__file__) + "/test_data/wnba_scoreboard.json", "r"
) as input_file:
    WNBA_JSON_RESPONSE = json.loads(input_file.read())


def delete_images():
    old_outputs = glob.glob(generate_scoreboard.OUTPUT_IMAGE % ("*"))
    # for file_path in old_outputs:
    #    os.remove(file_path)


class TestGenerateScoreboard(unittest.TestCase):
    def test_get_first_game_time(self):
        with requests_mock.Mocker() as m:
            m.get(
                "https://cdn.nba.com/static/json/liveData/scoreboard/todaysScoreboard_00.json",
                json=JSON_RESPONSE,
                status_code=200,
            )
            m.get(
                "https://data.wnba.com/data/5s/v2015/json/mobile_teams/wnba/2022/scores/10_todays_scores.json",
                json=WNBA_JSON_RESPONSE,
                status_code=200,
            )
            games = generate_scoreboard.fetch_todays_games()
        self.assertEqual(len(games), 3)
        first_time = generate_scoreboard.get_first_game_time(games)
        self.assertEqual(first_time, datetime.datetime(2021, 10, 3, 12, 30))

    def test_fetch_todays_games(self):
        with requests_mock.Mocker() as m:
            m.get(
                "https://cdn.nba.com/static/json/liveData/scoreboard/todaysScoreboard_00.json",
                json=JSON_RESPONSE,
                status_code=200,
            )
            m.get(
                "https://data.wnba.com/data/5s/v2015/json/mobile_teams/wnba/2022/scores/10_todays_scores.json",
                json=WNBA_JSON_RESPONSE,
                status_code=200,
            )
        games = generate_scoreboard.fetch_todays_games()
        self.assertEqual(len(games), 3)

    def test_are_all_games_finished_yes(self):
        games = [
            ScoreboardCell(
                status=generate_scoreboard.FINISHED,
                description=None,
                game_time=None,
                home_team=None,
                away_team=None,
            ),
            ScoreboardCell(
                status=generate_scoreboard.FINISHED,
                description=None,
                game_time=None,
                home_team=None,
                away_team=None,
            ),
        ]
        self.assertTrue(generate_scoreboard.are_all_games_finished(games))

    def test_are_all_games_finished_no(self):
        games = [
            ScoreboardCell(
                status=generate_scoreboard.IN_PROGRESS,
                description=None,
                game_time=None,
                home_team=None,
                away_team=None,
            ),
            ScoreboardCell(
                status=generate_scoreboard.FINISHED,
                description=None,
                game_time=None,
                home_team=None,
                away_team=None,
            ),
        ]
        self.assertFalse(generate_scoreboard.are_all_games_finished(games))

    def test_extract_score_or_record_wins_scheduled(self):
        cell = ScoreboardCell(
            status=generate_scoreboard.SCHEDULED,
            description=None,
            game_time=None,
            home_team=ScoreboardRow(name=None, wins=5, losses=2, score=0),
            away_team=ScoreboardRow(name=None, wins=3, losses=4, score=0),
        )
        self.assertEqual(cell.home_team_score_or_record(), "5-2")
        self.assertEqual(cell.away_team_score_or_record(), "3-4")

    def test_extract_score_or_record_in_progress(self):
        cell = ScoreboardCell(
            status=generate_scoreboard.IN_PROGRESS,
            description=None,
            game_time=None,
            home_team=ScoreboardRow(name=None, wins=5, losses=2, score=75),
            away_team=ScoreboardRow(name=None, wins=3, losses=4, score=80),
        )
        self.assertEqual(cell.home_team_score_or_record(), "75")
        self.assertEqual(cell.away_team_score_or_record(), "80")

    def test_extract_score_or_record_in_finished(self):
        cell = ScoreboardCell(
            status=generate_scoreboard.FINISHED,
            description=None,
            game_time=None,
            home_team=ScoreboardRow(name=None, wins=5, losses=2, score=105),
            away_team=ScoreboardRow(name=None, wins=3, losses=4, score=102),
        )
        self.assertEqual(cell.home_team_score_or_record(), "105")
        self.assertEqual(cell.away_team_score_or_record(), "102")

    def test_calculate_grid_dimensions_0(self):
        cols, rows = generate_scoreboard.calculate_grid_dimensions(0)
        self.assertEqual(cols, 0)
        self.assertEqual(rows, 0)

    def test_calculate_grid_dimensions_1(self):
        cols, rows = generate_scoreboard.calculate_grid_dimensions(1)
        self.assertEqual(cols, 1)
        self.assertEqual(rows, 1)

    def test_calculate_grid_dimensions_2(self):
        cols, rows = generate_scoreboard.calculate_grid_dimensions(2)
        self.assertEqual(cols, 1)
        self.assertEqual(rows, 2)

    def test_calculate_grid_dimensions_4(self):
        cols, rows = generate_scoreboard.calculate_grid_dimensions(4)
        self.assertEqual(cols, 2)
        self.assertEqual(rows, 2)

    def test_calculate_grid_dimensions_7(self):
        cols, rows = generate_scoreboard.calculate_grid_dimensions(7)
        self.assertEqual(cols, 3)
        self.assertEqual(rows, 3)

    def test_message_dimensions(self):
        font = ImageFont.truetype(
            generate_scoreboard.ROBOTO_PATH + "Roboto-BoldItalic.ttf", 12
        )
        w, h = generate_scoreboard.message_dimensions(font, "Testing")
        self.assertEqual(w, 40)
        self.assertEqual(h, 15)

    def test_add_text(self):
        image = Image.new("RGB", (200, 200), (255, 255, 255))
        draw = ImageDraw.Draw(image)
        font = ImageFont.truetype(
            generate_scoreboard.ROBOTO_PATH + "Roboto-BoldItalic.ttf", 12
        )
        w, h = generate_scoreboard.add_text(draw, (10, 10), "Testing", font)
        self.assertEqual(w, 40)
        self.assertEqual(h, 15)
        pix = image.getpixel((18, 17))
        self.assertEqual(pix, (0, 0, 0))

    def test_generate_scoreboard_image_from_scoreboard_json(self):
        with requests_mock.Mocker() as m:
            m.get(
                "https://cdn.nba.com/static/json/liveData/scoreboard/todaysScoreboard_00.json",
                json=JSON_RESPONSE,
                status_code=200,
            )
            m.get(
                "https://data.wnba.com/data/5s/v2015/json/mobile_teams/wnba/2022/scores/10_todays_scores.json",
                json=WNBA_JSON_RESPONSE,
                status_code=200,
            )
            games = generate_scoreboard.fetch_todays_games()
        self.assertEqual(len(games), 3)

        delete_images()
        generate_scoreboard.generate_scoreboard_image(games, image_priority=71)
        self.assertTrue(os.path.isfile(generate_scoreboard.OUTPUT_IMAGE % 71))

    def test_generate_scoreboard_image_games_in_each_status(self):
        game_1 = ScoreboardCell(
            status=generate_scoreboard.IN_PROGRESS,
            description="3:02 q1",
            game_time=None,
            home_team=ScoreboardRow(name="HME", wins=5, losses=2, score=81),
            away_team=ScoreboardRow(name="AWY", wins=3, losses=4, score=80),
        )
        game_2 = ScoreboardCell(
            status=generate_scoreboard.SCHEDULED,
            description="7:30pm est",
            game_time=None,
            home_team=ScoreboardRow(name="HME", wins=5, losses=2, score=80),
            away_team=ScoreboardRow(name="AWY", wins=3, losses=4, score=75),
        )
        game_3 = ScoreboardCell(
            status=generate_scoreboard.FINISHED,
            description="Final",
            game_time=None,
            home_team=ScoreboardRow(name="HME", wins=5, losses=2, score=95),
            away_team=ScoreboardRow(name="AWY", wins=3, losses=4, score=101),
        )
        game_4 = ScoreboardCell.from_fantasy_matchup(
            "Test Fantasy",
            Matchup(
                first_team=MatchupTeam(
                    team=Team(key="410.l.22889.t.6", name="Double Sabonis"),
                    team_points=96.7,
                    win_probability=0.75,
                ),
                second_team=MatchupTeam(
                    team=Team(key="410.l.22889.t.7", name="Explodo Heado"),
                    team_points=96.6,
                    win_probability=0.25,
                ),
            ),
        )
        delete_images()
        generate_scoreboard.generate_scoreboard_image(
            [game_1, game_2, game_3, game_4], image_priority=72
        )
        self.assertTrue(os.path.isfile(generate_scoreboard.OUTPUT_IMAGE % 72))


if __name__ == "__main__":
    unittest.main()
