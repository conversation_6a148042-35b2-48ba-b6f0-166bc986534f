from dataclasses import dataclass
from typing import Dict, List, Tuple
from PIL import ImageFont, ImageDraw, Image
import math
from datetime import date, datetime, timedelta
import os

from sports.basketball.scrape.nba.scoreboard_today import Scoreboard
from sports.basketball.scrape.nba.constants import WNBA_KEY
from sports.yahoo_fantasy.data_objects import Matchup

SCHEDULED = 1
IN_PROGRESS = 2
FINISHED = 3

BORDER_PADDING = 10
IMAGE_WIDTH = 600
IMAGE_HEIGHT = 448

BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
GREEN = (0, 225, 0)
RED = (200, 0, 0)
MINT_GREEN = (144, 238, 144)
LIGHT_RED = (255, 198, 198)
GREY = (175, 175, 175)

ROBOTO_PATH = os.path.dirname(__file__) + "/../../eink/roboto/"
OUTPUT_IMAGE = "/tmp/%s_nba_scoreboard.png"


@dataclass
class ScoreboardRow:
    name: str
    score: float = None
    wins: int = None
    losses: int = None
    win_probability: float = None

    @staticmethod
    def from_nba_dict(team_dict: Dict):
        return ScoreboardRow(
            name=team_dict["teamTricode"],
            score=team_dict["score"],
            wins=team_dict["wins"],
            losses=team_dict["losses"],
        )

    @staticmethod
    def from_wnba_dict(team_dict: Dict):
        return ScoreboardRow(
            name=team_dict["ta"],
            score=team_dict["s"],
            wins=0,
            losses=0,
        )


@dataclass
class ScoreboardCell:
    status: int
    game_time: str
    description: str
    away_team: ScoreboardRow
    home_team: ScoreboardRow

    @staticmethod
    def from_nba_dict(game_dict: Dict):
        return ScoreboardCell(
            status=game_dict["gameStatus"],
            game_time=game_dict["gameEt"],
            description=game_dict["gameStatusText"],
            away_team=ScoreboardRow.from_nba_dict(game_dict["awayTeam"]),
            home_team=ScoreboardRow.from_nba_dict(game_dict["homeTeam"]),
        )

    @staticmethod
    def from_wnba_dict(game_dict: Dict):
        return ScoreboardCell(
            status=game_dict["st"],
            game_time="2021-10-03T15:30:00Z",
            description=game_dict["stt"],
            away_team=ScoreboardRow.from_wnba_dict(game_dict["v"]),
            home_team=ScoreboardRow.from_wnba_dict(game_dict["h"]),
        )

    @staticmethod
    def from_fantasy_matchup(league_name: str, fansaty_matchup: Matchup):
        return ScoreboardCell(
            status=(
                FINISHED
                if fansaty_matchup.first_team.win_probability == 1.0
                or fansaty_matchup.second_team.win_probability == 1.0
                else IN_PROGRESS
            ),
            description=f"F:{league_name}"[0:10],
            game_time=None,
            home_team=ScoreboardRow(
                name=fansaty_matchup.first_team.team.name[0:3].upper(),
                score=fansaty_matchup.first_team.team_points,
                win_probability=fansaty_matchup.first_team.win_probability,
            ),
            away_team=ScoreboardRow(
                name=fansaty_matchup.second_team.team.name[0:3].upper(),
                score=fansaty_matchup.second_team.team_points,
                win_probability=fansaty_matchup.second_team.win_probability,
            ),
        )

    @staticmethod
    def from_world_cup_dict(world_cup_matchup: Dict):
        return ScoreboardCell(
            status=(
                FINISHED
                if world_cup_matchup["score1"] is not None
                or world_cup_matchup["score2"] is not None
                else IN_PROGRESS
            ),
            description="World Cup",
            game_time=None,
            home_team=ScoreboardRow(
                name=world_cup_matchup["team1"][0:3].upper(),
                score=world_cup_matchup["score1"] or 0,
                win_probability=world_cup_matchup["prob1"],
            ),
            away_team=ScoreboardRow(
                name=world_cup_matchup["team2"][0:3].upper(),
                score=world_cup_matchup["score2"] or 0,
                win_probability=world_cup_matchup["prob2"],
            ),
        )

    def home_team_score_or_record(self) -> str:
        return self.__score_or_record(self.home_team)

    def away_team_score_or_record(self) -> str:
        return self.__score_or_record(self.away_team)

    def __score_or_record(self, team: ScoreboardRow) -> str:
        if self.status == SCHEDULED:
            return f"{team.wins}-{team.losses}"
        else:
            return str(round(team.score))


def get_first_game_time(games: List[ScoreboardCell]) -> datetime:
    first_game = None
    for game in games:
        game_time = datetime.strptime(game.game_time, "%Y-%m-%dT%H:%M:%S%z")
        game_time_pst = (game_time + timedelta(hours=-3)).replace(tzinfo=None)
        first_game = (
            game_time_pst
            if (first_game is None or game_time_pst < first_game)
            else first_game
        )
    return first_game


def fetch_todays_games() -> List[ScoreboardCell]:
    scoreboard = Scoreboard(date.today(), force_fetch=True, backup_path="/tmp/")
    wnba_scoreboard = Scoreboard(
        date.today(), league=WNBA_KEY, force_fetch=True, backup_path="/tmp/"
    )
    nba_cells = [
        ScoreboardCell.from_nba_dict(game)
        for game in scoreboard.json["scoreboard"]["games"]
    ]
    wnba_cells = [
        ScoreboardCell.from_wnba_dict(game) for game in wnba_scoreboard.json["gs"]["g"]
    ]
    cells = []
    if nba_cells is not None:
        cells.extend(nba_cells)
    if wnba_cells is not None:
        cells.extend(wnba_cells)

    return cells


def are_all_games_finished(games: List[ScoreboardCell]) -> bool:
    for game in games:
        if game.status != FINISHED:
            return False
    return True


def calculate_grid_dimensions(num_items: int) -> Tuple[int, int]:
    sqr = math.sqrt(num_items)
    max_row_count = math.ceil(sqr)

    if sqr == max_row_count:
        row_count = max_row_count
        col_count = row_count
    elif (num_items % 2) == 0:
        row_count = min(num_items / 2, max_row_count)
        col_count = math.ceil(num_items / row_count)
    elif num_items > 1:
        row_count = min((num_items + 1) / 2, max_row_count)
        col_count = math.ceil((num_items + 1) / row_count)

    if row_count * num_items < num_items:
        row_count += 1

    # Max/Min is because I want to bias towards more columns unless it's size 2
    if num_items == 2:
        return int(min(col_count, row_count)), int(max(col_count, row_count))
    return int(max(col_count, row_count)), int(min(col_count, row_count))


def message_dimensions(font: ImageFont, message: str) -> Tuple[int, int]:
    if isinstance(font, ImageFont.ImageFont):
        return font.getsize(message)
    if isinstance(font, ImageFont.FreeTypeFont):
        return (40, 15)


def add_text(
    draw: ImageDraw,
    coordinates: Tuple[int, int],
    message: str,
    font: ImageFont,
    colour: Tuple[int, int, int] = None,
) -> Tuple[int, int]:
    if colour is None:
        colour = BLACK
    draw.text(coordinates, message, colour, font)
    return message_dimensions(font, message)


def generate_scoreboard_image(
    games: List[ScoreboardCell], image_priority: int = 0
) -> None:
    image = Image.new("RGB", (IMAGE_WIDTH, IMAGE_HEIGHT), (255, 255, 255))
    draw = ImageDraw.Draw(image)

    num_cols, num_rows = calculate_grid_dimensions(len(games))
    max_dim = max(num_rows, num_cols)

    row_height = int((image.height - (num_rows * 2 - 1) * BORDER_PADDING) / num_rows)
    column_width = int((image.width - (num_cols * 2 - 1) * BORDER_PADDING) / num_cols)

    game_time_font = ImageFont.truetype(
        ROBOTO_PATH + "Roboto-BoldItalic.ttf", int(image.height / 7 / max_dim)
    )

    team_and_score_font = ImageFont.truetype(
        ROBOTO_PATH + "Roboto-Bold.ttf", int(image.width / 5 / max_dim)
    )

    # Iterate over games
    x = BORDER_PADDING
    y = BORDER_PADDING
    padding = 1

    for index, game in enumerate(games):
        col_index = index % num_cols
        row_index = math.floor(index / num_cols)
        x = BORDER_PADDING + col_index * (2 * BORDER_PADDING + column_width)
        y = BORDER_PADDING + row_index * (2 * BORDER_PADDING + row_height)

        # Draw grid lines
        if col_index > 0:
            draw.line(
                (
                    x - 1.5 * BORDER_PADDING,
                    BORDER_PADDING,
                    x - 1.5 * BORDER_PADDING,
                    image.height - BORDER_PADDING,
                ),
                (0, 0, 0),
                width=1,
            )
        if row_index > 0:
            draw.line(
                (
                    BORDER_PADDING,
                    y - 1.5 * BORDER_PADDING,
                    image.width - BORDER_PADDING,
                    y - 1.5 * BORDER_PADDING,
                ),
                (0, 0, 0),
                width=1,
            )

        w, h = add_text(draw, (x, y), game.description, game_time_font)
        y = y + h + padding

        colour_text_away = None
        colour_text_home = None
        colour_background_away = None
        colour_background_home = None
        if game.status != SCHEDULED:
            if game.away_team.score > game.home_team.score:
                colour_text_away = WHITE if game.status == FINISHED else GREEN
                colour_background_away = GREEN if game.status == FINISHED else None
                colour_text_home = RED
            if game.home_team.score > game.away_team.score:
                colour_text_home = WHITE if game.status == FINISHED else GREEN
                colour_background_home = GREEN if game.status == FINISHED else None
                colour_text_away = RED
        color_box_percentage_home = 1.0
        color_box_percentage_away = 1.0
        if (
            game.status != FINISHED
            and game.home_team.win_probability is not None
            and game.away_team.win_probability is not None
        ):
            color_box_percentage_home = game.home_team.win_probability
            color_box_percentage_away = game.away_team.win_probability
            colour_background_away = (
                MINT_GREEN
                if game.away_team.win_probability >= game.home_team.win_probability
                else LIGHT_RED
            )
            colour_background_home = (
                MINT_GREEN
                if game.home_team.win_probability >= game.away_team.win_probability
                else LIGHT_RED
            )
            colour_text_away = BLACK
            colour_text_home = BLACK

        _, color_box_height = message_dimensions(
            team_and_score_font, game.away_team.name
        )
        color_box_height = color_box_height + BORDER_PADDING
        if colour_background_away is not None:
            draw.rectangle(
                (
                    x,
                    y,
                    x + (column_width * color_box_percentage_away),
                    y + color_box_height + padding,
                ),
                fill=colour_background_away,
            )
        add_text(
            draw,
            (x, y),
            game.away_team.name,
            team_and_score_font,
            colour=colour_text_away,
        )
        w, h = message_dimensions(team_and_score_font, game.away_team_score_or_record())
        add_text(
            draw,
            (x + column_width - w - BORDER_PADDING, y),
            game.away_team_score_or_record(),
            team_and_score_font,
            colour=colour_text_away,
        )
        y = y + h + 2 * padding

        if colour_background_home is not None:
            draw.rectangle(
                (
                    x,
                    y,
                    x + (column_width * color_box_percentage_home),
                    y + color_box_height + padding,
                ),
                fill=colour_background_home,
            )
        add_text(
            draw,
            (x, y),
            game.home_team.name,
            team_and_score_font,
            colour=colour_text_home,
        )
        w, h = message_dimensions(team_and_score_font, game.home_team_score_or_record())
        add_text(
            draw,
            (x + column_width - w - BORDER_PADDING, y),
            game.home_team_score_or_record(),
            team_and_score_font,
            colour=colour_text_home,
        )
        y = y + h + 2 * padding

    image.save(OUTPUT_IMAGE % (image_priority), "PNG")
