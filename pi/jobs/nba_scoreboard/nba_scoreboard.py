import os
import pwd
import glob
from crontab import <PERSON><PERSON><PERSON><PERSON>
from datetime import datetime
from typing import List
from PIL import Image

from pi.eink.inky_impression import InkyImpression
import pi.jobs.nba_scoreboard.generate_scoreboard as generate_scoreboard
from pi.jobs.nba_scoreboard.generate_scoreboard import ScoreboardCell
from pi.jobs.priority_tools import select_random_image_if_priority_list_contains
import sports.yahoo_fantasy.basketball as fantasy
import sports.yahoo_fantasy.football as football
import sports.soccer.fivethirtyeight_world_cup_2022_download as world_cup


def update_cron(games):
    first_game_time = generate_scoreboard.get_first_game_time(games)

    cron = CronTab(user=pwd.getpwuid(os.getuid())[0])
    jobs = list(cron.find_command("nba_scoreboard"))
    job = jobs[0] if len(jobs) > 0 else cron.new(command="/bin/timeout -s 9 60 /home/<USER>/jobs/nba_scoreboard.par")
    # If all games are done or there are no game check every 3 hours
    if (generate_scoreboard.are_all_games_finished(games)) or (first_game_time is None):
        job.clear()
        job.hour.every(3)
        job.minute.on(15)
        cron.write()
        return 33

    # If now < first game time set cron to 5 mins after the game
    if datetime.now() < first_game_time:
        job.clear()
        job.hour.on(first_game_time.hour)
        job.minute.on(first_game_time.minute + 5)
        cron.write()
        return 33

    # Else every 5 minutes if thats not already set
    if job.frequency_per_hour() != 12:
        job.clear()
        job.every(5).minutes()
        cron.write()

    return 99


def add_fantasy_basketball(games: List[ScoreboardCell]) -> List[ScoreboardCell]:
    league_ids = ['428.l.102378', '428.l.142396', '428.l.31762']
    for league_id in league_ids:
        fantasy_league = fantasy.Basketball(league_id=league_id)
        #print(f"2023 {fantasy_league.lookup_league_ids(2023)}")
        if not fantasy_league.is_finished:
            matchup = fantasy_league.my_matchup()
            #if matchup.first_team.team_points != 0.0 or matchup.second_team.team_points != 0.0:
            games.append(ScoreboardCell.from_fantasy_matchup(fantasy_league.league_name, matchup))
    return games


def add_fantasy_football(games: List[ScoreboardCell]) -> List[ScoreboardCell]:
    league_ids = ["423.l.211857"]
    for league_id in league_ids:
        fantasy_league = football.Football(league_id=league_id)
        print(f"2023 {fantasy_league.lookup_league_ids(2023)}")
        if not fantasy_league.is_finished:
            matchup = fantasy_league.my_matchup()
            print(f"matchup {matchup}")
            #if matchup.first_team.team_points != 0.0 or matchup.second_team.team_points != 0.0:
            games.append(ScoreboardCell.from_fantasy_matchup(fantasy_league.league_name, matchup))
    return games


def add_world_cup(games: List[ScoreboardCell]) -> List[ScoreboardCell]:
    todays_games = world_cup.Downloader().todays_games()
    for match in todays_games:
        games.append(ScoreboardCell.from_world_cup_dict(match))
    return games


if __name__ == "__main__":
    minute = datetime.now().minute

    old_outputs = glob.glob(generate_scoreboard.OUTPUT_IMAGE % ("*"))
    for file_path in old_outputs:
        os.remove(file_path)

    games = generate_scoreboard.fetch_todays_games()
    image_priority = update_cron(games)
    
    fantasy_games = []
    fantasy_games = add_fantasy_basketball(fantasy_games)
    fantasy_games = add_fantasy_football(fantasy_games)

    # Alternate between fantasy or scores
    if minute % 10 < 5:
        is_close_to_0 = True
    else:
        is_close_to_0 = False
 
    if is_close_to_0 and len(fantasy_games) > 0:
        games = fantasy_games
    elif not is_close_to_0 and len(games) > 0:
        games = games
    else:
        games = games + fantasy_games

    if len(games) > 0:
        generate_scoreboard.generate_scoreboard_image(games, image_priority)
        filename = generate_scoreboard.OUTPUT_IMAGE % (image_priority)

        priority_file = select_random_image_if_priority_list_contains(filename)
        if priority_file is not None:
            with Image.open(priority_file) as im:
                eink = InkyImpression()
                eink.show(im, flip=False)
