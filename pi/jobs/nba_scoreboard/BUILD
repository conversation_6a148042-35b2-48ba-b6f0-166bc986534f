load("@pip//:requirements.bzl", "requirement")

package(default_visibility = ["//visibility:public"])

py_library(
    name = "generate_scoreboard",
    srcs = ["generate_scoreboard.py"],
    data = [
        "//pi/eink/roboto:roboto_resources",
    ],
    deps = [
        "//sports/basketball/scrape/nba:scoreboard_today",
        "//sports/yahoo_fantasy:data_objects",
        requirement("pandas"),
        requirement("matplotlib"),
    ],
)

py_test(
    name = "generate_scoreboard_test",
    size = "small",
    srcs = [
        "generate_scoreboard_test.py",
    ],
    data = [
        "test_data/scoreboard.json",
        "test_data/wnba_scoreboard.json",
        "//pi/eink/roboto:roboto_resources",
    ],
    deps = [
        ":generate_scoreboard",
        "//sports/yahoo_fantasy:data_objects",
        requirement("requests-mock"),
    ],
)

py_binary(
    name = "nba_scoreboard",
    srcs = ["nba_scoreboard.py"],
    main = "nba_scoreboard.py",
    deps = [
        ":generate_scoreboard",
        "//pi/eink:inky_impression",
        "//pi/jobs:priority_tools",
        "//sports/soccer:fivethirtyeight_world_cup_2022_download",
        "//sports/yahoo_fantasy:basketball",
        "//sports/yahoo_fantasy:football",
        requirement("python-crontab"),
    ],
)

sh_binary(
    name = "deploy",
    srcs = ["deploy.sh"],
)
