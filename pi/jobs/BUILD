package(default_visibility = ["//visibility:public"])



py_binary(
    name = "e_ink_olympics_tokyo",
    srcs = ["e_ink_olympics_tokyo.py"],
    main = "e_ink_olympics_tokyo.py",
    deps = [
        "//pi/eink:waveshare_wrapper",
        "//sports/olympics/tokyo:schedule",
        "//third_party:pandas",
        "//third_party:python-crontab",
    ],
)

py_library(
    name = "priority_tools",
    srcs = ["priority_tools.py"],
)

py_test(
    name = "priority_tools_test",
    size = "small",
    srcs = ["priority_tools_test.py"],
    deps = [
        ":priority_tools",
    ],
)
