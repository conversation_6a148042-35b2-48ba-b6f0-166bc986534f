requirements_lock.txt is generated from requirements.txt, it represents to the truth of which version of each module should be installed.

To add a new dependency add it to requirements.txt and run the below command. It will then generate all other libraries that are required to install that dependency and update requirements_lock.txt. Dependencies are install in a lazy on demand fashion when required the first time by a target.

```
bazel run //third_party:requirements.update 
```