load("@rules_python//python:pip.bzl", "compile_pip_requirements")
load("@pip//:requirements.bzl", "requirement")

package(default_visibility = ["//visibility:public"])

compile_pip_requirements(
    name = "requirements",
    timeout = "moderate",
    extra_args = ["--resolver=backtracking --allow-unsafe"],
    requirements_in = "requirements.txt",
    requirements_txt = "requirements_lock.txt",
    tags = ["skip_ci"],  # Slow and not sure why it's needed
)

# Individual package aliases for backward compatibility
py_library(
    name = "requests",
    deps = [requirement("requests")],
)

py_library(
    name = "pandas",
    deps = [requirement("pandas")],
)

py_library(
    name = "numpy",
    deps = [requirement("numpy")],
)

py_library(
    name = "opencv",
    deps = [requirement("opencv-python")],
)

py_library(
    name = "inky",
    deps = [requirement("inky")],
)

py_library(
    name = "python-crontab",
    deps = [requirement("python-crontab")],
)
