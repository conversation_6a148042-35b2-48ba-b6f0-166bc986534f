#wheel>=0.45.1

# Requests
requests>=2.32.4
requests-cache>=1.2.1
requests-mock>=1.12.1
retrying>=1.4.2

#Pandas
pandas>=2.3.1

#numpy
numpy>=2.3.2

#opencv
opencv-python>=4.5.2.54

#colorama
colorama>=0.4.6

#notebook
#jupyter>=1.1.1

#matplotlib (3.0.2 for pi)
matplotlib>=3.10.5

#plotly
plotly>=6.2.0
#kaleido>=1.0.0

#cufflinks
cufflinks>=0.17.3

#seaborn
seaborn>=0.13.2

#SQLAlchemy
SQLAlchemy>=2.0.42

#pyscopg2
psycopg2-binary>=2.9.10
#psycopg2>=2.8.6
testing.postgresql>=1.3.0

#pulp
#pulp>=2.4

#slackwrapper
slackclient>=2.9.4

#inky
inky>=2.1.0

#tabulate
tabulate>=0.9.0

#cronttab
python-crontab>=3.3.0

#yahoo fantasy sports api (not from yahoo)
#git+https://github.com/michael-quinlan/yahoo-oauth.git@e5fd4f606b7dfd431beed585f6114557fa540bf8#yahoo-oauth==1.1
yahoo-oauth>=2.1.1
yahoo-fantasy-api>=2.12.0


# Make sure robot is installed when needed
font-roboto>=0.0.1

# Abseil Python Common Libraries
absl-py>=2.3.1

# Altair plots/visualization
altair>=5.5.0