# Find the latest version number here: https://github.com/bazelbuild/rules_python/releases
# and change the version number if needed in the line below.
bazel_dep(name = "rules_python", version = "1.5.0")
bazel_dep(name = "aspect_bazel_lib", version = "1.31.2")
bazel_dep(name = "container_structure_test", version = "1.15.0")
bazel_dep(name = "rules_pkg", version = "1.0.1")
bazel_dep(name = "aspect_rules_py", version = "0.3.0")
bazel_dep(name = "platforms", version = "0.0.11")

python = use_extension("@rules_python//python/extensions:python.bzl", "python")
python.toolchain(
    python_version = "3.13",
)

pip = use_extension("@rules_python//python/extensions:pip.bzl", "pip")
pip.parse(
    hub_name = "pip",
    python_version = "3.13",
    requirements_lock = "//third_party:requirements_lock.txt",
)
use_repo(pip, "pip")
