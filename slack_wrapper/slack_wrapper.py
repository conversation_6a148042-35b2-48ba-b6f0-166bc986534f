import slack


class SlackWrapper(object):
    def __init__(
        self,
        bot_name: str,
        channel: str,
        icon_emoji: str = ":speech_balloon:",
        logging=None,
    ):
        assert bot_name is not None
        assert channel is not None
        self._bot_name = bot_name
        self._channel = channel
        self._icon_emoji = icon_emoji
        self._token = "*******************************************************"
        self._client = slack.WebClient(token=self._token)
        self._logging = logging

    # Both print and send the message to slack
    def display(self, *args, **kwargs):
        if self._logging is not None:
            self._logging.info(*args, *kwargs)
        self.send("".join(map(str, args)))

    # Send the message to the slack channel
    def send(self, message: str):
        response = self._client.chat_postMessage(
            channel=self._channel,
            text=message,
            icon_emoji=self._icon_emoji,
            username=self._bot_name,
        )
        assert response["ok"]
        assert response["message"]["text"] == message
