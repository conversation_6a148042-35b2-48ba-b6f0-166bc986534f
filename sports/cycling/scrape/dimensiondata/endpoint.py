import json
from abc import ABC<PERSON><PERSON>
from datetime import timed<PERSON><PERSON>
from os import makedirs, getenv
from os.path import expanduser

from pathlib import Path
from requests import get
from requests_cache import install_cache

BACKUP_PATH = expanduser("~") + '/filmlab/cycling/scrape/'


class Endpoint(object):
    __metaclass__ = ABCMeta
    """Super class for all endpoint based objects"""

    def __init__(self):
        cache_expiry_seconds = int(getenv('DIMENSION_DATA_CACHE_EXPIRE_SECONDS', 600))
        install_cache(cache_name='cycling_cache', expire_after=timedelta(seconds=cache_expiry_seconds))
        self._fetch()

    @property
    def json(self):
        return self._json

    @property
    def dataframe(self):
        return DataFrame.from_dict(self.json)

    @property
    def _endpoint(self):
        return 'Should never get here'

    @property
    def _params(self):
        return 'Should never get here'

    @property
    def _filename(self):
        return 'Should never get here'

    def _fetch(self):
        headers = dict({
            'user-agent': ('unknown'),
            'Dnt': ('1'),
            'Accept-Encoding': ('application/json, text/plain, */*'),
            'Accept-Language': ('en'),
            'origin': ('https://racecenter.letour.fr/')
        })

        headers['referer'] = 'http://stats.nba.com/{ref}/'.format(ref='scores')
        _get = get(self._endpoint, params=self._params, headers=headers)
        _get.raise_for_status()
        self._json = _get.json()

        # Backup json downloads to disk
        self.__save_to_disk()

    def __save_to_disk(self):
        if not Path(BACKUP_PATH).exists():
            makedirs(BACKUP_PATH)
        output_path = BACKUP_PATH + str(self._filename)
        with open(output_path, 'w') as outfile:
            json.dump(self._json, outfile)
