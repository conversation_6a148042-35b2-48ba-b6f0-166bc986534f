package(default_visibility = ["//visibility:public"])

py_library(
    name = "dimensiondata",
    deps = [
        ":route",
        ":stages",
    ],
)

py_library(
    name = "stages",
    srcs = ["stages.py"],
    deps = [
        ":endpoint",
    ],
)

py_library(
    name = "route",
    srcs = ["route.py"],
    deps = [
        ":endpoint",
    ],
)

py_library(
    name = "endpoint",
    srcs = ["endpoint.py"],
    deps = [
        "//third_party:pandas",
        "//third_party:requests",
    ],
)
