from cycling.scrape.dimensiondata.endpoint import Endpoint
from pandas import DataFrame
from os import environ


class Stages(Endpoint):
    """Downloads the list of stages for a given race"""

    def __init__(self, race_id):
        self._race_id = race_id
        # Reduce the cache time for live feeds
        #environ['DIMENSION_DATA_CACHE_EXPIRE_SECONDS'] = '20'

        Endpoint.__init__(self)

    @property
    def _endpoint(self):
        return 'http://fep-api.dimensiondata.com/v2/race/{race_id}/stages'.format(
            race_id=self._race_id)

    @property
    def dataframe(self):
        return DataFrame.from_dict(self.json)

    @property
    def _filename(self):
        return 'race_{}_stages.json'.format(self._race_id)