from cycling.scrape.dimensiondata.endpoint import Endpoint
from pandas import DataFrame


class Route(Endpoint):
    """Downloads the route for a given stage"""

    def __init__(self, stage_id, reduction_factor=100):
        self._stage_id = stage_id
        self._reduction_factor = reduction_factor

        Endpoint.__init__(self)

    @property
    def _endpoint(self):
        return 'https://fep-api.dimensiondata.com/v2/stages/v2/{stage_id}/route?reductionfactor={reduction_factor}'.format(
            stage_id=self._stage_id, reduction_factor=self._reduction_factor)

    @property
    def dataframe(self):
        return DataFrame.from_dict(self.json)

    @property
    def _filename(self):
        return 'route_for_stage_{}.json'.format(self._stage_id)