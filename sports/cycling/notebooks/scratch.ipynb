{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["%matplotlib inline\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "from cycling.scrape.dimensiondata.stages import Stages\n", "from cycling.scrape.dimensiondata.route import Route\n", "import pandas as pd\n", "\n", "import plotly.graph_objs as go\n", "import plotly.figure_factory as ff\n", "from plotly.offline import download_plotlyjs, init_notebook_mode, plot, iplot\n", "init_notebook_mode(connected=True)\n", "import cufflinks as cf\n", "cf.go_offline()\n", "\n"]}, {"cell_type": "markdown", "metadata": {"collapsed": true, "deletable": true, "editable": true}, "source": ["https://fep-api.dimensiondata.com/v2/stages/v2/342/route?reductionfactor=100\n", "https://fep-api.dimensiondata.com/v2/stages/v2/342/classification/stage\n", "https://fep-api.dimensiondata.com/v2/stages/342/withdrawals\n", " https://fep-api.dimensiondata.com/v2/stages/v2/342/group-telemetry?datasource=aso\n", "    https://fep-api.dimensiondata.com/v2/race/33/teams\n", "    https://fep-api.dimensiondata.com/v2/poi/details/342/1743/6"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["stages = Stages(race_id=33)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["stages.json"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["stages.dataframe"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": ["route = Route(stage_id=342)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": ["route.json"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": ["df = pd.DataFrame.from_dict(route.json['profilePoints'])\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": ["df.plot()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": ["df2 = df[['DistanceFromStart','Altitude']]\n", "df2['Altitude'] = df['Altitude'] / 1000.0\n", "df2.iplot(kind='scatter', x='DistanceFromStart', y='Altitude', filename='cufflinks/cf-simple-line', layout=layout)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": ["\n", "layout = go.Layout(\n", "    yaxis=dict(\n", "        scaleanchor= 'x',\n", "        scaleratio= 1.0,\n", "        range=[0, 207]\n", "    ),\n", "    xaxis=dict(\n", "        range=[0, 207]\n", "    ),\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 2}