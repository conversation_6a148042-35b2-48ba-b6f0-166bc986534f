import os
from datetime import date
from os.path import getsize, exists
from requests import get, head
import pandas as pd

DOWNLOAD_URL = "https://projects.fivethirtyeight.com/soccer-api/international/2022/wc_matches.csv"


class Downloader(object):
    # url and file_path should only passed in during testing
    def __init__(self, url=DOWNLOAD_URL, file_path="/tmp/wc_matches.csv"):
        self._url = url
        self._file_path = file_path

    def update(self):
        if self.__fetch_content_length() != self.__on_disk_size():
            self.__fetch_file()
            return True
        return False

    def json(self):
        return pd.read_csv(self._file_path)

    def todays_games(self):
        self.update()
        data = self.json()
        today = data[data["date"] == str(date.today())]
        today = today.where(pd.notnull(today), None)
        return today.to_dict("records")

    def __fetch_content_length(self):
        _response = head(self._url)
        _response.raise_for_status()
        return eval(_response.headers["Content-Length"])

    def __fetch_file(self):
        _response = get(self._url, allow_redirects=True)
        _response.raise_for_status()
        os.makedirs(os.path.dirname(self._file_path), exist_ok=True)
        open(self._file_path, "wb").write(_response.content)

    def __on_disk_size(self):
        return getsize(self._file_path) if exists(self._file_path) else 0


def execute_download():
    print(Downloader().todays_games())


if __name__ == "__main__":
    execute_download()
