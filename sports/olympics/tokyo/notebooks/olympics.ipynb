{"cells": [{"cell_type": "code", "execution_count": null, "id": "87fba7da", "metadata": {}, "outputs": [], "source": ["from requests import get\n", "from sports.olympics.tokyo.schedule import Schedule\n", "from datetime import date"]}, {"cell_type": "code", "execution_count": null, "id": "d67c5aba", "metadata": {}, "outputs": [], "source": ["sc = Schedule(date(2021, 7, 21))"]}, {"cell_type": "code", "execution_count": null, "id": "f549940f", "metadata": {}, "outputs": [], "source": ["sc.json"]}, {"cell_type": "code", "execution_count": null, "id": "e04ef743", "metadata": {}, "outputs": [], "source": ["j['SportList'][0]['EventPhaseMatchList'][5]"]}, {"cell_type": "code", "execution_count": null, "id": "641f828a", "metadata": {}, "outputs": [], "source": ["gameresult_url = \"https://api-gracenote.nbcolympics.com/svc/games_v2.svc/json/GetMatchResult_Extended?sportId=112&matchId=670140&languageCode=2\""]}, {"cell_type": "code", "execution_count": null, "id": "6ebbbe33", "metadata": {}, "outputs": [], "source": ["gr = get(gameresult_url, headers=headers, timeout=5.0).json()"]}, {"cell_type": "code", "execution_count": null, "id": "63b0d4f3", "metadata": {}, "outputs": [], "source": ["gr['TeamStatistics'][0]['c_Header']"]}, {"cell_type": "code", "execution_count": null, "id": "626df85c", "metadata": {}, "outputs": [], "source": ["sc = Schedule(date(2021, 7, 21))"]}, {"cell_type": "code", "execution_count": null, "id": "bf234a21", "metadata": {}, "outputs": [], "source": ["sc.json"]}, {"cell_type": "code", "execution_count": null, "id": "c5263c3a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}}, "nbformat": 4, "nbformat_minor": 5}