import json
from abc import ABCMeta

from requests import get


class Endpoint(object):
    __metaclass__ = ABCMeta
    """Super class for all endpoint based objects"""

    def __init__(self, **kwargs):
        self.fetch()

    @property
    def json(self):
        return self._json

    def fetch(self):
        _get = get(
            self._endpoint, params=self._params, headers=self._headers, timeout=5.0
        )
        _get.raise_for_status()
        self._json = _get.json()

    @property
    def _endpoint(self):
        return "Should never get here"

    @property
    def _params(self):
        return "Should never get here"

    @property
    def _filename(self):
        return "Should never get here"

    @property
    def _headers(self):
        return dict(
            {
                "accept": "*/*",
                "accept-encoding": "gzip, deflate, br",
                "accept-language": "en-US,en;q=0.9,nl;q=0.8",
                "origin": "https://results.nbcolympics.com",
                "referer": "https://results.nbcolympics.com/",
                "sec-ch-ua": '" Not;A Brand";v="99", "Google Chrome";v="91", "Chromium";v="91"',
                "sec-ch-ua-mobile": "?0",
                "user-agent": "Mozilla/5.0 (X11; CrOS x86_64 13904.77.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.147 Safari/537.36",
            }
        )
