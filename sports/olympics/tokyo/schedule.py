import pandas as pd


from datetime import date
from sports.olympics.tokyo.endpoint import Endpoint


class Schedule(Endpoint):
    """Downloads the schedule for a given day"""

    def __init__(self, day: date, competition_set_id=1, language_code=2, **kwargs):
        self._date = day
        self._competition_set_id = competition_set_id
        self._language_code = language_code
        Endpoint.__init__(self, **kwargs)

    @property
    def _endpoint(self):
        return "https://api-gracenote.nbcolympics.com/svc/games_v2.svc/json/GetScheduleDate"

    @property
    def _params(self):
        date_param = "{year}{month:02d}{day:02d}".format(
            month=self._date.month, day=self._date.day, year=self._date.year
        )
        return {
            "competitionSetId": self._competition_set_id,
            "date": date_param,
            "languageCode": self._language_code,
        }
