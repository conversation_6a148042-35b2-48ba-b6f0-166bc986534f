# coding=utf-8

from sqlalchemy import <PERSON>um<PERSON>, <PERSON>, <PERSON>te<PERSON>, <PERSON><PERSON><PERSON>

from sports.basketball.database.base import Base


class PlayByPlays(Base):
    __tablename__ = 'playbyplays'
    game_id = Column(String(10), ForeignKey("games.game_id"), primary_key=True, nullable=False)
    event_num = Column(Integer, primary_key=True, nullable=False)
    event_msg_type = Column(Integer)
    event_msg_action_type = Column(Integer)
    period = Column(Integer)
    seconds = Column(Integer)
    wctimestring = Column(String(20))
    home_description = Column(String(100))
    visitor_description = Column(String(100))
    home_score = Column(Integer)
    visitor_score = Column(Integer)
    person1_type = Column(Integer)
    player1_id = Column(Integer)
    person2_type = Column(Integer)
    player2_id = Column(Integer)
    person3_type = Column(Integer)
    player3_id = Column(Integer)
