# coding=utf-8

from sqlalchemy import Column, String, Integer, DateTime

from sports.basketball.database.base import Base


class LinearChampion(Base):
    __tablename__ = 'linear_champion'
    champion = Column(String(5), primary_key=True, nullable=False)
    date = Column(DateTime, primary_key=True, nullable=False)
    season = Column(Integer, nullable=False)
    neutral = Column(Integer)
    playoff = Column(String(5), nullable=True)
    team1 = Column(String(5),  nullable=False)
    team2 = Column(String(5), nullable=False)
    score1 = Column(Integer)
    score2 = Column(Integer)
