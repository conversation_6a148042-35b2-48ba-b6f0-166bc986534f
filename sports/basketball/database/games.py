# coding=utf-8

from sqlalchemy import Column, String, Integer, DateTime

from sports.basketball.database.base import Base


class Games(Base):
    __tablename__ = 'games'
    game_id = Column(String(10), primary_key=True, nullable=False)
    game_date_est = Column(DateTime)
    game_sequence = Column(Integer)
    season = Column(Integer, nullable=False)
    home_team_id = Column(Integer, nullable=False)
    visitor_team_id = Column(Integer, nullable=False)
    quarters = Column(Integer)
    status = Column(String(20))
    season_type = Column(String(10))
    league = Column(Integer, nullable=False)
