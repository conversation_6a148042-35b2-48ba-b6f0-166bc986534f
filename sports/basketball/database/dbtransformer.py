from abc import ABCMeta, abstractmethod
from sports.basketball.database.database import Database


class DbTransformer(object):
    __metaclass__ = ABCMeta
    """Super class for transforming scraped results to db rows"""

    def __init__(self, database):
        """
        Creates a db connection and then writes a row in a dataframe
        to a db table.
        """
        db = database
        session = db.session()
        # Convert the raw scraped dataframe to a dataframe with columns
        # that match the db and then to a dictionary
        transformed_dict = self.transform().to_dict('records')
        for dic_row in transformed_dict:
            # Get a blank database object
            db_row = self.orm()
            # Create the values of the columns in the db from the dictionary
            for key, value in dic_row.items():
                setattr(db_row, key, value)
            # Merge essentially does an upsert
            session.merge(db_row)
            session.commit()
        session.close()

    @abstractmethod
    def orm(self):
        """
        Returns new table row object for this transformer
        :return: SqlAclmemy ORM
        """
        pass

    @abstractmethod
    def transform(self):
        """
        Transforms the scaped dataframe to a dataframe that contains columns
        that match those in the ORM
        :return: dataframe
        """
        pass
