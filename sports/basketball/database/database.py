import pandas.io.sql as sqlio
from sqlalchemy import create_engine, MetaData
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import sessionmaker
from sports.basketball.database.base import Base
from sports.basketball.database.teams import Teams
from sports.basketball.database.games import Games
from sports.basketball.database.players import Players
from sports.basketball.database.boxscores import Boxscores
from sports.basketball.database.playbyplays import PlayByPlays
from sports.basketball.database.shotcharts import ShotCharts
from sports.basketball.database.players_boxscores_calcs import PlayersBoxscoresCalcs
from sports.basketball.database.scores_fivethirtyeight import ScoresFiveThirtyEight
from sports.basketball.database.linear_champion import Linear<PERSON>hampion
from sports.basketball.database.nba_downloads import NBADownloads


class Database(object):
    def __init__(self, database_address=None):
        if database_address is None:
            # sudo -u filmlab psql
            self._conn = self.__connect("pi", "pi", "basketball")
        else:
            self._conn = create_engine(database_address.url())
        # Setup db schema
        Teams()
        Games()
        Players()
        Boxscores()
        PlayByPlays()
        ShotCharts()
        PlayersBoxscoresCalcs()
        ScoresFiveThirtyEight()
        LinearChampion()
        NBADownloads()
        Base.metadata.create_all(self._conn)
        # Setup a session pool
        self._sessions = sessionmaker(bind=self._conn)

    def __del__(self):
        self.close()

    def session(self):
        return self._sessions()

    def metadata(self):
        return MetaData(bind=self._conn)  # , reflect=True)

    def query(self, sql):
        # Returns a pandas dataframe
        return sqlio.read_sql_query(sql, self._conn)

    def delete(self, sql):
        # Returns a pandas dataframe
        self._conn.execute(sql)

    def count(self, sql):
        # Returns a pandas dataframe
        return self.query(sql).iat[0, 0]

    def insert(self, dataframe, table, index=False):
        dataframe.to_sql(table, con=self._conn, if_exists="append", index=index)

    def insert_new_entries(self, dataframe, table, index=False):
        """try to insert each row in the dataframe, if it is new then insert if it
        already exists then ignore the update"""
        rows_inserted = 0
        for i in dataframe.index.values:
            row = dataframe.loc[[i]]
            try:
                row.to_sql(table, con=self._conn, if_exists="append", index=index)
                rows_inserted += 1
            except IntegrityError:
                pass
        return rows_inserted

    def opened(self):
        return self._conn

    def get_tables(self):
        return self.query("SELECT * FROM pg_catalog.pg_tables;")

    def close(self):
        self._conn = None

    def __connect(self, user, password, db, host="localhost"):
        return create_engine(
            "postgresql://" + user + ":" + password + "@" + host + "/" + db
        )
