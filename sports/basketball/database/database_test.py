import unittest
import pandas as pd
from sqlalchemy import Column, Table, Integer, String
from sports.basketball.database.postgress_test_case import PostgressTestCase


class TestDatabase(PostgressTestCase, unittest.TestCase):
    def setUp(self):
        super(TestDatabase, self).setUp()
        self.assertTrue(self.db.opened())
        # Drop and Create test table for each test
        self.db.delete("DROP TABLE IF EXISTS unittest;")
        metadata = self.db.metadata()
        Table(
            "unittest",
            metadata,
            Column("id", Integer, primary_key=True, nullable=False),
            Column("name", String(10), unique=True, nullable=False),
            extend_existing=True,
        )
        metadata.create_all()

    def test_get_tables(self):
        tables = self.db.get_tables()
        self.assertGreater(len(tables), 0)

    def test_query(self):
        sql = "select * from unittest;"
        df = self.db.query(sql)
        self.assertIsNotNone(df)

    def test_count(self):
        sql = "select count(*) from unittest;"
        count = self.db.count(sql)
        self.assertGreaterEqual(count, 0)

    def test_insert(self):
        sql = "select count(*) from unittest;"
        orig = self.db.count(sql)
        df = pd.DataFrame({"name": ["Name 1"]})
        self.db.insert(df, table="unittest")
        new = self.db.count(sql)
        self.assertGreater(new, orig)

    def test_insert_new_entries(self):
        sql = "select count(*) from unittest;"
        df = pd.DataFrame({"name": ["Name 1"]})
        self.db.insert(df, table="unittest")
        self.assertEqual(self.db.count(sql), 1)
        df = pd.DataFrame({"name": ["Name 2"]}, {"name": ["Name 1"]})
        count = self.db.insert_new_entries(df, table="unittest")
        self.assertEqual(count, 1)
        self.assertEqual(self.db.count(sql), 2)

    def test_query_delete(self):
        df = pd.DataFrame({"name": ["Test Name"]})
        self.db.insert(df, table="unittest")
        count_sql = "select count(*) from unittest;"
        size = self.db.count(count_sql)
        self.assertGreater(size, 0)
        delete_sql = "delete from unittest;"
        self.db.delete(delete_sql)
        size = self.db.count(count_sql)
        self.assertEqual(size, 0)


if __name__ == "__main__":
    unittest.main()
