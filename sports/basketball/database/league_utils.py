import pandas as pd
from enum import Enum


class League(Enum):
    NBA = 0
    WNBA = 10


def league_str(league_or_row):
    if is_nba(league_or_row):
        return 'NBA'
    if is_wnba(league_or_row):
        return 'WNBA'


def is_nba(league_or_row):
    assert isinstance(league_or_row, League) or isinstance(league_or_row, pd.core.series.Series)
    if isinstance(league_or_row, League):
        return league_or_row == League.NBA
    if isinstance(league_or_row, pd.core.series.Series):
        return league_or_row.league == League.NBA.value


def is_wnba(league_or_row):
    assert isinstance(league_or_row, League) or isinstance(league_or_row, pd.core.series.Series)
    if isinstance(league_or_row, League):
        return league_or_row == League.WNBA
    if isinstance(league_or_row, pd.core.series.Series):
        return league_or_row.league == League.WNBA.value