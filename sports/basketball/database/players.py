# coding=utf-8

from sqlalchemy import Column, String, Integer, DateTime

from sports.basketball.database.base import Base


class Players(Base):
    __tablename__ = 'players'
    player_id = Column(Integer, primary_key=True, nullable=False)
    first_name = Column(String(20), nullable=False)
    last_name = Column(String(30), nullable=False)
    birthdate = Column(DateTime)
    school = Column(String(50))
    country = Column(String(50))
    playercode = Column(String(30))
    from_year = Column(Integer)
    to_year = Column(Integer)
    draft_year = Column(Integer)
    draft_round = Column(Integer)
    draft_position = Column(Integer)
