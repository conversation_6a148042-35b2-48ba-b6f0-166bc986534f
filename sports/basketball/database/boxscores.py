# coding=utf-8

from sqlalchemy import <PERSON>umn, <PERSON>, Integer, <PERSON><PERSON>ey

from sports.basketball.database.base import Base


class Boxscores(Base):
    __tablename__ = 'boxscores'
    game_id = Column(String(10), ForeignKey("games.game_id"), primary_key=True, nullable=False)
    team_id = Column(Integer, primary_key=True, nullable=False)
    player_id = Column(Integer, primary_key=True,  nullable=False)
    start_position = Column(String(1))
    comment = Column(String(50))
    seconds = Column(Integer)
    fgm = Column(Integer)
    fga = Column(Integer)
    fg3m = Column(Integer)
    fg3a = Column(Integer)
    ftm = Column(Integer)
    fta = Column(Integer)
    oreb = Column(Integer)
    dreb = Column(Integer)
    reb = Column(Integer)
    ast = Column(Integer)
    stl = Column(Integer)
    blk = Column(Integer)
    to = Column(Integer)
    pf = Column(Integer)
    pts = Column(Integer)
    plus_minus = Column(Integer)
