import pandas as pd
from datetime import date
from sports.basketball.database.league_utils import League
from sports.basketball.scrape.nba.constants import (
    NBA_PRESEASON_SEASON_DATES,
    NBA_REGULAR_SEASON_DATES,
    NBA_PLAYOFF_SEASON_DATES,
    WNBA_REGULAR_SEASON_DATES,
    WNBA_PLAYOFF_SEASON_DATES,
)

REGULAR_SEASON = "regular"
PRESEASON = "preseason"
PLAYOFFS = "playoffs"
SUMMER_LEAGUE = "summer"


def season_type(row: pd.core.series.Series):
    league = row["league"]
    season = int(row["season"])
    game_date = pd.to_datetime(row["game_date_est"]).date()

    if league == League.NBA.value:
        season_range = NBA_REGULAR_SEASON_DATES[season]
    if league == League.WNBA.value:
        season_range = WNBA_REGULAR_SEASON_DATES[season]

    if game_date < season_range[0]:
        return PRESEASON
    if game_date > season_range[1]:
        return PLAYOFFS
    return REGULAR_SEASON


def game_date_to_season(league: League, game_date: date):
    if not isinstance(league, League):
        raise ValueError("league should be League enum not {}".format(type(league)))
    if league == League.NBA:
        for season_dates in (
            NBA_PRESEASON_SEASON_DATES,
            NBA_REGULAR_SEASON_DATES,
            NBA_PLAYOFF_SEASON_DATES,
        ):
            for season, dates in season_dates.items():
                if dates[0] <= game_date <= dates[1]:
                    return season

    if league == League.WNBA:
        for season_dates in (WNBA_REGULAR_SEASON_DATES, WNBA_PLAYOFF_SEASON_DATES):
            for season, dates in season_dates.items():
                if dates[0] <= game_date <= dates[1]:
                    return season
    return None
