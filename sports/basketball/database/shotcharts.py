# coding=utf-8

from sqlalchemy import Column, String, Integer, Float, ForeignKey

from sports.basketball.database.base import Base


class ShotCharts(Base):
    __tablename__ = 'shotcharts'
    game_id = Column(String(10), ForeignKey("games.game_id"), primary_key=True, nullable=False)
    game_event_id = Column(Integer, primary_key=True,  nullable=False)
    player_id = Column(Integer, primary_key=True, nullable=False)
    player_name = Column(String(50))
    team_id = Column(Integer)
    team_name = Column(String(50))
    period = Column(Integer)
    seconds = Column(Integer)
    event_type = Column(String(50))
    action_type = Column(String(50))
    shot_type = Column(String(20))
    shot_zone_basic = Column(String(50))
    shot_zone_area = Column(String(50))
    shot_zone_range = Column(String(50))
    shot_distance = Column(Float)
    loc_x = Column(Float)
    loc_y = Column(Float)
    shot_attempted_flag = Column(Integer)
    shot_made_flag = Column(Integer)