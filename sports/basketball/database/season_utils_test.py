import unittest
from datetime import date

import pandas as pd

import sports.basketball.database.league_utils as league_utils
import sports.basketball.database.season_utils as season_utils


class TestSeasonUtils(unittest.TestCase):
    def test_game_date_to_season(self):
        nba2018 = date(year=2019, month=2, day=1)
        nba1978 = date(year=1978, month=12, day=1)
        self.assertEqual(season_utils.game_date_to_season(league_utils.League.NBA, nba2018), 2018)
        self.assertEqual(season_utils.game_date_to_season(league_utils.League.NBA, nba1978), 1978)

    def test_game_date_to_season_outside(self):
        nogames = date(year=2019, month=8, day=1)
        self.assertIsNone(season_utils.game_date_to_season(league_utils.League.NBA, nogames))

    def test_game_date_to_season_wrong_type(self):
        nba2018 = date(year=2019, month=2, day=1)
        self.assertRaises(ValueError, season_utils.game_date_to_season,"00", nba2018)

    def test_nba_season_type(self):
        reg2018 = pd.Series({'season': 2018, 'game_date_est': '2019-04-01', 'league': league_utils.League.NBA.value})
        self.assertEqual(season_utils.season_type(reg2018), season_utils.REGULAR_SEASON)

        play2018 = pd.Series({'season': 2018, 'game_date_est': '2019-06-01', 'league': league_utils.League.NBA.value})
        self.assertEqual(season_utils.season_type(play2018), season_utils.PLAYOFFS)

    def test_wnba_season_type(self):
        reg2018 = pd.Series({'season': 2018, 'game_date_est': '2018-06-01', 'league': league_utils.League.WNBA.value})
        self.assertEqual(season_utils.season_type(reg2018), season_utils.REGULAR_SEASON)

        play2018 = pd.Series({'season': 2018, 'game_date_est': '2018-09-01', 'league': league_utils.League.WNBA.value})
        self.assertEqual(season_utils.season_type(play2018), season_utils.PLAYOFFS)


if __name__ == '__main__':
    unittest.main()
