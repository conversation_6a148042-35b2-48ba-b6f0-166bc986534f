package(default_visibility = ["//visibility:public"])

load("@pip//:requirements.bzl", "requirement")

py_library(
    name = "database",
    srcs = ["database.py"],
    deps = [
        ":base",
        ":orm",
        ":utils",
        requirement("SQLAlchemy"),
        requirement("pandas"),
        requirement("psycopg2-binary"),
    ],
)

py_test(
    name = "database_test",
    size = "small",
    srcs = [
        "database_test.py",
    ],
    tags = ["local"],
    deps = [
        ":database",
        ":postgress_test_case",
        requirement("pandas"),
    ],
)

py_library(
    name = "utils",
    deps = [
        ":league_utils",
        ":season_utils",
    ],
)

py_library(
    name = "dbtransformer",
    srcs = ["dbtransformer.py"],
    deps = [
        "//sports/basketball/database",
        requirement("pandas"),
    ],
)

py_library(
    name = "base",
    srcs = ["base.py"],
    deps = [
        requirement("SQLAlchemy"),
    ],
)

py_library(
    name = "orm",
    deps = [
        ":boxscores",
        ":games",
        ":linear_champion",
        ":nba_downloads",
        ":playbyplays",
        ":players",
        ":players_boxscores_calcs",
        ":scores_fivethirtyeight",
        ":shortcharts",
        ":teams",
    ],
)

py_library(
    name = "teams",
    srcs = [
        "teams.py",
    ],
    deps = [
        "//sports/basketball/database:base",
        requirement("SQLAlchemy"),
    ],
)

py_library(
    name = "games",
    srcs = [
        "games.py",
    ],
    deps = [
        "//sports/basketball/database:base",
        requirement("SQLAlchemy"),
    ],
)

py_library(
    name = "players",
    srcs = [
        "players.py",
    ],
    deps = [
        "//sports/basketball/database:base",
        requirement("SQLAlchemy"),
    ],
)

py_library(
    name = "boxscores",
    srcs = [
        "boxscores.py",
    ],
    deps = [
        "//sports/basketball/database:base",
        requirement("SQLAlchemy"),
    ],
)

py_library(
    name = "playbyplays",
    srcs = [
        "playbyplays.py",
    ],
    deps = [
        "//sports/basketball/database:base",
        requirement("SQLAlchemy"),
    ],
)

py_library(
    name = "players_boxscores_calcs",
    srcs = [
        "players_boxscores_calcs.py",
    ],
    deps = [
        "//sports/basketball/database:base",
        requirement("SQLAlchemy"),
    ],
)

py_library(
    name = "shortcharts",
    srcs = [
        "shotcharts.py",
    ],
    deps = [
        "//sports/basketball/database:base",
        requirement("SQLAlchemy"),
    ],
)

py_library(
    name = "scores_fivethirtyeight",
    srcs = [
        "scores_fivethirtyeight.py",
    ],
    deps = [
        "//sports/basketball/database:base",
        requirement("SQLAlchemy"),
    ],
)

py_library(
    name = "linear_champion",
    srcs = [
        "linear_champion.py",
    ],
    deps = [
        "//sports/basketball/database:base",
        requirement("SQLAlchemy"),
    ],
)

py_library(
    name = "nba_downloads",
    srcs = [
        "nba_downloads.py",
    ],
    deps = [
        "//sports/basketball/database:base",
        requirement("SQLAlchemy"),
    ],
)

py_binary(
    name = "createtables",
    srcs = ["createtables.py"],
    deps = [
        ":database",
        requirement("psycopg2-binary"),
    ],
)

py_library(
    name = "league_utils",
    srcs = ["league_utils.py"],
)

py_test(
    name = "league_utils_test",
    size = "small",
    srcs = [
        "league_utils_test.py",
    ],
    tags = ["local"],
    deps = [
        "//sports/basketball/database",
        requirement("pandas"),
    ],
)

py_library(
    name = "season_utils",
    srcs = ["season_utils.py"],
    deps = [
        ":league_utils",
        "//sports/basketball/scrape/nba:constants",
    ],
)

py_test(
    name = "season_utils_test",
    size = "small",
    srcs = [
        "season_utils_test.py",
    ],
    tags = ["local"],
    deps = [
        "//sports/basketball/database",
        requirement("pandas"),
    ],
)

py_library(
    name = "postgress_test_case",
    srcs = ["postgress_test_case.py"],
    deps = [
        "//sports/basketball/database",
        requirement("testing.postgresql"),
    ],
)
