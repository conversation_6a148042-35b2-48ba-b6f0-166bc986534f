import unittest
import pandas as pd

import sports.basketball.database.league_utils as league_utils


class TestLeagueUtils(unittest.TestCase):
    def setUp(self):
        self._nba_row = pd.Series({"league": league_utils.League.NBA.value})
        self._wnba_row = pd.Series({"league": league_utils.League.WNBA.value})

    def test_league_str_nba(self):
        self.assertEqual(league_utils.league_str(league_utils.League.NBA), 'NBA')
        self.assertEqual(league_utils.league_str(self._nba_row), 'NBA')

    def test_league_str_wnba(self):
        self.assertEqual(league_utils.league_str(league_utils.League.WNBA), 'WNBA')
        self.assertEqual(league_utils.league_str(self._wnba_row), 'WNBA')

    def test_is_nba_int(self):
        self.assertTrue(league_utils.is_nba(league_utils.League.NBA))
        self.assertFalse(league_utils.is_nba(league_utils.League.WNBA))

    def test_is_nba_row(self):
        self.assertTrue(league_utils.is_nba(self._nba_row))

    def test_is_wnba(self):
        self.assertTrue(league_utils.is_wnba(league_utils.League.WNBA))
        self.assertFalse(league_utils.is_wnba(league_utils.League.NBA))

    def test_is_wnba_row(self):
        self.assertTrue(league_utils.is_wnba(self._wnba_row))


if __name__ == '__main__':
    unittest.main()
