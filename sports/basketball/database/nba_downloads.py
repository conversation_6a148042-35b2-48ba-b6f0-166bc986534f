# coding=utf-8

from sqlalchemy import Column, String, Integer, DateTime

from sports.basketball.database.base import Base


class NBADownloads(Base):
    __tablename__ = "nba_downloads"
    game_id = Column(String(10), primary_key=True, nullable=False)
    game_date_est = Column(DateTime)
    season = Column(Integer, nullable=False)
    game_status_id = Column(Integer)
    status = Column(String(20))
    num_box_score = Column(Integer)
    num_play_by_play = Column(Integer)
    num_shot_chart = Column(Integer)
