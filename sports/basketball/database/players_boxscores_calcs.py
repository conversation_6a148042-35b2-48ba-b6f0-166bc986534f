# coding=utf-8

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Float, String

from sports.basketball.database.base import Base


class PlayersBoxscoresCalcs(Base):
    __tablename__ = 'players_boxscores_calcs'
    game_id = Column(String(10), primary_key=True, nullable=False)
    player_id = Column(Integer, primary_key=True,  nullable=False)
    offensive_rating = Column(Float)
    defensive_rating = Column(Float)
    offensive_rating = Column(Float)
    defensive_rating = Column(Float)
    efg_percentage = Column(Float)
    ts_percentage = Column(Float)
    fg3_attempt_rate = Column(Float)
    ft_attempt_rate = Column(Float)
    oreb_percentage = Column(Float)
    dreb_percentage = Column(Float)
    reb_percentage = Column(Float)
    ast_percentage = Column(Float)
    stl_percentage = Column(Float)
    blk_percentage = Column(Float)
    to_percentage = Column(Float)
    usage_percentage = Column(Float)
