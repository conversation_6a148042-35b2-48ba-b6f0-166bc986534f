import unittest
import testing.postgresql

from sports.basketball.database.database import Database


class PostgressTestCase(unittest.TestCase):
    _postgresql = None
    _database = None

    @classmethod
    def setUpClass(cls):
        cls._factory = testing.postgresql.PostgresqlFactory(cache_initialized_db=True)

    @classmethod
    def tearDownClass(cls):
        cls._factory.clear_cache()

    def setUp(self):
        # Set database to None, only instiatate if needed
        self._postgresql = None
        self._database = None

    def tearDown(self):
        if self._postgresql is not None:
            self._postgresql.stop()

    @property
    def db(self):
        if self._postgresql is None:
            self._postgresql = self._factory()
            self._database = Database(database_address=self._postgresql)
        return self._database
