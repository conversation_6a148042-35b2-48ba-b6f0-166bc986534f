import os

import pandas as pd
from tabulate import tabulate

from sports.basketball.database.database import Database
from sports.basketball.markdown.markdown_writer import <PERSON><PERSON><PERSON>riter, SITE_ROOT
from sports.basketball.database.season_utils import game_date_to_season
from sports.basketball.database.league_utils import league_str, League
from sports.basketball.stats import basic


class GameSummary(MarkdownWriter):
    def __init__(self, game_id: int, output_root:str = SITE_ROOT):
        self._db = Database()
        self._game_id = game_id

        self._home_team_id = None
        self._home_team_abbr = None
        self._visitor_team_id = None
        self._visitor_team_abbr = None

        filename = self.extract_game_info(output_root)

        MarkdownWriter.__init__(self, filename=filename,
                                title="{} at {}".format(self._visitor_team_abbr, self._home_team_abbr))

        self.add_basic_boxscore()

        self.write()

    def extract_game_info(self, output_root):
        game_data = self._db.query("select * from games where game_id={}".format(self._game_id))
        league = League(game_data['league'][0])
        game_date = game_data['game_date_est'].dt.date[0]
        self._home_team_id = game_data['home_team_id'][0]
        self._visitor_team_id = game_data['visitor_team_id'][0]
        self._home_team_abbr = \
            self._db.query("select * from teams where team_id={}".format(self._home_team_id))['abbreviation'][0]
        self._visitor_team_abbr = \
            self._db.query("select * from teams where team_id={}".format(self._visitor_team_id))['abbreviation'][0]

        month_str = game_date.strftime('%B').lower()
        page_path = '{}/{}/{}/{}/{}/'.format(output_root, league_str(league).lower(),
                                             game_date_to_season(league, game_date), month_str, game_date.day)
        filename = '{}_{}.md'.format(self._visitor_team_abbr, self._home_team_abbr)
        try:
            os.makedirs(page_path)
        except FileExistsError:
            # directory already exists
            pass
        return "{}{}".format(page_path, filename).lower()

    def add_basic_boxscore(self):
        self.add_header(level=3, text="Basic Boxscore")
        self.add_boxscore(self._visitor_team_id, self._visitor_team_abbr)
        self.add_boxscore(self._home_team_id, self._home_team_abbr)

    def add_boxscore(self, team_id, team_abbr):
        self.add_header(level=4, text=team_abbr)
        df = self._db.query(
            "select * from boxscores join players_boxscores_calcs on boxscores.game_id=players_boxscores_calcs.game_id and boxscores.player_id=players_boxscores_calcs.player_id join players on boxscores.player_id = players.player_id where boxscores.game_id={} and boxscores.team_id={}".format(
                self._game_id, team_id))
        df['min'] = df.apply(basic.minutes_str, axis=1)
        df['fg%'] = df.apply(basic.fg_percentage, axis=1)
        df['fg'] = df.apply(basic.fg_str, axis=1)
        df['3pt%'] = df.apply(basic.fg3_percentage, axis=1)
        df['3pt'] = df.apply(basic.fg3_str, axis=1)
        df['ft%'] = df.apply(basic.ft_percentage, axis=1)
        df['ft'] = df.apply(basic.ft_str, axis=1)
        df = df.rename({'plus_minus': '+/-'}, axis=1)
        df = df.rename({'start_position': 'start'}, axis=1)
        df['name'] = df.apply(basic.first_last_name, axis=1)
        df = df.round({'fg%': 3, '3pt%': 3, 'ft%': 3})
        df = df.sort_values(by=['start', 'seconds'], ascending=False)
        box = df[
            ['start', 'name', 'min', '+/-', 'fg', 'fg%', '3pt', '3pt%', 'ft', 'ft%', 'oreb', 'reb', 'ast', 'stl', 'blk',
             'to', 'pf', 'pts']]
        self.add_paragraph(tabulate(box, tablefmt="github", headers="keys"))

        box_a = df[
            ['start', 'name', 'min', 'offensive_rating', 'defensive_rating', 'usage_percentage', 'efg_percentage',
             'fg3_attempt_rate', 'ft_attempt_rate', 'oreb_percentage', 'dreb_percentage', 'reb_percentage',
             'ast_percentage', 'stl_percentage', 'blk_percentage', 'to_percentage']]
        box_a = box_a.rename({'offensive_rating': 'off rtg'}, axis=1)
        box_a = box_a.rename({'defensive_rating': 'def rtg'}, axis=1)
        box_a = box_a.rename({'efg_percentage': 'eFG%'}, axis=1)
        box_a = box_a.rename({'fg3_attempt_rate': '3PAr'}, axis=1)
        box_a = box_a.rename({'ft_attempt_rate': 'FTAr'}, axis=1)
        box_a = box_a.rename({'oreb_percentage': 'orb%'}, axis=1)
        box_a = box_a.rename({'dreb_percentage': 'drb%'}, axis=1)
        box_a = box_a.rename({'reb_percentage': 'rb%'}, axis=1)
        box_a = box_a.rename({'ast_percentage': 'ast%'}, axis=1)
        box_a = box_a.rename({'stl_percentage': 'stl%'}, axis=1)
        box_a = box_a.rename({'blk_percentage': 'blk%'}, axis=1)
        box_a = box_a.rename({'to_percentage': 'to%'}, axis=1)
        box_a = box_a.rename({'usage_percentage': 'usage%'}, axis=1)
        box_a = box_a.round(
            {'off rtg': 2, 'def rtg': 2, 'usage%': 2, 'eFG%': 3, '3PAr': 2, 'FTAr': 2, 'orb%': 2, 'drb%': 2, 'rb%': 2,
             'ast%': 2, 'stl%': 2, 'blk%': 2, 'to%': 2})
        box_a = box_a.replace(pd.np.nan, '', regex=True)
        self.add_paragraph(tabulate(box_a, tablefmt="github", headers="keys"))
