
SITE_ROOT = '/home/<USER>/github/michael-quinlan/static_site/lab2305/content'

class MarkdownWriter(object):

    def __init__(self, filename: str, title: str, description: str = "", weight=1):
        assert filename is not None
        assert title is not None
        self._filename = filename
        self._title = title
        self._description = description
        self._weight = weight

        self._data = ''
        self.__add_page_header()

    def write(self):
        with open(self._filename, 'w') as file:
            file.write(self._data)

    def add_header(self, level:int, text: str):
        assert level is not None
        self._data += "{} {} {}\n".format('#'*level, text, '#'*level)

    def add_line(self, text: str):
        self._data += '{}\n'.format(text)

    def add_paragraph(self, text: str):
        self._data += '{}\n\n'.format(text)

    def __add_page_header(self):
        self.add_line('+++')
        self.add_line('title = "{}"'.format(self._title))
        self.add_line('description = "{}"'.format(self._description))
        self.add_line('weight = {}'.format(self._weight))
        self.add_line('+++')
