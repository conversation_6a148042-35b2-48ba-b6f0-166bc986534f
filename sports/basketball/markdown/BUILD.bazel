package(default_visibility = ["//visibility:public"])

load("@pip//:requirements.bzl", "requirement")

py_library(
    name = "markdown",
    deps = [
        ":game_summary",
        ":markdown_writer",
    ],
)

py_library(
    name = "markdown_writer",
    srcs = ["markdown_writer.py"],
)

py_library(
    name = "game_summary",
    srcs = ["game_summary.py"],
    deps = [
        ":markdown_writer",
        "//sports/basketball/database",
        "//sports/basketball/stats",
        requirement("pandas"),
        requirement("tabulate"),
    ],
)

py_test(
    name = "game_summary_test",
    size = "small",
    srcs = [
        "game_summary_test.py",
    ],
    tags = ["manual"],
    deps = [
        "//sports/basketball/markdown:game_summary",
    ],
)
