import unittest
import os

from sports.basketball.markdown.game_summary import GameSummary


class TestGameSummary(unittest.TestCase):
    def test_generation(self):
        GameSummary(game_id=1021900075, output_root='/tmp')
        self.assertTrue(os.path.isfile('/tmp//wnba/2019/july/3/nyl_sea.md'))
        self.assertGreater(os.path.getsize('/tmp//wnba/2019/july/3/nyl_sea.md'), 8000)

if __name__ == '__main__':
    unittest.main()
