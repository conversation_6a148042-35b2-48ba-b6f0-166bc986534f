import warnings

warnings.filterwarnings("ignore")

import sys

from sports.basketball.database.database import Database
from sports.basketball.linear_champion.calculator import Calculator
from slack_wrapper.slack_wrapper import SlackWrapper
import pandas as pd


def run_update(database: Database):
    results = Calculator(database=database).calculate()
    max_champion_date = database.query("select max(date) from linear_champion").iat[
        0, 0
    ]
    max_result_date = pd.to_datetime(results.dataframe["date"]).iloc[-1]
    if max_champion_date is None or max_result_date > max_champion_date:
        results.write_to_database(database=database)

    # Don't post on slack if in a unit test
    if "unittest" not in sys.modules:
        slack = SlackWrapper(
            bot_name="LC Bot", icon_emoji=":trophy:", channel="#nba_linear_champion"
        )
        slack.display(results.streak)


if __name__ == "__main__":
    run_update(Database())
