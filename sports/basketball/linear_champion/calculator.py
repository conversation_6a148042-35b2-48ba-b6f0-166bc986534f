import pandas as pd
from sports.basketball.linear_champion.result import Result
from sports.basketball.database.database import Database


class Calculator(object):
    def __init__(
        self, database: Database = None, file_path: str = None, include_playoffs=True
    ):
        if file_path is None and database is None:
            raise ValueError("Must pass in either a database or file_path")
        if file_path is not None and database is not None:
            raise ValueError("Do not pass in both a database and file_path")

        if file_path is None:
            sql = "select * from scores_fivethirtyeight order by date"
            self._raw_dataframe = database.query(sql)
        else:
            self._raw_dataframe = pd.read_csv(file_path)
        filtered = self._raw_dataframe[
            ["date", "season", "playoff", "team1", "team2", "score1", "score2"]
        ]
        filtered["playoff"].replace({"NaN": None}, inplace=True)
        # Remove playoff games
        if not include_playoffs:
            filtered = filtered[filtered.playoff.isnull()]
        # The champion deadends after the first nba season due to D<PERSON> not playing in season 2, so lets skip that year.
        filtered = filtered[filtered.season > 1948]
        # Games in the future can have elo rating but no scores, ignore them for linear championship.
        self._dataframe = filtered.dropna(subset=["score1", "score2"])

    @property
    def raw_dataframe(self):
        return self._raw_dataframe

    @property
    def dataframe(self):
        return self._dataframe

    # Function that calculates the sequence of champions given a set of games and an optional starting champion.
    # Where possible use the above methods with known filters, but this is provided to allow arbitrary filters and
    # queries.
    def calculate(self, games_dataframe=None, current_champion=None):
        if games_dataframe is None:
            games_dataframe = self.dataframe
        history = []
        for index, row in games_dataframe.iterrows():
            winner = Calculator.__team_winner(row)
            if not current_champion or Calculator.__team_in_game(current_champion, row):
                current_champion = winner
                row["champion"] = current_champion
                history.append(row)
        return Result(pd.DataFrame(history).reset_index())

    # Return which team won the game
    @staticmethod
    def __team_winner(row):
        if row.score1 > row.score2:
            return row.team1
        else:
            return row.team2

    # Is a team playing in a game
    @staticmethod
    def __team_in_game(team: str, row):
        return row.team1 == team or row.team2 == team
