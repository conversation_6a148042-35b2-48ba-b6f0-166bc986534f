import os
import unittest
import pandas as pd

from sports.basketball.linear_champion.update import run_update
from sports.basketball.database.postgress_test_case import PostgressTestCase


class TestUpdate(PostgressTestCase, unittest.TestCase):
    def setUp(self):
        super(TestUpdate, self).setUp()

    def test_Update(self):
        df = pd.DataFrame(
            {
                "date": ["2021-07-14 00:00:00"],
                "season": [2021],
                "neutral": [0],
                "playoff": ["f"],
                "team1": ["MIL"],
                "team2": ["PHO"],
                "score1": [109],
                "score2": [103],
            }
        )
        self.db.insert(df, table="scores_fivethirtyeight")
        run_update(self.db)
        champion = self.db.query("select champion from linear_champion")
        self.assertEqual(champion.values[0], "MIL")


if __name__ == "__main__":
    unittest.main()
