package(default_visibility = ["//visibility:public"])

load("@pip//:requirements.bzl", "requirement")

py_library(
    name = "linear_champion",
    deps = [
        ":calculator",
        ":result",
        "//sports/basketball/scrape/fivethirtyeight:constants",
    ],
)

py_library(
    name = "calculator",
    srcs = ["calculator.py"],
    deps = [
        ":result",
        "//sports/basketball/database",
        "//sports/basketball/scrape/fivethirtyeight:constants",
        requirement("pandas"),
    ],
)

py_test(
    name = "calculator_test",
    size = "small",
    srcs = [
        "calculator_test.py",
    ],
    data = [
        "//sports/basketball/scrape/fivethirtyeight:nba_elo_test_file",
    ],
    deps = [
        ":calculator",
    ],
)

py_library(
    name = "result",
    srcs = ["result.py"],
    deps = [
        "//sports/basketball/database:dbtransformer",
        "//sports/basketball/database:linear_champion",
        "//sports/basketball/scrape/fivethirtyeight:constants",
        requirement("pandas"),
    ],
)

py_test(
    name = "result_test",
    size = "small",
    srcs = [
        "result_test.py",
    ],
    data = [
        "//sports/basketball/scrape/fivethirtyeight:nba_elo_test_file",
    ],
    deps = [
        ":calculator",
        ":result",
    ],
)

py_binary(
    name = "update",
    srcs = ["update.py"],
    deps = [
        ":linear_champion",
        "//slack_wrapper",
        "//sports/basketball/database",
    ],
)

py_test(
    name = "update_test",
    size = "small",
    srcs = [
        "update_test.py",
    ],
    deps = [
        ":update",
        "//sports/basketball/database:postgress_test_case",
    ],
)
