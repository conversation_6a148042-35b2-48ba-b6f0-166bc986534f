import pandas as pd
from sports.basketball.database.dbtransformer import DbT<PERSON>sformer
from sports.basketball.database.linear_champion import Linear<PERSON>hampion

class Result(DbTransformer):

    def __init__(self, champion_list : pd.DataFrame):
        self._dataframe = champion_list

    @property
    def dataframe(self):
        return self._dataframe

    @property
    def current_champion(self):
        return self._dataframe.tail(1).champion.values[0]

    @property
    def streak(self):
        df = pd.DataFrame()
        champ = self.current_champion
        for index, row in self._dataframe[::-1].iterrows():
            if row.champion == champ:
                df = df.append(row,ignore_index=True)
            else:
                break
        return df

    @property
    def streak_length(self):
        return len(self.streak.index)

    def filter_season(self, season):
        return Result(self._dataframe[self._dataframe.season==season])

    def filter_regular_season(self):
        return Result(self._dataframe[self._dataframe.playoff.isnull()])

    # The following functions allow the persistence of results to a database.
    def orm(self):
        return LinearChampion()

    def transform(self):
        return self._dataframe

    def write_to_database(self, **kwargs):
        DbTransformer.__init__(self, **kwargs)
