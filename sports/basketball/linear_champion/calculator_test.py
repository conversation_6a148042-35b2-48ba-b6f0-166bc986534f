import os
import unittest

from sports.basketball.linear_champion.calculator import Calculator

file_path = os.path.dirname(__file__) + "/..//scrape/fivethirtyeight/nba_elo_test.csv"


class TestLinearChampion(unittest.TestCase):
    def setUp(self):
        self.calculator = Calculator(file_path=file_path)

    def test_raw_dataframe(self):
        self.assertEqual(len(self.calculator.raw_dataframe), 68404)

    def test_dataframe(self):
        self.assertEqual(self.calculator.dataframe.shape[0], 67826)
        self.assertEqual(self.calculator.dataframe.shape[1], 7)

        self.assertCountEqual(
            self.calculator.dataframe.columns,
            ["date", "season", "playoff", "team1", "team2", "score1", "score2"],
        )

    def test_no_playoffs(self):
        no_playoffs = Calculator(file_path=file_path, include_playoffs=False)
        self.assertEqual(no_playoffs.dataframe.shape[0], 63521)
        self.assertEqual(no_playoffs.dataframe.shape[1], 7)
        self.assertEqual(no_playoffs.dataframe.tail(1).team1.values[0], "LAC")
        self.assertEqual(no_playoffs.dataframe.tail(1).team2.values[0], "UTA")

    def test_calculate(self):
        # For detailed tests on the result of a calculate look at result_test.py
        result = self.calculator.calculate()
        self.assertEqual(result.dataframe.shape[0], 6683)
        self.assertEqual(result.dataframe.tail(1).champion.values[0], "GSW")

    def test_database_and_file_path_are_none_raises(self):
        with self.assertRaises(ValueError):
            Calculator()

    def test_database_and_file_path_are_both_set_raises(self):
        with self.assertRaises(ValueError):
            Calculator(database="db", file_path="path")


if __name__ == "__main__":
    unittest.main()
