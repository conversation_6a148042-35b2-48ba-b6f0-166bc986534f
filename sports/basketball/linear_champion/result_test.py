import os
import unittest

from sports.basketball.linear_champion.calculator import Calculator

file_path = os.path.dirname(__file__) + "/..//scrape/fivethirtyeight/nba_elo_test.csv"


class TestResult(unittest.TestCase):
    # Since this takes 10+ seconds, lets calculate once and not in setup
    result = Calculator(file_path=file_path).calculate()

    def setUp(self):
        pass

    def test_current_champion(self):
        self.assertEqual(TestResult.result.current_champion, "GSW")

    def test_streak_length(self):
        self.assertEqual(TestResult.result.streak_length, 1)

    def test_filter_season(self):
        filtered = TestResult.result.filter_season(2000)
        self.assertEqual(filtered.current_champion, "DEN")

    def test_filter_regular_season(self):
        filtered = TestResult.result.filter_regular_season()
        self.assertEqual(filtered.current_champion, "POR")
        self.assertEqual(filtered.streak_length, 3)


if __name__ == "__main__":
    unittest.main()
