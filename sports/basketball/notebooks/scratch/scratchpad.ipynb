{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["%matplotlib inline\n", "%load_ext autoreload\n", "%autoreload 2\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "import pandas as pd\n", "from sports.basketball.database.database import Database\n", "from sports.basketball.stats.shotcharts import plot as plot_shotchart\n", "from sports.basketball.scrape.nba.scoreboard import Scoreboard\n", "from datetime import date\n", "from tabulate import tabulate\n", "db = Database()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["db.query(\"select * from scores_fivethirtyeight order by date\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["db.query(\"select * from games where season_type like 'all-star'\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["db.query(\"update games set status = 'Ignore Final' where game_id=39000001\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["df = db.query(\"select * from boxscores where game_id=1021900039\")\n", "print(tabulate(df, tablefmt=\"pipe\", headers=\"keys\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["db.query(\"update games set league = 10 where game_id>1000000000\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["db.query(\"select * from games where game_id=1021900059\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["from scrape.nba.scoreboard import Scoreboard\n", "Scoreboard(day=date(2019,6,23), league='10').dataframe"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["db.query(\"select * from players where player_id=201147\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 2}