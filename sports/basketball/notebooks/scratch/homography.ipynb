{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["%matplotlib inline\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "import cv2\n", "import numpy as np\n", "from matplotlib import pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["# Read source image.\n", "im_src = cv2.imread('/home/<USER>/shot.png')\n", "RGB_src = cv2.cvtColor(im_src, cv2.COLOR_BGR2RGB)\n", "# Four corners of the book in source image\n", "pts_src = np.array([[226, 346], [406, 346],[248, 560], [226, 560], [597, 250]])\n", " \n", "# Read destination image.\n", "im_dst = cv2.imread('/home/<USER>/court.png')\n", "RGB_dst = cv2.cvtColor(im_dst, cv2.COLOR_BGR2RGB)\n", "# Four corners of the book in destination image.\n", "pts_dst = np.array([[1851, 1430],[1570, 1136], [3224, 1299], [3288, 1338], [737, 906]])\n", " \n", "# Calculate Homography\n", "h, status = cv2.findHomography(pts_src, pts_dst)\n", "     \n", "# Warp source image to destination based on homography\n", "im_out = cv2.warpPerspective(im_src, h, (im_dst.shape[1],im_dst.shape[0]))\n", "     \n", "# Display images\n", "#cv2.imshow(\"Source Image\", im_src)\n", "#cv2.imshow(\"Destination Image\", im_dst)\n", "#cv2.imshow(\"Warped Source Image\", im_out)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["RGB_out = cv2.cvtColor(im_out, cv2.COLOR_BGR2RGB)\n", "plt.figure(figsize=(32, 18))\n", "plt.imshow(RGB_dst)\n", "plt.imshow(RGB_out, alpha=0.7)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["plt.imshow(RGB_src)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["plt.imshow(RGB_dst)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["from stats.shotcharts import draw_court"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["plt.figure(figsize=(12, 11))\n", "ax = draw_court()\n", "ax.set(aspect=\"equal\")\n", "plt.xlim(-280, 280)\n", "plt.ylim(-80, 450)\n", "plt.savefig(\"/home/<USER>/shot.png\", bbox_inches='tight', pad_inches=0)   \n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 2}