{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["\n", "%matplotlib inline\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "import plotly.graph_objs as go\n", "import plotly.figure_factory as ff\n", "from plotly.offline import download_plotlyjs, init_notebook_mode, plot, iplot\n", "import numpy as np\n", "import seaborn as sns\n", "import scipy.stats as st\n", "import cufflinks as cf\n", "cf.set_config_file(offline=True)\n", "init_notebook_mode(connected=True)\n", "\n", "import pandas as pd\n", "from sports.basketball.database.database import Database\n", "from sports.basketball.stats.shotcharts import plot as plot_shotchart\n", "db = Database()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["GAME_ID = 21700536\n", "game_df = db.query(\"select * from shotcharts where game_id = \" + str(GAME_ID) + \" order by game_event_id\")"]}, {"cell_type": "markdown", "metadata": {"collapsed": true, "deletable": true, "editable": true}, "source": ["<script src=\"https://cdn.plot.ly/plotly-latest.min.js\"></script"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["game_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": ["def kde_scipy(vals1, vals2, a,b, c,d, N ):\n", "\n", "    #vals1, vals2 are the values of two variables (columns)\n", "    #(a,b) interval for vals1; usually larger than (np.min(vals1), np.max(vals1))\n", "    #(c,d) -\"-          vals2 \n", "\n", "    x=np.linspace(a,b,N)\n", "    y=np.linspace(c,d,N)\n", "    X,Y=np.meshgrid(x,y)\n", "    positions = np.vstack([<PERSON>.ravel(), Y.ravel()])\n", "    values = np.vstack([vals1, vals2])\n", "    kernel = st.gaussian_kde(values)\n", "    Z = np.reshape(kernel(positions).T, X.shape)\n", "\n", "    return [x, y, Z]\n", "\n", "x, y, Z = kde_scipy(-game_df.loc_x, game_df.loc_y, -251, 251, -101, 423.5, 50)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["# point on the center of the court (dummy data)\n", "\n", "makes = go.<PERSON><PERSON><PERSON>(\n", "    x = -game_df[game_df.shot_made_flag==1].loc_x, \n", "    y = game_df[game_df.shot_made_flag==1].loc_y, \n", "    mode = 'markers',\n", "    marker = dict(\n", "        size = 10,\n", "        color = 'rgba(0, 200, 0, .75)',\n", "        line = dict(\n", "              color = 'rgb(40, 40, 40)',\n", "              width = 1\n", "            )\n", "    ),\n", "    text= game_df.player_name,\n", "    hoverinfo='text',\n", "    name = 'Made',\n", ")\n", "\n", "misses = go.<PERSON><PERSON>(\n", "    x = -game_df[game_df.shot_made_flag==0].loc_x, \n", "    y = game_df[game_df.shot_made_flag==0].loc_y, \n", "    mode = 'markers',\n", "    marker = dict(\n", "        size = 10,\n", "        color = 'rgba(200, 0, 0, .75)',\n", "        line = dict(\n", "              color = 'rgb(40, 40, 40)',\n", "              width = 1\n", "            )\n", "    ),\n", "    text= game_df.player_name,\n", "    hoverinfo='text',\n", "    name = 'Missed',\n", ")\n", "\n", "contours = go.Heatmap(\n", "        z=Z,\n", "        x=x,\n", "        y=y,\n", "        name = 'Shot Distribution',\n", "        colorscale='Greys',\n", "        reversescale=True,\n", "        opacity=1.0,\n", "        showscale=False,\n", "        hoverinfo = 'skip',\n", "        zsmooth = 'best',\n", ")\n", "\n", "data = [misses, makes, contours]\n", "# list containing all the shapes\n", "court_shapes = []\n", "\n", "outer_lines_shape = dict(\n", "  type='rect',\n", "  xref='x',\n", "  yref='y',\n", "  x0='-250',\n", "  y0='-47.5',\n", "  x1='250',\n", "  y1='422.5',\n", "  line=dict(\n", "      color='rgba(169, 169, 169, 1)',\n", "      width=2\n", "  ),\n", "  fillcolor= 'rgba(240, 240, 240, 0.5)',\n", ")\n", " \n", "court_shapes.append(outer_lines_shape)\n", "\n", "hoop_shape = dict(\n", "  type='circle',\n", "  xref='x',\n", "  yref='y',\n", "  x0='7.5',\n", "  y0='7.5',\n", "  x1='-7.5',\n", "  y1='-7.5',\n", "  line=dict(\n", "    color='rgba(169, 169, 169, 1)',\n", "    width=2\n", "  )\n", ")\n", " \n", "court_shapes.append(hoop_shape)\n", "\n", "backboard_shape = dict(\n", "  type='rect',\n", "  xref='x',\n", "  yref='y',\n", "  x0='-30',\n", "  y0='-7.5',\n", "  x1='30',\n", "  y1='-6.5',\n", "  line=dict(\n", "    color='rgba(169, 169, 169, 1)',\n", "    width=2\n", "  ),\n", "  fillcolor='rgba(169, 169, 169, 1)'\n", ")\n", " \n", "court_shapes.append(backboard_shape)\n", "\n", "outer_three_sec_shape = dict(\n", "  type='rect',\n", "  xref='x',\n", "  yref='y',\n", "  x0='-80',\n", "  y0='-47.5',\n", "  x1='80',\n", "  y1='143.5',\n", "  line=dict(\n", "      color='rgba(169, 169, 169, 1)',\n", "      width=2\n", "  )\n", ")\n", " \n", "court_shapes.append(outer_three_sec_shape)\n", "\n", "outer_three_sec_shape = dict(\n", "  type='rect',\n", "  xref='x',\n", "  yref='y',\n", "  x0='-80',\n", "  y0='-47.5',\n", "  x1='80',\n", "  y1='143.5',\n", "  line=dict(\n", "      color='rgba(169, 169, 169, 1)',\n", "      width=2\n", "  )\n", ")\n", " \n", "court_shapes.append(outer_three_sec_shape)\n", "\n", "inner_three_sec_shape = dict(\n", "  type='rect',\n", "  xref='x',\n", "  yref='y',\n", "  x0='-60',\n", "  y0='-47.5',\n", "  x1='60',\n", "  y1='143.5',\n", "  line=dict(\n", "      color='rgba(169, 169, 169, 1)',\n", "      width=2\n", "  )\n", ")\n", " \n", "court_shapes.append(inner_three_sec_shape)\n", "\n", "left_line_shape = dict(\n", "  type='line',\n", "  xref='x',\n", "  yref='y',\n", "  x0='-220',\n", "  y0='-47.5',\n", "  x1='-220',\n", "  y1='92.5',\n", "  line=dict(\n", "      color='rgba(169, 169, 169, 1)',\n", "      width=2\n", "  )\n", ")\n", " \n", "court_shapes.append(left_line_shape)\n", "\n", "right_line_shape = dict(\n", "  type='line',\n", "  xref='x',\n", "  yref='y',\n", "  x0='220',\n", "  y0='-47.5',\n", "  x1='220',\n", "  y1='92.5',\n", "  line=dict(\n", "      color='rgba(169, 169, 169, 1)',\n", "      width=2\n", "  )\n", ")\n", " \n", "court_shapes.append(right_line_shape)\n", "\n", "three_point_circle = dict(\n", "  type='circle',\n", "  xref='x',\n", "  yref='y',\n", "  x0='238.5',\n", "  y0='238.5',\n", "  x1='-238.5',\n", "  y1='-238.5',  \n", "  line=dict(\n", "      color='rgba(169, 169, 169, 1)',\n", "      width=2\n", "  )\n", ")\n", " \n", "#court_shapes.append(three_point_circle)\n", "\n", "\n", "three_point_arc_shape = dict(\n", "  type='path',\n", "  xref='x',\n", "  yref='y',\n", "  path='M -220 92.5 C -130 288, 130 288, 220 92.5',\n", "  line=dict(\n", "      color='rgba(169, 169, 169, 1)',\n", "      width=2\n", "  )\n", ")\n", " \n", "court_shapes.append(three_point_arc_shape)\n", "\n", "center_circe_shape = dict(\n", "  type='circle',\n", "  xref='x',\n", "  yref='y',\n", "  x0='60',\n", "  y0='482.5',\n", "  x1='-60',\n", "  y1='362.5',\n", "  line=dict(\n", "      color='rgba(169, 169, 169, 1)',\n", "      width=2\n", "  )\n", ")\n", " \n", "#court_shapes.append(center_circle_shape)\n", "\n", "center_lower_circle_shape = dict(\n", "  type='path',\n", "  xref='x',\n", "  yref='y',\n", "  path='M -60 422.5 C -52 343, 52 343, 60 422.5',\n", "  line=dict(\n", "      color='rgba(169, 169, 169, 1)',\n", "      width=2,\n", "  )\n", ")\n", "court_shapes.append(center_lower_circle_shape)\n", "\n", "res_circle_shape = dict(\n", "  type='circle',\n", "  xref='x',\n", "  yref='y',\n", "  x0='20',\n", "  y0='442.5',\n", "  x1='-20',\n", "  y1='402.5',\n", "  line=dict(\n", "      color='rgba(169, 169, 169, 1)',\n", "      width=2\n", "  )\n", ")\n", " \n", "#court_shapes.append(res_circle_shape)\n", "\n", "free_throw_circle_shape = dict(\n", "  type='circle',\n", "  xref='x',\n", "  yref='y',\n", "  x0='60',\n", "  y0='200',\n", "  x1='-60',\n", "  y1='80',\n", "  line=dict(\n", "      color='rgba(169, 169, 169, 1)',\n", "      width=2\n", "  )\n", ")\n", " \n", "#court_shapes.append(free_throw_circle_shape)\n", "\n", "\n", "free_throw_top_circle_shape = dict(\n", "  type='path',\n", "  xref='x',\n", "  yref='y',\n", "  path='M -60 140 C -52 219.5, 52 219.5, 60 140',\n", "  line=dict(\n", "      color='rgba(169, 169, 169, 1)',\n", "      width=2,\n", "  )\n", ")\n", "court_shapes.append(free_throw_top_circle_shape)\n", "\n", "free_throw_bottom_circle_shape = dict(\n", "  type='path',\n", "  xref='x',\n", "  yref='y',\n", "  path='M -60 140 C -52 60.5, 52 60.5, 60 140',\n", "  line=dict(\n", "      color='rgba(169, 169, 169, 1)',\n", "      width=2,\n", "      dash='dash'\n", "  )\n", ")\n", "court_shapes.append(free_throw_bottom_circle_shape)\n", "\n", "res_area_shape = dict(\n", "  type='circle',\n", "  xref='x',\n", "  yref='y',\n", "  x0='40',\n", "  y0='40',\n", "  x1='-40',\n", "  y1='-40',\n", "  line=dict(\n", "    color='rgba(169, 169, 169, 1)',\n", "    width=2,\n", "    dash='dot'\n", "  )\n", ")\n", " \n", "#court_shapes.append(res_area_shape)\n", "\n", "res_arc_shape = dict(\n", "  type='path',\n", "  xref='x',\n", "  yref='y',\n", "  path='M -40 0 C -35 54, 35 54, 40 0',\n", "  line=dict(\n", "      color='rgba(169, 169, 169, 1)',\n", "      width=2,\n", "  )\n", ")\n", "court_shapes.append(res_arc_shape)\n", "\n", "layout = go.Layout(\n", "    autosize=True,\n", "    hovermode = 'closest',\n", "    xaxis=dict(\n", "        showgrid=False,\n", "        range=[-251, 251],\n", "        showline=False,\n", "        zeroline=False,\n", "        showticklabels=False,\n", "    ),\n", "    yaxis=dict(\n", "        showgrid=False,\n", "        scaleanchor= 'x',\n", "        scaleratio= 1.0,\n", "        range=[-101, 423.5],\n", "        showline=False,\n", "        zeroline=False,\n", "        showticklabels=False,\n", "    ),\n", "    margin=go.layout.<PERSON><PERSON>(\n", "        l=0, #left margin\n", "        r=0, #right margin\n", "        b=0, #bottom margin\n", "        t=0, #top margin\n", "        pad=0,\n", "    ),\n", "    shapes=court_shapes,\n", ")\n", "\n", "fig = go.Figure(data=data, layout=layout)\n", "\n", "iplot(fig, config=dict(displaylogo=False,\n", "                 displayModeBar=False))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["plot_shotchart(game_df, title='All Shots', output_file='/tmp/game_chart.png')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["t = np.linspace(-1, 1.2, 2000)\n", "x = -game_df.loc_x\n", "y = game_df.loc_y\n", "\n", "colorscale = ['#7A4579', '#D56073', 'rgb(236,158,105)', (1, 1, 0.2), (0.98,0.98,0.98)]\n", "\n", "fig = ff.create_2d_density(\n", "    x, y, colorscale=colorscale,\n", "    hist_color='rgb(255, 237, 222)', point_size=3\n", ")\n", "\n", "iplot(fig, filename='histogram_subplots')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["plot(fig, include_plotlyjs=False, config=dict(displaylogo=False, displayModeBar=False), output_type='div')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["\n", "df = cf.datagen.lines()\n", "\n", "iplot([{\n", "    'x': df.index,\n", "    'y': df[col],\n", "    'name': col\n", "}  for col in df.columns], filename='cufflinks/simple-line')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 2}