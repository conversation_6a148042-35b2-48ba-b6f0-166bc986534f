{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["%matplotlib inline\n", "%load_ext autoreload\n", "%autoreload 2\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "import pandas as pd\n", "from sports.basketball.database.database import Database\n", "from sports.basketball.scrape.nba.scoreboard import Scoreboard\n", "from datetime import date\n", "from tabulate import tabulate\n", "from sports.basketball.stats import basic\n", "db = Database()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["GAME_ID = 41800406 #1021900075"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": ["df = db.query(\"select * from boxscores join players_boxscores_calcs on boxscores.game_id=players_boxscores_calcs.game_id and boxscores.player_id=players_boxscores_calcs.player_id join players on boxscores.player_id = players.player_id where boxscores.game_id={}\".format(GAME_ID))\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["df['min'] = df.apply(basic.minutes_str, axis=1)\n", "df['fg%'] = df.apply(basic.fg_percentage, axis=1)\n", "df['fg'] = df.apply(basic.fg_str, axis=1)\n", "df['3pt%'] = df.apply(basic.fg3_percentage, axis=1)\n", "df['3pt'] = df.apply(basic.fg3_str, axis=1)\n", "df['ft%'] = df.apply(basic.ft_percentage, axis=1)\n", "df['ft'] = df.apply(basic.ft_str, axis=1)\n", "df = df.rename({'plus_minus': '+/-'}, axis=1)\n", "df = df.rename({'start_position': 'start'}, axis=1)\n", "df['name'] = df.apply(basic.first_last_name, axis=1)\n", "df = df.round({'fg%': 2, '3pt%':2, 'ft%': 2})\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["df1, df2 = [x for _, x in df.groupby(df['team_id'])]\n", "ds_1 = df1.sort_values(by=['start', 'seconds'], ascending=False)\n", "ds_1"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["list(ds_1.columns.values)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["box_1 = ds_1[['start', 'name', 'min', '+/-', 'fg', 'fg%', '3pt', '3pt%', 'ft', 'ft%', 'oreb', 'reb', 'ast', 'stl', 'blk', 'to', 'pf', 'pts']]\n", "box_1"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": ["box_a = ds_1[['start', 'name', 'min', 'offensive_rating', 'defensive_rating', 'usage_percentage', 'efg_percentage', 'fg3_attempt_rate', 'ft_attempt_rate', 'oreb_percentage', 'dreb_percentage', 'reb_percentage', 'ast_percentage', 'stl_percentage', 'blk_percentage', 'to_percentage']]\n", "box_a = box_a.rename({'offensive_rating': 'off rtg'}, axis=1)\n", "box_a = box_a.rename({'defensive_rating': 'def rtg'}, axis=1)\n", "box_a = box_a.rename({'efg_percentage': 'eFG%'}, axis=1)\n", "box_a = box_a.rename({'fg3_attempt_rate': '3PAr'}, axis=1)\n", "box_a = box_a.rename({'ft_attempt_rate': 'FTAr'}, axis=1)\n", "box_a = box_a.rename({'oreb_percentage': 'orb%'}, axis=1)\n", "box_a = box_a.rename({'dreb_percentage': 'drb%'}, axis=1)\n", "box_a = box_a.rename({'reb_percentage': 'rb%'}, axis=1)\n", "box_a = box_a.rename({'ast_percentage': 'ast%'}, axis=1)\n", "box_a = box_a.rename({'stl_percentage': 'stl%'}, axis=1)\n", "box_a = box_a.rename({'blk_percentage': 'blk%'}, axis=1)\n", "box_a = box_a.rename({'to_percentage': 'to%'}, axis=1)\n", "box_a = box_a.rename({'usage_percentage': 'usage%'}, axis=1)\n", "\n", "box_a = box_a.round({'off rtg':2, 'def rtg':2, 'usage%':2, 'eFG%': 3, '3PAr':2, 'FTAr': 2, 'orb%': 2, 'drb%':2, 'rb%': 2, 'ast%': 2, 'stl%':2, 'blk%': 2, 'to%': 2})\n", "box_a"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["game_data"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["game_data = db.query(\"select * from games where game_id={}\".format(GAME_ID))\n", "game_date = game_data['game_date_est'].dt.date[0]\n", "home_team_id = game_data['home_team_id'][0]\n", "visitor_team_id = game_data['visitor_team_id'][0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": ["home_team = db.query(\"select * from teams where team_id={}\".format(home_team_id))['abbreviation'][0]\n", "visitor_team = db.query(\"select * from teams where team_id={}\".format(visitor_team_id))['abbreviation'][0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["print(tabulate(box_1, tablefmt=\"github\", headers=\"keys\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["game_date"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["db.query(\"select * from games where league='00' order by game_date_est desc limit 1\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 2}