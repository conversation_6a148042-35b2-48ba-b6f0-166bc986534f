{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["from __future__ import division\n", "\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "from sports.basketball.database.database import Database\n", "from sports.basketball.database.dbtransformer import DbTransformer\n", "from sports.basketball.database.players_boxscores_calcs import PlayersBoxscoresCalcs\n", "from sports.basketball.stats import advanced, basic\n", "from datetime import date\n", "import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["db = Database()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["GAME_ID = '21700312'\n", "PLAYER_ID_DRAYMOND_GREEN = '203110'\n", "PLAYER_ID_ANDRE = '2738'\n", "PLAYER_ID_KEVIN_DURANT = '201142'\n", "TEAM_ID_WARRIORS = '1610612744'\n", "TEAM_ID_LAKERS = '1610612747'\n", "warriors = db.query(\"select * from boxscores where game_id={} and team_id={}\".format(GAME_ID, TEAM_ID_WARRIORS)).sum()\n", "lakers = db.query(\"select * from boxscores where game_id={} and team_id={}\".format(GAME_ID, TEAM_ID_LAKERS)).sum()\n", "draymond = db.query(\"select * from boxscores where game_id={} and player_id={}\".format(GAME_ID, PLAYER_ID_DRAYMOND_GREEN))\n", "durant = db.query(\"select * from boxscores where game_id={} and player_id={}\".format(GAME_ID, PLAYER_ID_KEVIN_DURANT))\n", "andre = db.query(\"select * from boxscores where game_id={} and player_id={}\".format(GAME_ID, PLAYER_ID_ANDRE))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["db.query(\"select * from playbyplays where game_id = 0020300827 order by event_num\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["from requests import get\n", "def _json_to_dataframe(_json, index):\n", "        try:\n", "            headers = _json['resultSets'][index]['headers']\n", "            values = _json['resultSets'][index]['rowSet']\n", "        except KeyError:\n", "            # This is so ugly but this is what you get when your data comes out\n", "            # in not a standard format\n", "            try:\n", "                headers = _json['resultSet'][ndx]['headers']\n", "                values = _json['resultSet'][ndx]['rowSet']\n", "            except KeyError:\n", "                # Added for results that only include one set (ex. LeagueLeaders)\n", "                headers = json_data['resultSet']['headers']\n", "                values = json_data['resultSet']['rowSet']\n", "        headers = [x.lower() for x in headers]\n", "        return pd.DataFrame(values, columns=headers)\n", "    \n", "def fetch(endpoint, params):\n", "        base_url = 'http://stats.nba.com/stats/{endpoint}'\n", "        headers = dict({\n", "            'user-agent': ('unknown'),  # TODO (Issue #4) Randomize user agent.\n", "            'Dnt': ('1'),\n", "            'Accept-Encoding': ('gzip, deflate, sdch'),\n", "            'Accept-Language': ('en'),\n", "            'origin': ('http://stats.nba.com')\n", "        })\n", "\n", "        headers['referer'] = 'http://stats.nba.com/{ref}/'.format(ref='scores')\n", "        _get = get(base_url.format(endpoint=endpoint), params=params,\n", "                   headers=headers)\n", "        _get.raise_for_status()\n", "        return _get.json()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["endpoint = \"shotchartdetail\"\n", "#https://stats.nba.com/stats/shotchartdetail?Period=0&VsConference=&LeagueID=00&LastNGames=0&TeamID=0&Position=&Location=&Outcome=&ContextMeasure=FGA&DateFrom=&StartPeriod=&DateTo=&OpponentTeamID=0&ContextFilter=&RangeType=&Season=2016-17&AheadBehind=&EndRange=&VsDivision=&PointDiff=&RookieYear=&GameSegment=&Month=0&ClutchTime=&StartRange=&EndPeriod=&SeasonType=Regular+Season&SeasonSegment=&GameID=0021600009&PlayerID=201935&CFID=&PlayerPosition=\n", "params = {'PlayerID': 0,\n", "          'TeamID': 0,\n", "          'GameID': '',\n", "          'LeagueID': '00',\n", "           'Season':  '2017-18',\n", "                                      'SeasonType': 'Regular Season',\n", "                                      'Outcome': '',\n", "                                      'Location': '',\n", "                                      'Month': 0,\n", "                                      'SeasonSegment': '',\n", "                                      'DateFrom':  '',\n", "                                      'DateTo': '',\n", "                                      'OpponentTeamID': 0,\n", "                                      'VsConference': '',\n", "                                      'VsDivision': '',\n", "                                      'PlayerPosition': '',\n", "                                      'GameSegment': '',\n", "                                      'Period':  0,\n", "                                      'LastNGames': 0,\n", "                                      'AheadBehind': '',\n", "                                      'ContextMeasure': 'FGM',\n", "                                      'ClutchTime': '',\n", "                                      'RookieYear': ''}\n", "                                      #'CFID':''}\n", "jdata = fetch(endpoint, params)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["len(_json_to_dataframe(jdata, 0))\n", "#list(df)\n", "#df[['homedescription', 'score', 'visitordescription','player1_name', 'player2_id', 'player2_name', 'player3_name']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["df = db.query(\"select * from boxscores join games on games.game_id=boxscores.game_id where player_id=2544 and games.season=2009 and games.season_type='regular' and seconds>0\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["df.sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["db.query(\"update games set season_type = 'all-star' where home_team_id=1610616833\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["db.query(\"select * from players where first_name='<PERSON><PERSON><PERSON>'\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["df = db.query(\"select * from players_boxscores_calcs join boxscores on (boxscores.game_id=players_boxscores_calcs.game_id and boxscores.player_id=players_boxscores_calcs.player_id)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["df2 = df[np.isfinite(df['ts_percentage'])]\n", "df2 = df2.sort_values(by='ts_percentage', ascending=False)\n", "df2 = df2[df.fga>10]\n", "df2[['game_id', 'player_id', 'fga', 'ts_percentage']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["print(db.query(\"select * from players where player_id=1533\"))\n", "print(db.query(\"select * from games where game_id=20800720\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true, "scrolled": true}, "outputs": [], "source": ["db.query(\"SELECT DISTINCT(game_id_string), season FROM games WHERE (NOT EXISTS (SELECT 1 FROM boxscores WHERE boxscores.game_id = games.game_id) OR status!='Final') AND status!='CNCL'\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["db.query(\"select count(distinct(game_id)) from games where game_date_est<'1991-04-10'\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["db.query(\"select* from games where season_type='regular' and season=2018\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["db.query(\"select * from boxscores where game_id=0021800013\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["db.query(\"select * from teams where team_id=1610616833\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["db.query(\"select * from boxscores where boxscores.game_id in (select game_id from games where game_date_est='2000-11-04')\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["db.query(\"select game_id, game_date_est, status from games where (status!='Final' and status!='CNCL' and status NOT LIKE 'Ignore%%')\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["db.query(\"update games set status = 'Ignore Final' where game_id=0039700011\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["db.query(\"select * from games where coalesce(TRIM(status), '') = '' and game_date_est<'2000-01-01'\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["db.query(\"delete from players_boxscores_calcs where game_id=0021800002\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["(u'0039402201', 1994, array([], dtype=object))\n", "(u'0039700011', 1997, array([], dtype=object))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 2}