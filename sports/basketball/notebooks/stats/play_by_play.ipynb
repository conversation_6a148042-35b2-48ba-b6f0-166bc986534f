{"cells": [{"cell_type": "code", "execution_count": 83, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%matplotlib inline\n", "%load_ext autoreload\n", "%autoreload 2\n", "import pandas as pd\n", "from sports.basketball.database.database import Database\n", "from sports.basketball.stats.playbyplay.constants import EventType, PersonType\n", "from sports.basketball.stats import advanced, basic\n", "from datetime import date\n", "import pandas as pd\n", "import numpy as np\n", "db = Database()\n", "\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["db = Database()\n", "GAME_ID = 21700536"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["events = db.query(\"select * from playbyplays where game_id = \" + str(GAME_ID) + \" order by event_num\")\n", "box_score = db.query(\"select * from boxscores join players on players.player_id=boxscores.player_id where game_id = \" + str(GAME_ID))"]}, {"cell_type": "code", "execution_count": 38, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["def final_score(events):\n", "    return str(int(events.home_score.max())) + '-' + str(int(events.visitor_score.max()))\n", "\n", "def score_progression(events):\n", "    return events[['period', 'seconds', 'home_score', 'visitor_score']].dropna()\n", "\n", "def plot_scores_progression(events):\n", "    scores = score_progression(events)[['home_score', 'visitor_score']]\n", "    scores.plot.line(figsize=(10,5))"]}, {"cell_type": "code", "execution_count": 39, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>game_id</th>\n", "      <th>event_num</th>\n", "      <th>event_msg_type</th>\n", "      <th>event_msg_action_type</th>\n", "      <th>period</th>\n", "      <th>seconds</th>\n", "      <th>wctimestring</th>\n", "      <th>home_description</th>\n", "      <th>visitor_description</th>\n", "      <th>home_score</th>\n", "      <th>visitor_score</th>\n", "      <th>person1_type</th>\n", "      <th>player1_id</th>\n", "      <th>person2_type</th>\n", "      <th>player2_id</th>\n", "      <th>person3_type</th>\n", "      <th>player3_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>2</td>\n", "      <td>12</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>720</td>\n", "      <td>9:11 PM</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21700536</td>\n", "      <td>4</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>720</td>\n", "      <td>9:11 PM</td>\n", "      <td><PERSON> vs. <PERSON>: Tip to <PERSON></td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>203486</td>\n", "      <td>5</td>\n", "      <td>101161</td>\n", "      <td>5</td>\n", "      <td>1627732</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>21700536</td>\n", "      <td>7</td>\n", "      <td>1</td>\n", "      <td>80</td>\n", "      <td>1</td>\n", "      <td>706</td>\n", "      <td>9:11 PM</td>\n", "      <td>None</td>\n", "      <td><PERSON><PERSON> 21' Step Back Jump Shot (2 PTS) (Simmon...</td>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>5</td>\n", "      <td>200755</td>\n", "      <td>5</td>\n", "      <td>1627732</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>21700536</td>\n", "      <td>10</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>677</td>\n", "      <td>9:12 PM</td>\n", "      <td>Harris Bad Pass Turnover (P1.T1)</td>\n", "      <td>Covington STEAL (1 STL)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>203914</td>\n", "      <td>5</td>\n", "      <td>203496</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>21700536</td>\n", "      <td>12</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>664</td>\n", "      <td>9:12 PM</td>\n", "      <td>None</td>\n", "      <td><PERSON><PERSON> 17' Running Jump Shot (4 PTS)</td>\n", "      <td>4.0</td>\n", "      <td>0.0</td>\n", "      <td>5</td>\n", "      <td>200755</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>21700536</td>\n", "      <td>13</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>646</td>\n", "      <td>9:12 PM</td>\n", "      <td><PERSON><PERSON> 17' Jump Shot</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>203486</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>21700536</td>\n", "      <td>14</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>643</td>\n", "      <td>9:12 PM</td>\n", "      <td>None</td>\n", "      <td>Covington REBOUND (Off:0 Def:1)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>203496</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>21700536</td>\n", "      <td>15</td>\n", "      <td>1</td>\n", "      <td>73</td>\n", "      <td>1</td>\n", "      <td>638</td>\n", "      <td>9:12 PM</td>\n", "      <td>None</td>\n", "      <td>Saric 3' Driving Reverse Layup (2 PTS)</td>\n", "      <td>6.0</td>\n", "      <td>0.0</td>\n", "      <td>5</td>\n", "      <td>203967</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>21700536</td>\n", "      <td>16</td>\n", "      <td>9</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>638</td>\n", "      <td>9:13 PM</td>\n", "      <td>NUGGETS Timeout: Regular (Full 1 Short 0)</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>1610612743</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>21700536</td>\n", "      <td>17</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>617</td>\n", "      <td>9:16 PM</td>\n", "      <td>Murray 25' 3PT Jump Shot (3 PTS) (Jokic 1 AST)</td>\n", "      <td>None</td>\n", "      <td>6.0</td>\n", "      <td>3.0</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>21700536</td>\n", "      <td>19</td>\n", "      <td>1</td>\n", "      <td>108</td>\n", "      <td>1</td>\n", "      <td>597</td>\n", "      <td>9:16 PM</td>\n", "      <td>None</td>\n", "      <td>Johnson 1' Cutting Dunk Shot (2 PTS) (Redick 1...</td>\n", "      <td>8.0</td>\n", "      <td>3.0</td>\n", "      <td>5</td>\n", "      <td>101161</td>\n", "      <td>5</td>\n", "      <td>200755</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>21700536</td>\n", "      <td>21</td>\n", "      <td>1</td>\n", "      <td>78</td>\n", "      <td>1</td>\n", "      <td>580</td>\n", "      <td>9:16 PM</td>\n", "      <td>Murray 7' Floating Jump Shot (5 PTS) (Jokic 2 ...</td>\n", "      <td>None</td>\n", "      <td>8.0</td>\n", "      <td>5.0</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>21700536</td>\n", "      <td>23</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>564</td>\n", "      <td>9:17 PM</td>\n", "      <td>None</td>\n", "      <td>Simmons Bad Pass Turnover (P1.T1)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>1627732</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>21700536</td>\n", "      <td>24</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>562</td>\n", "      <td>9:17 PM</td>\n", "      <td>None</td>\n", "      <td><PERSON><PERSON>FOUL (P1.T1) (<PERSON><PERSON>)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>200755</td>\n", "      <td>4</td>\n", "      <td>203914</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>21700536</td>\n", "      <td>26</td>\n", "      <td>2</td>\n", "      <td>79</td>\n", "      <td>1</td>\n", "      <td>546</td>\n", "      <td>9:17 PM</td>\n", "      <td>MISS Murray 18' Pullup Jump Shot</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>21700536</td>\n", "      <td>27</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>543</td>\n", "      <td>9:17 PM</td>\n", "      <td>None</td>\n", "      <td><PERSON>ric REBOUND (Off:0 Def:1)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>203967</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>21700536</td>\n", "      <td>28</td>\n", "      <td>1</td>\n", "      <td>98</td>\n", "      <td>1</td>\n", "      <td>537</td>\n", "      <td>9:17 PM</td>\n", "      <td>None</td>\n", "      <td>Covington 3' Cutting Layup Shot (2 PTS) (Saric...</td>\n", "      <td>10.0</td>\n", "      <td>5.0</td>\n", "      <td>5</td>\n", "      <td>203496</td>\n", "      <td>5</td>\n", "      <td>203967</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>21700536</td>\n", "      <td>30</td>\n", "      <td>1</td>\n", "      <td>79</td>\n", "      <td>1</td>\n", "      <td>526</td>\n", "      <td>9:18 PM</td>\n", "      <td>Harris 13' Pullup Jump Shot (2 PTS)</td>\n", "      <td>None</td>\n", "      <td>10.0</td>\n", "      <td>7.0</td>\n", "      <td>4</td>\n", "      <td>203914</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>21700536</td>\n", "      <td>31</td>\n", "      <td>6</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>526</td>\n", "      <td>9:18 PM</td>\n", "      <td>None</td>\n", "      <td><PERSON>ick <PERSON>FOUL (P2.T2) (<PERSON><PERSON>)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>200755</td>\n", "      <td>4</td>\n", "      <td>203914</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>21700536</td>\n", "      <td>33</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>526</td>\n", "      <td>9:18 PM</td>\n", "      <td>None</td>\n", "      <td>SUB: McConnell FOR Redick</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>200755</td>\n", "      <td>5</td>\n", "      <td>204456</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>21700536</td>\n", "      <td>35</td>\n", "      <td>3</td>\n", "      <td>10</td>\n", "      <td>1</td>\n", "      <td>526</td>\n", "      <td>9:18 PM</td>\n", "      <td>Harris Free Throw 1 of 1 (3 PTS)</td>\n", "      <td>None</td>\n", "      <td>10.0</td>\n", "      <td>8.0</td>\n", "      <td>4</td>\n", "      <td>203914</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>21700536</td>\n", "      <td>36</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>509</td>\n", "      <td>9:19 PM</td>\n", "      <td>None</td>\n", "      <td><PERSON><PERSON> 13' Hook Shot</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>1627732</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>21700536</td>\n", "      <td>37</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>506</td>\n", "      <td>9:19 PM</td>\n", "      <td><PERSON> REBOUND (Off:0 Def:1)</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>201163</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>21700536</td>\n", "      <td>38</td>\n", "      <td>1</td>\n", "      <td>101</td>\n", "      <td>1</td>\n", "      <td>491</td>\n", "      <td>9:19 PM</td>\n", "      <td>Murray 7' Driving Floating Jump Shot (7 PTS)</td>\n", "      <td>None</td>\n", "      <td>10.0</td>\n", "      <td>10.0</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>21700536</td>\n", "      <td>39</td>\n", "      <td>9</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>490</td>\n", "      <td>9:19 PM</td>\n", "      <td>None</td>\n", "      <td>76ers Timeout: Regular (Reg.1 Short 0)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>1610612755</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>21700536</td>\n", "      <td>40</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>490</td>\n", "      <td>9:21 PM</td>\n", "      <td>None</td>\n", "      <td>SUB: Bayless FOR Saric</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>203967</td>\n", "      <td>5</td>\n", "      <td>201573</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>21700536</td>\n", "      <td>42</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>481</td>\n", "      <td>9:22 PM</td>\n", "      <td>None</td>\n", "      <td>MISS <PERSON>vington 26' 3PT Jump Shot</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>203496</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>21700536</td>\n", "      <td>43</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>479</td>\n", "      <td>9:22 PM</td>\n", "      <td>None</td>\n", "      <td><PERSON> REBOUND (Off:1 Def:0)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>101161</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>21700536</td>\n", "      <td>44</td>\n", "      <td>1</td>\n", "      <td>79</td>\n", "      <td>1</td>\n", "      <td>476</td>\n", "      <td>9:22 PM</td>\n", "      <td>None</td>\n", "      <td><PERSON><PERSON>C<PERSON> 13' Pullup Jump Shot (2 PTS) (Johns<PERSON>...</td>\n", "      <td>12.0</td>\n", "      <td>10.0</td>\n", "      <td>5</td>\n", "      <td>204456</td>\n", "      <td>5</td>\n", "      <td>101161</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>21700536</td>\n", "      <td>46</td>\n", "      <td>2</td>\n", "      <td>78</td>\n", "      <td>1</td>\n", "      <td>452</td>\n", "      <td>9:22 PM</td>\n", "      <td>MISS <PERSON> 8' Floating Jump Shot</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>203486</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>454</th>\n", "      <td>21700536</td>\n", "      <td>664</td>\n", "      <td>6</td>\n", "      <td>28</td>\n", "      <td>4</td>\n", "      <td>92</td>\n", "      <td>11:17 PM</td>\n", "      <td>Barton Personal Take Foul (P1.T3) (<PERSON><PERSON>)</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>203115</td>\n", "      <td>5</td>\n", "      <td>1627732</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>455</th>\n", "      <td>21700536</td>\n", "      <td>666</td>\n", "      <td>2</td>\n", "      <td>63</td>\n", "      <td>4</td>\n", "      <td>74</td>\n", "      <td>11:18 PM</td>\n", "      <td>None</td>\n", "      <td>MISS Bayless 15' Fadeaway Jumper</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>201573</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>456</th>\n", "      <td>21700536</td>\n", "      <td>667</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>71</td>\n", "      <td>11:18 PM</td>\n", "      <td><PERSON><PERSON> REBOUND (Off:1 Def:7)</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>1626168</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>457</th>\n", "      <td>21700536</td>\n", "      <td>668</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>61</td>\n", "      <td>11:18 PM</td>\n", "      <td>None</td>\n", "      <td><PERSON>.FOUL (P4.PN) (<PERSON><PERSON>)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>1627732</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>458</th>\n", "      <td>21700536</td>\n", "      <td>670</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>4</td>\n", "      <td>61</td>\n", "      <td>11:18 PM</td>\n", "      <td>Jokic Free Throw 1 of 2 (18 PTS)</td>\n", "      <td>None</td>\n", "      <td>103.0</td>\n", "      <td>99.0</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>459</th>\n", "      <td>21700536</td>\n", "      <td>671</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>4</td>\n", "      <td>61</td>\n", "      <td>11:19 PM</td>\n", "      <td>Jokic Free Throw 2 of 2 (19 PTS)</td>\n", "      <td>None</td>\n", "      <td>103.0</td>\n", "      <td>100.0</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>460</th>\n", "      <td>21700536</td>\n", "      <td>672</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>4</td>\n", "      <td>36</td>\n", "      <td>11:19 PM</td>\n", "      <td>Lyle<PERSON> BLOCK (1 BLK)</td>\n", "      <td>MISS <PERSON> 6' Running Jump Shot</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>201573</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>1626168</td>\n", "    </tr>\n", "    <tr>\n", "      <th>461</th>\n", "      <td>21700536</td>\n", "      <td>674</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>34</td>\n", "      <td>11:19 PM</td>\n", "      <td><PERSON> REBOUND (Off:1 Def:7)</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>462</th>\n", "      <td>21700536</td>\n", "      <td>675</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>25</td>\n", "      <td>11:19 PM</td>\n", "      <td>MI<PERSON> 27' 3PT Jump Shot</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>203115</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>463</th>\n", "      <td>21700536</td>\n", "      <td>676</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>21</td>\n", "      <td>11:19 PM</td>\n", "      <td>None</td>\n", "      <td><PERSON><PERSON> REBOUND (Off:2 Def:7)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>203967</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>464</th>\n", "      <td>21700536</td>\n", "      <td>677</td>\n", "      <td>9</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>21</td>\n", "      <td>11:19 PM</td>\n", "      <td>None</td>\n", "      <td>76ers Timeout: Regular (Reg.6 Short 0)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>1610612755</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>465</th>\n", "      <td>21700536</td>\n", "      <td>678</td>\n", "      <td>9</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>21</td>\n", "      <td>11:21 PM</td>\n", "      <td>None</td>\n", "      <td>76ers Timeout: Regular (Reg.7 Short 0)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>1610612755</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>466</th>\n", "      <td>21700536</td>\n", "      <td>679</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>21</td>\n", "      <td>11:22 PM</td>\n", "      <td>SUB: Chandler FOR Jokic</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>4</td>\n", "      <td>201163</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>467</th>\n", "      <td>21700536</td>\n", "      <td>680</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>21</td>\n", "      <td>11:22 PM</td>\n", "      <td>SUB: <PERSON></td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>4</td>\n", "      <td>1628470</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>468</th>\n", "      <td>21700536</td>\n", "      <td>683</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>19</td>\n", "      <td>11:22 PM</td>\n", "      <td><PERSON>.FOUL (P3.PN) (<PERSON><PERSON>)</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>201163</td>\n", "      <td>5</td>\n", "      <td>203496</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>469</th>\n", "      <td>21700536</td>\n", "      <td>685</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>4</td>\n", "      <td>19</td>\n", "      <td>11:23 PM</td>\n", "      <td>None</td>\n", "      <td>Covington Free Throw 1 of 2 (12 PTS)</td>\n", "      <td>104.0</td>\n", "      <td>100.0</td>\n", "      <td>5</td>\n", "      <td>203496</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>470</th>\n", "      <td>21700536</td>\n", "      <td>686</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>19</td>\n", "      <td>11:23 PM</td>\n", "      <td>SUB: Jokic FOR Craig</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>1628470</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>471</th>\n", "      <td>21700536</td>\n", "      <td>688</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>19</td>\n", "      <td>11:23 PM</td>\n", "      <td>None</td>\n", "      <td>SUB: McConnell FOR Saric</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>203967</td>\n", "      <td>5</td>\n", "      <td>204456</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>472</th>\n", "      <td>21700536</td>\n", "      <td>690</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>4</td>\n", "      <td>19</td>\n", "      <td>11:24 PM</td>\n", "      <td>None</td>\n", "      <td>Covington Free Throw 2 of 2 (13 PTS)</td>\n", "      <td>105.0</td>\n", "      <td>100.0</td>\n", "      <td>5</td>\n", "      <td>203496</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>473</th>\n", "      <td>21700536</td>\n", "      <td>691</td>\n", "      <td>9</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>19</td>\n", "      <td>11:24 PM</td>\n", "      <td>NUGGETS Timeout: Regular (Full 6 Short 0)</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>1610612743</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>474</th>\n", "      <td>21700536</td>\n", "      <td>692</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>19</td>\n", "      <td>11:24 PM</td>\n", "      <td>SUB: <PERSON> FOR Chandler</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>201163</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>475</th>\n", "      <td>21700536</td>\n", "      <td>693</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>19</td>\n", "      <td>11:24 PM</td>\n", "      <td>None</td>\n", "      <td>SUB: Saric FOR McConnell</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>204456</td>\n", "      <td>5</td>\n", "      <td>203967</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>476</th>\n", "      <td>21700536</td>\n", "      <td>696</td>\n", "      <td>6</td>\n", "      <td>2</td>\n", "      <td>4</td>\n", "      <td>11</td>\n", "      <td>11:25 PM</td>\n", "      <td>None</td>\n", "      <td><PERSON>FOUL (P5.PN) (<PERSON><PERSON>)</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>1627732</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>477</th>\n", "      <td>21700536</td>\n", "      <td>698</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>4</td>\n", "      <td>11</td>\n", "      <td>11:26 PM</td>\n", "      <td>Murray Free Throw 1 of 2 (30 PTS)</td>\n", "      <td>None</td>\n", "      <td>105.0</td>\n", "      <td>101.0</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>478</th>\n", "      <td>21700536</td>\n", "      <td>699</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>11</td>\n", "      <td>11:26 PM</td>\n", "      <td>SUB: Chandler FOR Jokic</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>4</td>\n", "      <td>201163</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>479</th>\n", "      <td>21700536</td>\n", "      <td>701</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>4</td>\n", "      <td>11</td>\n", "      <td>11:26 PM</td>\n", "      <td>Murray Free Throw 2 of 2 (31 PTS)</td>\n", "      <td>None</td>\n", "      <td>105.0</td>\n", "      <td>102.0</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>480</th>\n", "      <td>21700536</td>\n", "      <td>702</td>\n", "      <td>1</td>\n", "      <td>50</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>11:26 PM</td>\n", "      <td>None</td>\n", "      <td>Covington 2' Running Dunk (15 PTS) (Saric 6 AST)</td>\n", "      <td>107.0</td>\n", "      <td>102.0</td>\n", "      <td>5</td>\n", "      <td>203496</td>\n", "      <td>5</td>\n", "      <td>203967</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>481</th>\n", "      <td>21700536</td>\n", "      <td>704</td>\n", "      <td>2</td>\n", "      <td>79</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>11:26 PM</td>\n", "      <td>MISS <PERSON> 35' 3PT Pullup Jump Shot</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>482</th>\n", "      <td>21700536</td>\n", "      <td>705</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>11:27 PM</td>\n", "      <td>NUGGETS Rebound</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>1610612743</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>483</th>\n", "      <td>21700536</td>\n", "      <td>706</td>\n", "      <td>13</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>11:29 PM</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>107.0</td>\n", "      <td>102.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>484 rows × 17 columns</p>\n", "</div>"], "text/plain": ["      game_id  event_num  event_msg_type  event_msg_action_type  period  \\\n", "0    21700536          2              12                      0       1   \n", "1    21700536          4              10                      0       1   \n", "2    21700536          7               1                     80       1   \n", "3    21700536         10               5                      1       1   \n", "4    21700536         12               1                      2       1   \n", "5    21700536         13               2                      1       1   \n", "6    21700536         14               4                      0       1   \n", "7    21700536         15               1                     73       1   \n", "8    21700536         16               9                      1       1   \n", "9    21700536         17               1                      1       1   \n", "10   21700536         19               1                    108       1   \n", "11   21700536         21               1                     78       1   \n", "12   21700536         23               5                      1       1   \n", "13   21700536         24               6                      1       1   \n", "14   21700536         26               2                     79       1   \n", "15   21700536         27               4                      0       1   \n", "16   21700536         28               1                     98       1   \n", "17   21700536         30               1                     79       1   \n", "18   21700536         31               6                      2       1   \n", "19   21700536         33               8                      0       1   \n", "20   21700536         35               3                     10       1   \n", "21   21700536         36               2                      3       1   \n", "22   21700536         37               4                      0       1   \n", "23   21700536         38               1                    101       1   \n", "24   21700536         39               9                      1       1   \n", "25   21700536         40               8                      0       1   \n", "26   21700536         42               2                      1       1   \n", "27   21700536         43               4                      0       1   \n", "28   21700536         44               1                     79       1   \n", "29   21700536         46               2                     78       1   \n", "..        ...        ...             ...                    ...     ...   \n", "454  21700536        664               6                     28       4   \n", "455  21700536        666               2                     63       4   \n", "456  21700536        667               4                      0       4   \n", "457  21700536        668               6                      1       4   \n", "458  21700536        670               3                     11       4   \n", "459  21700536        671               3                     12       4   \n", "460  21700536        672               2                      2       4   \n", "461  21700536        674               4                      0       4   \n", "462  21700536        675               2                      1       4   \n", "463  21700536        676               4                      0       4   \n", "464  21700536        677               9                      1       4   \n", "465  21700536        678               9                      1       4   \n", "466  21700536        679               8                      0       4   \n", "467  21700536        680               8                      0       4   \n", "468  21700536        683               6                      1       4   \n", "469  21700536        685               3                     11       4   \n", "470  21700536        686               8                      0       4   \n", "471  21700536        688               8                      0       4   \n", "472  21700536        690               3                     12       4   \n", "473  21700536        691               9                      1       4   \n", "474  21700536        692               8                      0       4   \n", "475  21700536        693               8                      0       4   \n", "476  21700536        696               6                      2       4   \n", "477  21700536        698               3                     11       4   \n", "478  21700536        699               8                      0       4   \n", "479  21700536        701               3                     12       4   \n", "480  21700536        702               1                     50       4   \n", "481  21700536        704               2                     79       4   \n", "482  21700536        705               4                      0       4   \n", "483  21700536        706              13                      0       4   \n", "\n", "     seconds wctimestring                                   home_description  \\\n", "0        720      9:11 PM                                               None   \n", "1        720      9:11 PM      Jump <PERSON> vs. <PERSON>: Tip to <PERSON>   \n", "2        706      9:11 PM                                               None   \n", "3        677      9:12 PM                   Harris Bad Pass Turnover (P1.T1)   \n", "4        664      9:12 PM                                               None   \n", "5        646      9:12 PM                         <PERSON><PERSON> 17' Jump Shot   \n", "6        643      9:12 PM                                               None   \n", "7        638      9:12 PM                                               None   \n", "8        638      9:13 PM          NUGGETS Timeout: Regular (Full 1 Short 0)   \n", "9        617      9:16 PM     <PERSON> 25' 3PT Jump Shot (3 PTS) (Jokic 1 AST)   \n", "10       597      9:16 PM                                               None   \n", "11       580      9:16 PM  <PERSON> 7' Floating Jump Shot (5 PTS) (Jokic 2 ...   \n", "12       564      9:17 PM                                               None   \n", "13       562      9:17 PM                                               None   \n", "14       546      9:17 PM                   <PERSON><PERSON> 18' Pullup Jump Shot   \n", "15       543      9:17 PM                                               None   \n", "16       537      9:17 PM                                               None   \n", "17       526      9:18 PM                <PERSON> 13' Pullup Jump Shot (2 PTS)   \n", "18       526      9:18 PM                                               None   \n", "19       526      9:18 PM                                               None   \n", "20       526      9:18 PM                   <PERSON> Throw 1 of 1 (3 PTS)   \n", "21       509      9:19 PM                                               None   \n", "22       506      9:19 PM                     <PERSON> REBOUND (Off:0 Def:1)   \n", "23       491      9:19 PM       <PERSON> 7' Driving Floating Jump Shot (7 PTS)   \n", "24       490      9:19 PM                                               None   \n", "25       490      9:21 PM                                               None   \n", "26       481      9:22 PM                                               None   \n", "27       479      9:22 PM                                               None   \n", "28       476      9:22 PM                                               None   \n", "29       452      9:22 PM                 <PERSON><PERSON> 8' Floating Jump Shot   \n", "..       ...          ...                                                ...   \n", "454       92     11:17 PM    Barton Personal Take Foul (P1.T3) (<PERSON><PERSON>)   \n", "455       74     11:18 PM                                               None   \n", "456       71     11:18 PM                        <PERSON><PERSON> REBOUND (Off:1 Def:7)   \n", "457       61     11:18 PM                                               None   \n", "458       61     11:18 PM                   Jo<PERSON><PERSON> Free Throw 1 of 2 (18 PTS)   \n", "459       61     11:19 PM                   <PERSON><PERSON><PERSON> Free Throw 2 of 2 (19 PTS)   \n", "460       36     11:19 PM                                Lyles BLOCK (1 BLK)   \n", "461       34     11:19 PM                       <PERSON> REBOUND (Off:1 Def:7)   \n", "462       25     11:19 PM                      <PERSON><PERSON> 27' 3PT Jump Shot   \n", "463       21     11:19 PM                                               None   \n", "464       21     11:19 PM                                               None   \n", "465       21     11:21 PM                                               None   \n", "466       21     11:22 PM                            SUB: <PERSON> FOR Jo<PERSON>c   \n", "467       21     11:22 PM                              SUB: <PERSON>   \n", "468       19     11:22 PM                  <PERSON>.FOUL (P3.PN) (<PERSON><PERSON>)   \n", "469       19     11:23 PM                                               None   \n", "470       19     11:23 PM                               SUB: Jo<PERSON>c FOR Craig   \n", "471       19     11:23 PM                                               None   \n", "472       19     11:24 PM                                               None   \n", "473       19     11:24 PM          NUGGETS Timeout: Regular (Full 6 Short 0)   \n", "474       19     11:24 PM                           SUB: <PERSON> FOR Chandler   \n", "475       19     11:24 PM                                               None   \n", "476       11     11:25 PM                                               None   \n", "477       11     11:26 PM                  <PERSON> Throw 1 of 2 (30 PTS)   \n", "478       11     11:26 PM                            SUB: <PERSON> FOR Jo<PERSON>c   \n", "479       11     11:26 PM                  <PERSON> Throw 2 of 2 (31 PTS)   \n", "480        4     11:26 PM                                               None   \n", "481        0     11:26 PM               <PERSON><PERSON> 35' 3PT <PERSON><PERSON><PERSON> Jump Shot   \n", "482        0     11:27 PM                                    NUGGETS Rebound   \n", "483        0     11:29 PM                                               None   \n", "\n", "                                   visitor_description  home_score  \\\n", "0                                                 None         NaN   \n", "1                                                 None         NaN   \n", "2    Redick 21' Step Back Jump Shot (2 PTS) (Simmon...         2.0   \n", "3                              Covington STEAL (1 STL)         NaN   \n", "4                 <PERSON><PERSON> 17' Running Jump Shot (4 PTS)         4.0   \n", "5                                                 None         NaN   \n", "6                      Covington REBOUND (Off:0 Def:1)         NaN   \n", "7               Saric 3' Driving Reverse Layup (2 PTS)         6.0   \n", "8                                                 None         NaN   \n", "9                                                 None         6.0   \n", "10   Johnson 1' <PERSON><PERSON> Dunk Shot (2 PTS) (Redick 1...         8.0   \n", "11                                                None         8.0   \n", "12                   Simmons Bad Pass Turnover (P1.T1)         NaN   \n", "13                     Redick P.FOUL (P1.T1) (<PERSON><PERSON>)         NaN   \n", "14                                                None         NaN   \n", "15                         Saric REBOUND (Off:0 Def:1)         NaN   \n", "16   Covington 3' Cutting Layup Shot (2 PTS) (Saric...        10.0   \n", "17                                                None        10.0   \n", "18                 Redick S.FOUL (P2.T2) (<PERSON><PERSON>)         NaN   \n", "19                           SUB: Mc<PERSON>onnell FOR Redick         NaN   \n", "20                                                None        10.0   \n", "21                          <PERSON><PERSON> 13' <PERSON> <PERSON>   \n", "22                                                None         NaN   \n", "23                                                None        10.0   \n", "24              76ers Timeout: Regular (<PERSON>.1 Short 0)         NaN   \n", "25                              SUB: Bayless FOR Saric         NaN   \n", "26                    MISS Covington 26' 3PT Jump Shot         NaN   \n", "27                       Johnson REBOUND (Off:1 Def:0)         NaN   \n", "28   <PERSON><PERSON><PERSON><PERSON> 13' <PERSON><PERSON><PERSON> Jump Shot (2 PTS) (Johnso...        12.0   \n", "29                                                None         NaN   \n", "..                                                 ...         ...   \n", "454                                               None         NaN   \n", "455                   MISS <PERSON>less 15' Fadeaway Jumper         NaN   \n", "456                                               None         NaN   \n", "457               Simmons P.FOUL (P4.PN) (<PERSON><PERSON>)         NaN   \n", "458                                               None       103.0   \n", "459                                               None       103.0   \n", "460                  MISS <PERSON> 6' Running Jump Shot         NaN   \n", "461                                               None         NaN   \n", "462                                               None         NaN   \n", "463                        Saric REBOUND (Off:2 Def:7)         NaN   \n", "464             76ers Timeout: Regular (<PERSON>.6 Short 0)         NaN   \n", "465             76ers Timeout: Regular (<PERSON>.7 Short 0)         NaN   \n", "466                                               None         NaN   \n", "467                                               None         NaN   \n", "468                                               None         NaN   \n", "469               Covington Free Throw 1 of 2 (12 PTS)       104.0   \n", "470                                               None         NaN   \n", "471                           SUB: McConnell FOR Saric         NaN   \n", "472               Covington Free Throw 2 of 2 (13 PTS)       105.0   \n", "473                                               None         NaN   \n", "474                                               None         NaN   \n", "475                           SUB: Saric FOR McConnell         NaN   \n", "476                   Simmons S.FOUL (P5.PN) (<PERSON><PERSON>)         NaN   \n", "477                                               None       105.0   \n", "478                                               None         NaN   \n", "479                                               None       105.0   \n", "480   Covington 2' Running Dunk (15 PTS) (Saric 6 AST)       107.0   \n", "481                                               None         NaN   \n", "482                                               None         NaN   \n", "483                                               None       107.0   \n", "\n", "     visitor_score  person1_type  player1_id  person2_type  player2_id  \\\n", "0              NaN             0           0             0           0   \n", "1              NaN             4      203486             5      101161   \n", "2              0.0             5      200755             5     1627732   \n", "3              NaN             4      203914             5      203496   \n", "4              0.0             5      200755             0           0   \n", "5              NaN             4      203486             0           0   \n", "6              NaN             5      203496             0           0   \n", "7              0.0             5      203967             0           0   \n", "8              NaN             2  1610612743             0           0   \n", "9              3.0             4     1627750             4      203999   \n", "10             3.0             5      101161             5      200755   \n", "11             5.0             4     1627750             4      203999   \n", "12             NaN             5     1627732             0           0   \n", "13             NaN             5      200755             4      203914   \n", "14             NaN             4     1627750             0           0   \n", "15             NaN             5      203967             0           0   \n", "16             5.0             5      203496             5      203967   \n", "17             7.0             4      203914             0           0   \n", "18             NaN             5      200755             4      203914   \n", "19             NaN             5      200755             5      204456   \n", "20             8.0             4      203914             0           0   \n", "21             NaN             5     1627732             0           0   \n", "22             NaN             4      201163             0           0   \n", "23            10.0             4     1627750             0           0   \n", "24             NaN             3  1610612755             0           0   \n", "25             NaN             5      203967             5      201573   \n", "26             NaN             5      203496             0           0   \n", "27             NaN             5      101161             0           0   \n", "28            10.0             5      204456             5      101161   \n", "29             NaN             4      203486             0           0   \n", "..             ...           ...         ...           ...         ...   \n", "454            NaN             4      203115             5     1627732   \n", "455            NaN             5      201573             0           0   \n", "456            NaN             4     1626168             0           0   \n", "457            NaN             5     1627732             4      203999   \n", "458           99.0             4      203999             0           0   \n", "459          100.0             4      203999             0           0   \n", "460            NaN             5      201573             0           0   \n", "461            NaN             4     1627750             0           0   \n", "462            NaN             4      203115             0           0   \n", "463            NaN             5      203967             0           0   \n", "464            NaN             3  1610612755             0           0   \n", "465            NaN             3  1610612755             0           0   \n", "466            NaN             4      203999             4      201163   \n", "467            NaN             4     1627750             4     1628470   \n", "468            NaN             4      201163             5      203496   \n", "469          100.0             5      203496             0           0   \n", "470            NaN             4     1628470             4      203999   \n", "471            NaN             5      203967             5      204456   \n", "472          100.0             5      203496             0           0   \n", "473            NaN             2  1610612743             0           0   \n", "474            NaN             4      201163             4     1627750   \n", "475            NaN             5      204456             5      203967   \n", "476            NaN             5     1627732             4     1627750   \n", "477          101.0             4     1627750             0           0   \n", "478            NaN             4      203999             4      201163   \n", "479          102.0             4     1627750             0           0   \n", "480          102.0             5      203496             5      203967   \n", "481            NaN             4     1627750             0           0   \n", "482            NaN             2  1610612743             0           0   \n", "483          102.0             0           0             0           0   \n", "\n", "     person3_type  player3_id  \n", "0               0           0  \n", "1               5     1627732  \n", "2               0           0  \n", "3               0           0  \n", "4               0           0  \n", "5               0           0  \n", "6               0           0  \n", "7               0           0  \n", "8               0           0  \n", "9               0           0  \n", "10              0           0  \n", "11              0           0  \n", "12              0           0  \n", "13              1           0  \n", "14              0           0  \n", "15              0           0  \n", "16              0           0  \n", "17              0           0  \n", "18              1           0  \n", "19              0           0  \n", "20              0           0  \n", "21              0           0  \n", "22              0           0  \n", "23              0           0  \n", "24              0           0  \n", "25              0           0  \n", "26              0           0  \n", "27              0           0  \n", "28              0           0  \n", "29              0           0  \n", "..            ...         ...  \n", "454             1           0  \n", "455             0           0  \n", "456             0           0  \n", "457             1           0  \n", "458             0           0  \n", "459             0           0  \n", "460             4     1626168  \n", "461             0           0  \n", "462             0           0  \n", "463             0           0  \n", "464             0           0  \n", "465             0           0  \n", "466             0           0  \n", "467             0           0  \n", "468             1           0  \n", "469             0           0  \n", "470             0           0  \n", "471             0           0  \n", "472             0           0  \n", "473             0           0  \n", "474             0           0  \n", "475             0           0  \n", "476             1           0  \n", "477             0           0  \n", "478             0           0  \n", "479             0           0  \n", "480             0           0  \n", "481             0           0  \n", "482             0           0  \n", "483             0           0  \n", "\n", "[484 rows x 17 columns]"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["events"]}, {"cell_type": "code", "execution_count": 40, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>player1_id</th>\n", "      <th>made</th>\n", "      <th>miss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>101161</td>\n", "      <td>4</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>200755</td>\n", "      <td>8</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>201163</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>201573</td>\n", "      <td>6</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>202344</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>203115</td>\n", "      <td>5</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>203496</td>\n", "      <td>6</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>203914</td>\n", "      <td>7</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>203967</td>\n", "      <td>6</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>203999</td>\n", "      <td>5</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>204456</td>\n", "      <td>5</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>1626158</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>1626168</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>1627732</td>\n", "      <td>3</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>1627750</td>\n", "      <td>11</td>\n", "      <td>12</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    player1_id  made  miss\n", "0       101161     4     3\n", "1       200755     8     7\n", "2       201163     1     2\n", "3       201573     6     4\n", "4       202344     1     2\n", "5       203115     5    10\n", "6       203496     6     5\n", "7       203914     7     7\n", "8       203967     6    10\n", "9       203999     5     9\n", "10      204456     5     4\n", "11     1626158     6     6\n", "12     1626168     6     6\n", "13     1627732     3     6\n", "14     1627750    11    12"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["made_shots = events[(events.event_msg_type==EventType.SHOT_MADE.value)].groupby('player1_id').size().reset_index(name='made')\n", "missed_shots = events[(events.event_msg_type==EventType.SHOT_MISS.value)].groupby('player1_id').size().reset_index(name='miss')\n", "pd.merge(made_shots, missed_shots, how='left', on='player1_id')"]}, {"cell_type": "code", "execution_count": 41, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>game_id</th>\n", "      <th>event_num</th>\n", "      <th>event_msg_type</th>\n", "      <th>event_msg_action_type</th>\n", "      <th>period</th>\n", "      <th>seconds</th>\n", "      <th>wctimestring</th>\n", "      <th>home_description</th>\n", "      <th>visitor_description</th>\n", "      <th>home_score</th>\n", "      <th>visitor_score</th>\n", "      <th>person1_type</th>\n", "      <th>player1_id</th>\n", "      <th>person2_type</th>\n", "      <th>player2_id</th>\n", "      <th>person3_type</th>\n", "      <th>player3_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>21700536</td>\n", "      <td>35</td>\n", "      <td>3</td>\n", "      <td>10</td>\n", "      <td>1</td>\n", "      <td>526</td>\n", "      <td>9:18 PM</td>\n", "      <td>Harris Free Throw 1 of 1 (3 PTS)</td>\n", "      <td>None</td>\n", "      <td>10.0</td>\n", "      <td>8.0</td>\n", "      <td>4</td>\n", "      <td>203914</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>21700536</td>\n", "      <td>67</td>\n", "      <td>3</td>\n", "      <td>10</td>\n", "      <td>1</td>\n", "      <td>359</td>\n", "      <td>9:25 PM</td>\n", "      <td>Murray Free Throw 1 of 1 (10 PTS)</td>\n", "      <td>None</td>\n", "      <td>16.0</td>\n", "      <td>15.0</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>21700536</td>\n", "      <td>73</td>\n", "      <td>3</td>\n", "      <td>13</td>\n", "      <td>1</td>\n", "      <td>312</td>\n", "      <td>9:26 PM</td>\n", "      <td>None</td>\n", "      <td>Saric Free Throw 1 of 3 (3 PTS)</td>\n", "      <td>19.0</td>\n", "      <td>15.0</td>\n", "      <td>5</td>\n", "      <td>203967</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>21700536</td>\n", "      <td>74</td>\n", "      <td>3</td>\n", "      <td>14</td>\n", "      <td>1</td>\n", "      <td>312</td>\n", "      <td>9:26 PM</td>\n", "      <td>None</td>\n", "      <td>Saric Free Throw 2 of 3 (4 PTS)</td>\n", "      <td>20.0</td>\n", "      <td>15.0</td>\n", "      <td>5</td>\n", "      <td>203967</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>21700536</td>\n", "      <td>77</td>\n", "      <td>3</td>\n", "      <td>15</td>\n", "      <td>1</td>\n", "      <td>312</td>\n", "      <td>9:27 PM</td>\n", "      <td>None</td>\n", "      <td>Saric Free Throw 3 of 3 (5 PTS)</td>\n", "      <td>21.0</td>\n", "      <td>15.0</td>\n", "      <td>5</td>\n", "      <td>203967</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61</th>\n", "      <td>21700536</td>\n", "      <td>92</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>1</td>\n", "      <td>244</td>\n", "      <td>9:29 PM</td>\n", "      <td>Barton Free Throw 1 of 2 (1 PTS)</td>\n", "      <td>None</td>\n", "      <td>21.0</td>\n", "      <td>18.0</td>\n", "      <td>4</td>\n", "      <td>203115</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64</th>\n", "      <td>21700536</td>\n", "      <td>97</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>1</td>\n", "      <td>244</td>\n", "      <td>9:29 PM</td>\n", "      <td>Barton Free Throw 2 of 2 (2 PTS)</td>\n", "      <td>None</td>\n", "      <td>21.0</td>\n", "      <td>19.0</td>\n", "      <td>4</td>\n", "      <td>203115</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73</th>\n", "      <td>21700536</td>\n", "      <td>112</td>\n", "      <td>3</td>\n", "      <td>10</td>\n", "      <td>1</td>\n", "      <td>206</td>\n", "      <td>9:30 PM</td>\n", "      <td>Harris Free Throw 1 of 1 (10 PTS)</td>\n", "      <td>None</td>\n", "      <td>23.0</td>\n", "      <td>22.0</td>\n", "      <td>4</td>\n", "      <td>203914</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>80</th>\n", "      <td>21700536</td>\n", "      <td>120</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>1</td>\n", "      <td>161</td>\n", "      <td>9:31 PM</td>\n", "      <td>None</td>\n", "      <td>Holmes Free Throw 1 of 2 (1 PTS)</td>\n", "      <td>24.0</td>\n", "      <td>24.0</td>\n", "      <td>5</td>\n", "      <td>1626158</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82</th>\n", "      <td>21700536</td>\n", "      <td>123</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>1</td>\n", "      <td>161</td>\n", "      <td>9:32 PM</td>\n", "      <td>None</td>\n", "      <td>Holmes Free Throw 2 of 2 (2 PTS)</td>\n", "      <td>25.0</td>\n", "      <td>24.0</td>\n", "      <td>5</td>\n", "      <td>1626158</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>129</th>\n", "      <td>21700536</td>\n", "      <td>199</td>\n", "      <td>3</td>\n", "      <td>10</td>\n", "      <td>2</td>\n", "      <td>556</td>\n", "      <td>9:45 PM</td>\n", "      <td>Jokic Free Throw 1 of 1 (3 PTS)</td>\n", "      <td>None</td>\n", "      <td>33.0</td>\n", "      <td>40.0</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>150</th>\n", "      <td>21700536</td>\n", "      <td>231</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>2</td>\n", "      <td>370</td>\n", "      <td>9:49 PM</td>\n", "      <td>Barton Free Throw 1 of 2 (10 PTS)</td>\n", "      <td>None</td>\n", "      <td>41.0</td>\n", "      <td>47.0</td>\n", "      <td>4</td>\n", "      <td>203115</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>154</th>\n", "      <td>21700536</td>\n", "      <td>238</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>2</td>\n", "      <td>370</td>\n", "      <td>9:50 PM</td>\n", "      <td>MISS Barton Free Throw 2 of 2</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>203115</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>168</th>\n", "      <td>21700536</td>\n", "      <td>260</td>\n", "      <td>3</td>\n", "      <td>10</td>\n", "      <td>2</td>\n", "      <td>292</td>\n", "      <td>9:54 PM</td>\n", "      <td>None</td>\n", "      <td>MISS Holmes Free Throw 1 of 1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>1626158</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>178</th>\n", "      <td>21700536</td>\n", "      <td>273</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>2</td>\n", "      <td>244</td>\n", "      <td>9:56 PM</td>\n", "      <td>Craig Free Throw 1 of 2 (1 PTS)</td>\n", "      <td>None</td>\n", "      <td>46.0</td>\n", "      <td>52.0</td>\n", "      <td>4</td>\n", "      <td>1628470</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>179</th>\n", "      <td>21700536</td>\n", "      <td>274</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>2</td>\n", "      <td>244</td>\n", "      <td>9:56 PM</td>\n", "      <td><PERSON> Throw 2 of 2 (2 PTS)</td>\n", "      <td>None</td>\n", "      <td>46.0</td>\n", "      <td>53.0</td>\n", "      <td>4</td>\n", "      <td>1628470</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>218</th>\n", "      <td>21700536</td>\n", "      <td>334</td>\n", "      <td>3</td>\n", "      <td>10</td>\n", "      <td>3</td>\n", "      <td>689</td>\n", "      <td>10:22 PM</td>\n", "      <td>Murray Free Throw 1 of 1 (19 PTS)</td>\n", "      <td>None</td>\n", "      <td>58.0</td>\n", "      <td>62.0</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>220</th>\n", "      <td>21700536</td>\n", "      <td>337</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>3</td>\n", "      <td>683</td>\n", "      <td>10:22 PM</td>\n", "      <td>Murray Free Throw 1 of 2 (20 PTS)</td>\n", "      <td>None</td>\n", "      <td>58.0</td>\n", "      <td>63.0</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>221</th>\n", "      <td>21700536</td>\n", "      <td>338</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>3</td>\n", "      <td>683</td>\n", "      <td>10:23 PM</td>\n", "      <td>Murray Free Throw 2 of 2 (21 PTS)</td>\n", "      <td>None</td>\n", "      <td>58.0</td>\n", "      <td>64.0</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>234</th>\n", "      <td>21700536</td>\n", "      <td>353</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>3</td>\n", "      <td>617</td>\n", "      <td>10:25 PM</td>\n", "      <td>Jokic Free Throw 1 of 2 (6 PTS)</td>\n", "      <td>None</td>\n", "      <td>58.0</td>\n", "      <td>65.0</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>235</th>\n", "      <td>21700536</td>\n", "      <td>354</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>3</td>\n", "      <td>617</td>\n", "      <td>10:25 PM</td>\n", "      <td>Jokic Free Throw 2 of 2 (7 PTS)</td>\n", "      <td>None</td>\n", "      <td>58.0</td>\n", "      <td>66.0</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>268</th>\n", "      <td>21700536</td>\n", "      <td>398</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>3</td>\n", "      <td>449</td>\n", "      <td>10:32 PM</td>\n", "      <td>Jokic Free Throw 1 of 2 (8 PTS)</td>\n", "      <td>None</td>\n", "      <td>58.0</td>\n", "      <td>72.0</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>270</th>\n", "      <td>21700536</td>\n", "      <td>401</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>3</td>\n", "      <td>449</td>\n", "      <td>10:32 PM</td>\n", "      <td>MISS Jokic Free Throw 2 of 2</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>273</th>\n", "      <td>21700536</td>\n", "      <td>405</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>3</td>\n", "      <td>436</td>\n", "      <td>10:33 PM</td>\n", "      <td>None</td>\n", "      <td>Saric Free Throw 1 of 2 (12 PTS)</td>\n", "      <td>59.0</td>\n", "      <td>72.0</td>\n", "      <td>5</td>\n", "      <td>203967</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>274</th>\n", "      <td>21700536</td>\n", "      <td>406</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>3</td>\n", "      <td>436</td>\n", "      <td>10:33 PM</td>\n", "      <td>None</td>\n", "      <td><PERSON>ric Free Throw 2 of 2 (13 PTS)</td>\n", "      <td>60.0</td>\n", "      <td>72.0</td>\n", "      <td>5</td>\n", "      <td>203967</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>290</th>\n", "      <td>21700536</td>\n", "      <td>431</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>3</td>\n", "      <td>284</td>\n", "      <td>10:40 PM</td>\n", "      <td>Jokic Free Throw 1 of 2 (9 PTS)</td>\n", "      <td>None</td>\n", "      <td>69.0</td>\n", "      <td>73.0</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>294</th>\n", "      <td>21700536</td>\n", "      <td>438</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>3</td>\n", "      <td>284</td>\n", "      <td>10:40 PM</td>\n", "      <td>Jokic Free Throw 2 of 2 (10 PTS)</td>\n", "      <td>None</td>\n", "      <td>69.0</td>\n", "      <td>74.0</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>308</th>\n", "      <td>21700536</td>\n", "      <td>457</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>3</td>\n", "      <td>161</td>\n", "      <td>10:45 PM</td>\n", "      <td>MISS Plumlee Free Throw 1 of 2</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>203486</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>310</th>\n", "      <td>21700536</td>\n", "      <td>459</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>3</td>\n", "      <td>161</td>\n", "      <td>10:45 PM</td>\n", "      <td>MISS Plumlee Free Throw 2 of 2</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>203486</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>314</th>\n", "      <td>21700536</td>\n", "      <td>464</td>\n", "      <td>3</td>\n", "      <td>10</td>\n", "      <td>3</td>\n", "      <td>134</td>\n", "      <td>10:46 PM</td>\n", "      <td>MISS Plumlee Free Throw 1 of 1</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>203486</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>316</th>\n", "      <td>21700536</td>\n", "      <td>467</td>\n", "      <td>3</td>\n", "      <td>10</td>\n", "      <td>3</td>\n", "      <td>134</td>\n", "      <td>10:46 PM</td>\n", "      <td>MISS Plumlee Free Throw 1 of 1</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>203486</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>332</th>\n", "      <td>21700536</td>\n", "      <td>490</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>3</td>\n", "      <td>26</td>\n", "      <td>10:49 PM</td>\n", "      <td>Plumlee Free Throw 1 of 2 (1 PTS)</td>\n", "      <td>None</td>\n", "      <td>83.0</td>\n", "      <td>77.0</td>\n", "      <td>4</td>\n", "      <td>203486</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>334</th>\n", "      <td>21700536</td>\n", "      <td>493</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>3</td>\n", "      <td>26</td>\n", "      <td>10:50 PM</td>\n", "      <td>MISS Plumlee Free Throw 2 of 2</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>203486</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>377</th>\n", "      <td>21700536</td>\n", "      <td>560</td>\n", "      <td>3</td>\n", "      <td>10</td>\n", "      <td>4</td>\n", "      <td>521</td>\n", "      <td>11:03 PM</td>\n", "      <td>Murray Free Throw 1 of 1 (26 PTS)</td>\n", "      <td>None</td>\n", "      <td>93.0</td>\n", "      <td>87.0</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>395</th>\n", "      <td>21700536</td>\n", "      <td>583</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>4</td>\n", "      <td>418</td>\n", "      <td>11:06 PM</td>\n", "      <td>Murray Free Throw 1 of 2 (27 PTS)</td>\n", "      <td>None</td>\n", "      <td>97.0</td>\n", "      <td>88.0</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>398</th>\n", "      <td>21700536</td>\n", "      <td>588</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>4</td>\n", "      <td>418</td>\n", "      <td>11:06 PM</td>\n", "      <td>MISS Murray Free Throw 2 of 2</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>433</th>\n", "      <td>21700536</td>\n", "      <td>636</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>4</td>\n", "      <td>176</td>\n", "      <td>11:12 PM</td>\n", "      <td>MISS Lyles Free Throw 1 of 2</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>1626168</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>436</th>\n", "      <td>21700536</td>\n", "      <td>640</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>4</td>\n", "      <td>176</td>\n", "      <td>11:13 PM</td>\n", "      <td><PERSON><PERSON> Free Throw 2 of 2 (16 PTS)</td>\n", "      <td>None</td>\n", "      <td>103.0</td>\n", "      <td>98.0</td>\n", "      <td>4</td>\n", "      <td>1626168</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>458</th>\n", "      <td>21700536</td>\n", "      <td>670</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>4</td>\n", "      <td>61</td>\n", "      <td>11:18 PM</td>\n", "      <td>Jokic Free Throw 1 of 2 (18 PTS)</td>\n", "      <td>None</td>\n", "      <td>103.0</td>\n", "      <td>99.0</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>459</th>\n", "      <td>21700536</td>\n", "      <td>671</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>4</td>\n", "      <td>61</td>\n", "      <td>11:19 PM</td>\n", "      <td>Jokic Free Throw 2 of 2 (19 PTS)</td>\n", "      <td>None</td>\n", "      <td>103.0</td>\n", "      <td>100.0</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>469</th>\n", "      <td>21700536</td>\n", "      <td>685</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>4</td>\n", "      <td>19</td>\n", "      <td>11:23 PM</td>\n", "      <td>None</td>\n", "      <td>Covington Free Throw 1 of 2 (12 PTS)</td>\n", "      <td>104.0</td>\n", "      <td>100.0</td>\n", "      <td>5</td>\n", "      <td>203496</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>472</th>\n", "      <td>21700536</td>\n", "      <td>690</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>4</td>\n", "      <td>19</td>\n", "      <td>11:24 PM</td>\n", "      <td>None</td>\n", "      <td>Covington Free Throw 2 of 2 (13 PTS)</td>\n", "      <td>105.0</td>\n", "      <td>100.0</td>\n", "      <td>5</td>\n", "      <td>203496</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>477</th>\n", "      <td>21700536</td>\n", "      <td>698</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>4</td>\n", "      <td>11</td>\n", "      <td>11:26 PM</td>\n", "      <td>Murray Free Throw 1 of 2 (30 PTS)</td>\n", "      <td>None</td>\n", "      <td>105.0</td>\n", "      <td>101.0</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>479</th>\n", "      <td>21700536</td>\n", "      <td>701</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>4</td>\n", "      <td>11</td>\n", "      <td>11:26 PM</td>\n", "      <td>Murray Free Throw 2 of 2 (31 PTS)</td>\n", "      <td>None</td>\n", "      <td>105.0</td>\n", "      <td>102.0</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      game_id  event_num  event_msg_type  event_msg_action_type  period  \\\n", "20   21700536         35               3                     10       1   \n", "42   21700536         67               3                     10       1   \n", "47   21700536         73               3                     13       1   \n", "48   21700536         74               3                     14       1   \n", "50   21700536         77               3                     15       1   \n", "61   21700536         92               3                     11       1   \n", "64   21700536         97               3                     12       1   \n", "73   21700536        112               3                     10       1   \n", "80   21700536        120               3                     11       1   \n", "82   21700536        123               3                     12       1   \n", "129  21700536        199               3                     10       2   \n", "150  21700536        231               3                     11       2   \n", "154  21700536        238               3                     12       2   \n", "168  21700536        260               3                     10       2   \n", "178  21700536        273               3                     11       2   \n", "179  21700536        274               3                     12       2   \n", "218  21700536        334               3                     10       3   \n", "220  21700536        337               3                     11       3   \n", "221  21700536        338               3                     12       3   \n", "234  21700536        353               3                     11       3   \n", "235  21700536        354               3                     12       3   \n", "268  21700536        398               3                     11       3   \n", "270  21700536        401               3                     12       3   \n", "273  21700536        405               3                     11       3   \n", "274  21700536        406               3                     12       3   \n", "290  21700536        431               3                     11       3   \n", "294  21700536        438               3                     12       3   \n", "308  21700536        457               3                     11       3   \n", "310  21700536        459               3                     12       3   \n", "314  21700536        464               3                     10       3   \n", "316  21700536        467               3                     10       3   \n", "332  21700536        490               3                     11       3   \n", "334  21700536        493               3                     12       3   \n", "377  21700536        560               3                     10       4   \n", "395  21700536        583               3                     11       4   \n", "398  21700536        588               3                     12       4   \n", "433  21700536        636               3                     11       4   \n", "436  21700536        640               3                     12       4   \n", "458  21700536        670               3                     11       4   \n", "459  21700536        671               3                     12       4   \n", "469  21700536        685               3                     11       4   \n", "472  21700536        690               3                     12       4   \n", "477  21700536        698               3                     11       4   \n", "479  21700536        701               3                     12       4   \n", "\n", "     seconds wctimestring                   home_description  \\\n", "20       526      9:18 PM   <PERSON> Throw 1 of 1 (3 PTS)   \n", "42       359      9:25 PM  <PERSON> Free Throw 1 of 1 (10 PTS)   \n", "47       312      9:26 PM                               None   \n", "48       312      9:26 PM                               None   \n", "50       312      9:27 PM                               None   \n", "61       244      9:29 PM   <PERSON> Throw 1 of 2 (1 PTS)   \n", "64       244      9:29 PM   <PERSON> Throw 2 of 2 (2 PTS)   \n", "73       206      9:30 PM  <PERSON> Throw 1 of 1 (10 PTS)   \n", "80       161      9:31 PM                               None   \n", "82       161      9:32 PM                               None   \n", "129      556      9:45 PM    Jokic Free Throw 1 of 1 (3 PTS)   \n", "150      370      9:49 PM  <PERSON> Throw 1 of 2 (10 PTS)   \n", "154      370      9:50 PM      <PERSON><PERSON> Free Throw 2 of 2   \n", "168      292      9:54 PM                               None   \n", "178      244      9:56 PM    <PERSON>ow 1 of 2 (1 PTS)   \n", "179      244      9:56 PM    <PERSON> Throw 2 of 2 (2 PTS)   \n", "218      689     10:22 PM  <PERSON> Free Throw 1 of 1 (19 PTS)   \n", "220      683     10:22 PM  <PERSON> Free Throw 1 of 2 (20 PTS)   \n", "221      683     10:23 PM  <PERSON> Throw 2 of 2 (21 PTS)   \n", "234      617     10:25 PM    Jokic Free Throw 1 of 2 (6 PTS)   \n", "235      617     10:25 PM    Jokic Free Throw 2 of 2 (7 PTS)   \n", "268      449     10:32 PM    <PERSON><PERSON><PERSON> Throw 1 of 2 (8 PTS)   \n", "270      449     10:32 PM       <PERSON><PERSON> Free Throw 2 of 2   \n", "273      436     10:33 PM                               None   \n", "274      436     10:33 PM                               None   \n", "290      284     10:40 PM    Jokic Free Throw 1 of 2 (9 PTS)   \n", "294      284     10:40 PM   Jokic Free Throw 2 of 2 (10 PTS)   \n", "308      161     10:45 PM     MISS Plumlee Free Throw 1 of 2   \n", "310      161     10:45 PM     MI<PERSON>lee Free Throw 2 of 2   \n", "314      134     10:46 PM     <PERSON><PERSON>lee Free Throw 1 of 1   \n", "316      134     10:46 PM     <PERSON><PERSON> Throw 1 of 1   \n", "332       26     10:49 PM  Plumlee Free Throw 1 of 2 (1 PTS)   \n", "334       26     10:50 PM     MISS Plumlee Free Throw 2 of 2   \n", "377      521     11:03 PM  <PERSON> Throw 1 of 1 (26 PTS)   \n", "395      418     11:06 PM  <PERSON> Throw 1 of 2 (27 PTS)   \n", "398      418     11:06 PM      <PERSON><PERSON> Throw 2 of 2   \n", "433      176     11:12 PM       MI<PERSON> Lyles Free Throw 1 of 2   \n", "436      176     11:13 PM   <PERSON><PERSON> Free Throw 2 of 2 (16 PTS)   \n", "458       61     11:18 PM   Jo<PERSON><PERSON> Free Throw 1 of 2 (18 PTS)   \n", "459       61     11:19 PM   <PERSON><PERSON><PERSON> Free Throw 2 of 2 (19 PTS)   \n", "469       19     11:23 PM                               None   \n", "472       19     11:24 PM                               None   \n", "477       11     11:26 PM  <PERSON> Throw 1 of 2 (30 PTS)   \n", "479       11     11:26 PM  <PERSON> Throw 2 of 2 (31 PTS)   \n", "\n", "                      visitor_description  home_score  visitor_score  \\\n", "20                                   None        10.0            8.0   \n", "42                                   None        16.0           15.0   \n", "47        Saric Free Throw 1 of 3 (3 PTS)        19.0           15.0   \n", "48        Saric Free Throw 2 of 3 (4 PTS)        20.0           15.0   \n", "50        Saric Free Throw 3 of 3 (5 PTS)        21.0           15.0   \n", "61                                   None        21.0           18.0   \n", "64                                   None        21.0           19.0   \n", "73                                   None        23.0           22.0   \n", "80       Holmes Free Throw 1 of 2 (1 PTS)        24.0           24.0   \n", "82       Holmes Free Throw 2 of 2 (2 PTS)        25.0           24.0   \n", "129                                  None        33.0           40.0   \n", "150                                  None        41.0           47.0   \n", "154                                  None         NaN            NaN   \n", "168         MISS Holmes Free Throw 1 of 1         NaN            NaN   \n", "178                                  None        46.0           52.0   \n", "179                                  None        46.0           53.0   \n", "218                                  None        58.0           62.0   \n", "220                                  None        58.0           63.0   \n", "221                                  None        58.0           64.0   \n", "234                                  None        58.0           65.0   \n", "235                                  None        58.0           66.0   \n", "268                                  None        58.0           72.0   \n", "270                                  None         NaN            NaN   \n", "273      Saric Free Throw 1 of 2 (12 PTS)        59.0           72.0   \n", "274      Saric Free Throw 2 of 2 (13 PTS)        60.0           72.0   \n", "290                                  None        69.0           73.0   \n", "294                                  None        69.0           74.0   \n", "308                                  None         NaN            NaN   \n", "310                                  None         NaN            NaN   \n", "314                                  None         NaN            NaN   \n", "316                                  None         NaN            NaN   \n", "332                                  None        83.0           77.0   \n", "334                                  None         NaN            NaN   \n", "377                                  None        93.0           87.0   \n", "395                                  None        97.0           88.0   \n", "398                                  None         NaN            NaN   \n", "433                                  None         NaN            NaN   \n", "436                                  None       103.0           98.0   \n", "458                                  None       103.0           99.0   \n", "459                                  None       103.0          100.0   \n", "469  Covington Free Throw 1 of 2 (12 PTS)       104.0          100.0   \n", "472  Covington Free Throw 2 of 2 (13 PTS)       105.0          100.0   \n", "477                                  None       105.0          101.0   \n", "479                                  None       105.0          102.0   \n", "\n", "     person1_type  player1_id  person2_type  player2_id  person3_type  \\\n", "20              4      203914             0           0             0   \n", "42              4     1627750             0           0             0   \n", "47              5      203967             0           0             0   \n", "48              5      203967             0           0             0   \n", "50              5      203967             0           0             0   \n", "61              4      203115             0           0             0   \n", "64              4      203115             0           0             0   \n", "73              4      203914             0           0             0   \n", "80              5     1626158             0           0             0   \n", "82              5     1626158             0           0             0   \n", "129             4      203999             0           0             0   \n", "150             4      203115             0           0             0   \n", "154             4      203115             0           0             0   \n", "168             5     1626158             0           0             0   \n", "178             4     1628470             0           0             0   \n", "179             4     1628470             0           0             0   \n", "218             4     1627750             0           0             0   \n", "220             4     1627750             0           0             0   \n", "221             4     1627750             0           0             0   \n", "234             4      203999             0           0             0   \n", "235             4      203999             0           0             0   \n", "268             4      203999             0           0             0   \n", "270             4      203999             0           0             0   \n", "273             5      203967             0           0             0   \n", "274             5      203967             0           0             0   \n", "290             4      203999             0           0             0   \n", "294             4      203999             0           0             0   \n", "308             4      203486             0           0             0   \n", "310             4      203486             0           0             0   \n", "314             4      203486             0           0             0   \n", "316             4      203486             0           0             0   \n", "332             4      203486             0           0             0   \n", "334             4      203486             0           0             0   \n", "377             4     1627750             0           0             0   \n", "395             4     1627750             0           0             0   \n", "398             4     1627750             0           0             0   \n", "433             4     1626168             0           0             0   \n", "436             4     1626168             0           0             0   \n", "458             4      203999             0           0             0   \n", "459             4      203999             0           0             0   \n", "469             5      203496             0           0             0   \n", "472             5      203496             0           0             0   \n", "477             4     1627750             0           0             0   \n", "479             4     1627750             0           0             0   \n", "\n", "     player3_id  \n", "20            0  \n", "42            0  \n", "47            0  \n", "48            0  \n", "50            0  \n", "61            0  \n", "64            0  \n", "73            0  \n", "80            0  \n", "82            0  \n", "129           0  \n", "150           0  \n", "154           0  \n", "168           0  \n", "178           0  \n", "179           0  \n", "218           0  \n", "220           0  \n", "221           0  \n", "234           0  \n", "235           0  \n", "268           0  \n", "270           0  \n", "273           0  \n", "274           0  \n", "290           0  \n", "294           0  \n", "308           0  \n", "310           0  \n", "314           0  \n", "316           0  \n", "332           0  \n", "334           0  \n", "377           0  \n", "395           0  \n", "398           0  \n", "433           0  \n", "436           0  \n", "458           0  \n", "459           0  \n", "469           0  \n", "472           0  \n", "477           0  \n", "479           0  "]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["events[(events.event_msg_type==EventType.FREE_THROW.value)]"]}, {"cell_type": "code", "execution_count": 46, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>index</th>\n", "      <th>game_id</th>\n", "      <th>event_num</th>\n", "      <th>event_msg_type</th>\n", "      <th>event_msg_action_type</th>\n", "      <th>period</th>\n", "      <th>seconds</th>\n", "      <th>wctimestring</th>\n", "      <th>home_description</th>\n", "      <th>visitor_description</th>\n", "      <th>home_score</th>\n", "      <th>visitor_score</th>\n", "      <th>person1_type</th>\n", "      <th>player1_id</th>\n", "      <th>person2_type</th>\n", "      <th>player2_id</th>\n", "      <th>person3_type</th>\n", "      <th>player3_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20</td>\n", "      <td>21700536</td>\n", "      <td>35</td>\n", "      <td>3</td>\n", "      <td>10</td>\n", "      <td>1</td>\n", "      <td>526</td>\n", "      <td>9:18 PM</td>\n", "      <td>Harris Free Throw 1 of 1 (3 PTS)</td>\n", "      <td>None</td>\n", "      <td>10.0</td>\n", "      <td>8.0</td>\n", "      <td>4</td>\n", "      <td>203914</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>42</td>\n", "      <td>21700536</td>\n", "      <td>67</td>\n", "      <td>3</td>\n", "      <td>10</td>\n", "      <td>1</td>\n", "      <td>359</td>\n", "      <td>9:25 PM</td>\n", "      <td>Murray Free Throw 1 of 1 (10 PTS)</td>\n", "      <td>None</td>\n", "      <td>16.0</td>\n", "      <td>15.0</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>47</td>\n", "      <td>21700536</td>\n", "      <td>73</td>\n", "      <td>3</td>\n", "      <td>13</td>\n", "      <td>1</td>\n", "      <td>312</td>\n", "      <td>9:26 PM</td>\n", "      <td>None</td>\n", "      <td>Saric Free Throw 1 of 3 (3 PTS)</td>\n", "      <td>19.0</td>\n", "      <td>15.0</td>\n", "      <td>5</td>\n", "      <td>203967</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>48</td>\n", "      <td>21700536</td>\n", "      <td>74</td>\n", "      <td>3</td>\n", "      <td>14</td>\n", "      <td>1</td>\n", "      <td>312</td>\n", "      <td>9:26 PM</td>\n", "      <td>None</td>\n", "      <td>Saric Free Throw 2 of 3 (4 PTS)</td>\n", "      <td>20.0</td>\n", "      <td>15.0</td>\n", "      <td>5</td>\n", "      <td>203967</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>50</td>\n", "      <td>21700536</td>\n", "      <td>77</td>\n", "      <td>3</td>\n", "      <td>15</td>\n", "      <td>1</td>\n", "      <td>312</td>\n", "      <td>9:27 PM</td>\n", "      <td>None</td>\n", "      <td>Saric Free Throw 3 of 3 (5 PTS)</td>\n", "      <td>21.0</td>\n", "      <td>15.0</td>\n", "      <td>5</td>\n", "      <td>203967</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>61</td>\n", "      <td>21700536</td>\n", "      <td>92</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>1</td>\n", "      <td>244</td>\n", "      <td>9:29 PM</td>\n", "      <td>Barton Free Throw 1 of 2 (1 PTS)</td>\n", "      <td>None</td>\n", "      <td>21.0</td>\n", "      <td>18.0</td>\n", "      <td>4</td>\n", "      <td>203115</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>64</td>\n", "      <td>21700536</td>\n", "      <td>97</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>1</td>\n", "      <td>244</td>\n", "      <td>9:29 PM</td>\n", "      <td>Barton Free Throw 2 of 2 (2 PTS)</td>\n", "      <td>None</td>\n", "      <td>21.0</td>\n", "      <td>19.0</td>\n", "      <td>4</td>\n", "      <td>203115</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>73</td>\n", "      <td>21700536</td>\n", "      <td>112</td>\n", "      <td>3</td>\n", "      <td>10</td>\n", "      <td>1</td>\n", "      <td>206</td>\n", "      <td>9:30 PM</td>\n", "      <td>Harris Free Throw 1 of 1 (10 PTS)</td>\n", "      <td>None</td>\n", "      <td>23.0</td>\n", "      <td>22.0</td>\n", "      <td>4</td>\n", "      <td>203914</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>80</td>\n", "      <td>21700536</td>\n", "      <td>120</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>1</td>\n", "      <td>161</td>\n", "      <td>9:31 PM</td>\n", "      <td>None</td>\n", "      <td>Holmes Free Throw 1 of 2 (1 PTS)</td>\n", "      <td>24.0</td>\n", "      <td>24.0</td>\n", "      <td>5</td>\n", "      <td>1626158</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>82</td>\n", "      <td>21700536</td>\n", "      <td>123</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>1</td>\n", "      <td>161</td>\n", "      <td>9:32 PM</td>\n", "      <td>None</td>\n", "      <td>Holmes Free Throw 2 of 2 (2 PTS)</td>\n", "      <td>25.0</td>\n", "      <td>24.0</td>\n", "      <td>5</td>\n", "      <td>1626158</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>129</td>\n", "      <td>21700536</td>\n", "      <td>199</td>\n", "      <td>3</td>\n", "      <td>10</td>\n", "      <td>2</td>\n", "      <td>556</td>\n", "      <td>9:45 PM</td>\n", "      <td>Jokic Free Throw 1 of 1 (3 PTS)</td>\n", "      <td>None</td>\n", "      <td>33.0</td>\n", "      <td>40.0</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>150</td>\n", "      <td>21700536</td>\n", "      <td>231</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>2</td>\n", "      <td>370</td>\n", "      <td>9:49 PM</td>\n", "      <td>Barton Free Throw 1 of 2 (10 PTS)</td>\n", "      <td>None</td>\n", "      <td>41.0</td>\n", "      <td>47.0</td>\n", "      <td>4</td>\n", "      <td>203115</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>154</td>\n", "      <td>21700536</td>\n", "      <td>238</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>2</td>\n", "      <td>370</td>\n", "      <td>9:50 PM</td>\n", "      <td>MISS Barton Free Throw 2 of 2</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>203115</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>168</td>\n", "      <td>21700536</td>\n", "      <td>260</td>\n", "      <td>3</td>\n", "      <td>10</td>\n", "      <td>2</td>\n", "      <td>292</td>\n", "      <td>9:54 PM</td>\n", "      <td>None</td>\n", "      <td>MISS Holmes Free Throw 1 of 1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>1626158</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>178</td>\n", "      <td>21700536</td>\n", "      <td>273</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>2</td>\n", "      <td>244</td>\n", "      <td>9:56 PM</td>\n", "      <td>Craig Free Throw 1 of 2 (1 PTS)</td>\n", "      <td>None</td>\n", "      <td>46.0</td>\n", "      <td>52.0</td>\n", "      <td>4</td>\n", "      <td>1628470</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>179</td>\n", "      <td>21700536</td>\n", "      <td>274</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>2</td>\n", "      <td>244</td>\n", "      <td>9:56 PM</td>\n", "      <td><PERSON> Throw 2 of 2 (2 PTS)</td>\n", "      <td>None</td>\n", "      <td>46.0</td>\n", "      <td>53.0</td>\n", "      <td>4</td>\n", "      <td>1628470</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>218</td>\n", "      <td>21700536</td>\n", "      <td>334</td>\n", "      <td>3</td>\n", "      <td>10</td>\n", "      <td>3</td>\n", "      <td>689</td>\n", "      <td>10:22 PM</td>\n", "      <td>Murray Free Throw 1 of 1 (19 PTS)</td>\n", "      <td>None</td>\n", "      <td>58.0</td>\n", "      <td>62.0</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>220</td>\n", "      <td>21700536</td>\n", "      <td>337</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>3</td>\n", "      <td>683</td>\n", "      <td>10:22 PM</td>\n", "      <td>Murray Free Throw 1 of 2 (20 PTS)</td>\n", "      <td>None</td>\n", "      <td>58.0</td>\n", "      <td>63.0</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>221</td>\n", "      <td>21700536</td>\n", "      <td>338</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>3</td>\n", "      <td>683</td>\n", "      <td>10:23 PM</td>\n", "      <td>Murray Free Throw 2 of 2 (21 PTS)</td>\n", "      <td>None</td>\n", "      <td>58.0</td>\n", "      <td>64.0</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>234</td>\n", "      <td>21700536</td>\n", "      <td>353</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>3</td>\n", "      <td>617</td>\n", "      <td>10:25 PM</td>\n", "      <td>Jokic Free Throw 1 of 2 (6 PTS)</td>\n", "      <td>None</td>\n", "      <td>58.0</td>\n", "      <td>65.0</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>235</td>\n", "      <td>21700536</td>\n", "      <td>354</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>3</td>\n", "      <td>617</td>\n", "      <td>10:25 PM</td>\n", "      <td>Jokic Free Throw 2 of 2 (7 PTS)</td>\n", "      <td>None</td>\n", "      <td>58.0</td>\n", "      <td>66.0</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>268</td>\n", "      <td>21700536</td>\n", "      <td>398</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>3</td>\n", "      <td>449</td>\n", "      <td>10:32 PM</td>\n", "      <td>Jokic Free Throw 1 of 2 (8 PTS)</td>\n", "      <td>None</td>\n", "      <td>58.0</td>\n", "      <td>72.0</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>270</td>\n", "      <td>21700536</td>\n", "      <td>401</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>3</td>\n", "      <td>449</td>\n", "      <td>10:32 PM</td>\n", "      <td>MISS Jokic Free Throw 2 of 2</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>273</td>\n", "      <td>21700536</td>\n", "      <td>405</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>3</td>\n", "      <td>436</td>\n", "      <td>10:33 PM</td>\n", "      <td>None</td>\n", "      <td>Saric Free Throw 1 of 2 (12 PTS)</td>\n", "      <td>59.0</td>\n", "      <td>72.0</td>\n", "      <td>5</td>\n", "      <td>203967</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>274</td>\n", "      <td>21700536</td>\n", "      <td>406</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>3</td>\n", "      <td>436</td>\n", "      <td>10:33 PM</td>\n", "      <td>None</td>\n", "      <td><PERSON>ric Free Throw 2 of 2 (13 PTS)</td>\n", "      <td>60.0</td>\n", "      <td>72.0</td>\n", "      <td>5</td>\n", "      <td>203967</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>290</td>\n", "      <td>21700536</td>\n", "      <td>431</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>3</td>\n", "      <td>284</td>\n", "      <td>10:40 PM</td>\n", "      <td>Jokic Free Throw 1 of 2 (9 PTS)</td>\n", "      <td>None</td>\n", "      <td>69.0</td>\n", "      <td>73.0</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>294</td>\n", "      <td>21700536</td>\n", "      <td>438</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>3</td>\n", "      <td>284</td>\n", "      <td>10:40 PM</td>\n", "      <td>Jokic Free Throw 2 of 2 (10 PTS)</td>\n", "      <td>None</td>\n", "      <td>69.0</td>\n", "      <td>74.0</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>308</td>\n", "      <td>21700536</td>\n", "      <td>457</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>3</td>\n", "      <td>161</td>\n", "      <td>10:45 PM</td>\n", "      <td>MISS Plumlee Free Throw 1 of 2</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>203486</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>310</td>\n", "      <td>21700536</td>\n", "      <td>459</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>3</td>\n", "      <td>161</td>\n", "      <td>10:45 PM</td>\n", "      <td>MISS Plumlee Free Throw 2 of 2</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>203486</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>314</td>\n", "      <td>21700536</td>\n", "      <td>464</td>\n", "      <td>3</td>\n", "      <td>10</td>\n", "      <td>3</td>\n", "      <td>134</td>\n", "      <td>10:46 PM</td>\n", "      <td>MISS Plumlee Free Throw 1 of 1</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>203486</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>316</td>\n", "      <td>21700536</td>\n", "      <td>467</td>\n", "      <td>3</td>\n", "      <td>10</td>\n", "      <td>3</td>\n", "      <td>134</td>\n", "      <td>10:46 PM</td>\n", "      <td>MISS Plumlee Free Throw 1 of 1</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>203486</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>332</td>\n", "      <td>21700536</td>\n", "      <td>490</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>3</td>\n", "      <td>26</td>\n", "      <td>10:49 PM</td>\n", "      <td>Plumlee Free Throw 1 of 2 (1 PTS)</td>\n", "      <td>None</td>\n", "      <td>83.0</td>\n", "      <td>77.0</td>\n", "      <td>4</td>\n", "      <td>203486</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>334</td>\n", "      <td>21700536</td>\n", "      <td>493</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>3</td>\n", "      <td>26</td>\n", "      <td>10:50 PM</td>\n", "      <td>MISS Plumlee Free Throw 2 of 2</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>203486</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>377</td>\n", "      <td>21700536</td>\n", "      <td>560</td>\n", "      <td>3</td>\n", "      <td>10</td>\n", "      <td>4</td>\n", "      <td>521</td>\n", "      <td>11:03 PM</td>\n", "      <td>Murray Free Throw 1 of 1 (26 PTS)</td>\n", "      <td>None</td>\n", "      <td>93.0</td>\n", "      <td>87.0</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>395</td>\n", "      <td>21700536</td>\n", "      <td>583</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>4</td>\n", "      <td>418</td>\n", "      <td>11:06 PM</td>\n", "      <td>Murray Free Throw 1 of 2 (27 PTS)</td>\n", "      <td>None</td>\n", "      <td>97.0</td>\n", "      <td>88.0</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>398</td>\n", "      <td>21700536</td>\n", "      <td>588</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>4</td>\n", "      <td>418</td>\n", "      <td>11:06 PM</td>\n", "      <td>MISS Murray Free Throw 2 of 2</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>433</td>\n", "      <td>21700536</td>\n", "      <td>636</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>4</td>\n", "      <td>176</td>\n", "      <td>11:12 PM</td>\n", "      <td>MISS Lyles Free Throw 1 of 2</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4</td>\n", "      <td>1626168</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>436</td>\n", "      <td>21700536</td>\n", "      <td>640</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>4</td>\n", "      <td>176</td>\n", "      <td>11:13 PM</td>\n", "      <td><PERSON><PERSON> Free Throw 2 of 2 (16 PTS)</td>\n", "      <td>None</td>\n", "      <td>103.0</td>\n", "      <td>98.0</td>\n", "      <td>4</td>\n", "      <td>1626168</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>458</td>\n", "      <td>21700536</td>\n", "      <td>670</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>4</td>\n", "      <td>61</td>\n", "      <td>11:18 PM</td>\n", "      <td>Jokic Free Throw 1 of 2 (18 PTS)</td>\n", "      <td>None</td>\n", "      <td>103.0</td>\n", "      <td>99.0</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>459</td>\n", "      <td>21700536</td>\n", "      <td>671</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>4</td>\n", "      <td>61</td>\n", "      <td>11:19 PM</td>\n", "      <td>Jokic Free Throw 2 of 2 (19 PTS)</td>\n", "      <td>None</td>\n", "      <td>103.0</td>\n", "      <td>100.0</td>\n", "      <td>4</td>\n", "      <td>203999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>469</td>\n", "      <td>21700536</td>\n", "      <td>685</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>4</td>\n", "      <td>19</td>\n", "      <td>11:23 PM</td>\n", "      <td>None</td>\n", "      <td>Covington Free Throw 1 of 2 (12 PTS)</td>\n", "      <td>104.0</td>\n", "      <td>100.0</td>\n", "      <td>5</td>\n", "      <td>203496</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>472</td>\n", "      <td>21700536</td>\n", "      <td>690</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>4</td>\n", "      <td>19</td>\n", "      <td>11:24 PM</td>\n", "      <td>None</td>\n", "      <td>Covington Free Throw 2 of 2 (13 PTS)</td>\n", "      <td>105.0</td>\n", "      <td>100.0</td>\n", "      <td>5</td>\n", "      <td>203496</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>477</td>\n", "      <td>21700536</td>\n", "      <td>698</td>\n", "      <td>3</td>\n", "      <td>11</td>\n", "      <td>4</td>\n", "      <td>11</td>\n", "      <td>11:26 PM</td>\n", "      <td>Murray Free Throw 1 of 2 (30 PTS)</td>\n", "      <td>None</td>\n", "      <td>105.0</td>\n", "      <td>101.0</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>479</td>\n", "      <td>21700536</td>\n", "      <td>701</td>\n", "      <td>3</td>\n", "      <td>12</td>\n", "      <td>4</td>\n", "      <td>11</td>\n", "      <td>11:26 PM</td>\n", "      <td>Murray Free Throw 2 of 2 (31 PTS)</td>\n", "      <td>None</td>\n", "      <td>105.0</td>\n", "      <td>102.0</td>\n", "      <td>4</td>\n", "      <td>1627750</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    index   game_id  event_num  event_msg_type  event_msg_action_type  period  \\\n", "0      20  21700536         35               3                     10       1   \n", "1      42  21700536         67               3                     10       1   \n", "2      47  21700536         73               3                     13       1   \n", "3      48  21700536         74               3                     14       1   \n", "4      50  21700536         77               3                     15       1   \n", "5      61  21700536         92               3                     11       1   \n", "6      64  21700536         97               3                     12       1   \n", "7      73  21700536        112               3                     10       1   \n", "8      80  21700536        120               3                     11       1   \n", "9      82  21700536        123               3                     12       1   \n", "10    129  21700536        199               3                     10       2   \n", "11    150  21700536        231               3                     11       2   \n", "12    154  21700536        238               3                     12       2   \n", "13    168  21700536        260               3                     10       2   \n", "14    178  21700536        273               3                     11       2   \n", "15    179  21700536        274               3                     12       2   \n", "16    218  21700536        334               3                     10       3   \n", "17    220  21700536        337               3                     11       3   \n", "18    221  21700536        338               3                     12       3   \n", "19    234  21700536        353               3                     11       3   \n", "20    235  21700536        354               3                     12       3   \n", "21    268  21700536        398               3                     11       3   \n", "22    270  21700536        401               3                     12       3   \n", "23    273  21700536        405               3                     11       3   \n", "24    274  21700536        406               3                     12       3   \n", "25    290  21700536        431               3                     11       3   \n", "26    294  21700536        438               3                     12       3   \n", "27    308  21700536        457               3                     11       3   \n", "28    310  21700536        459               3                     12       3   \n", "29    314  21700536        464               3                     10       3   \n", "30    316  21700536        467               3                     10       3   \n", "31    332  21700536        490               3                     11       3   \n", "32    334  21700536        493               3                     12       3   \n", "33    377  21700536        560               3                     10       4   \n", "34    395  21700536        583               3                     11       4   \n", "35    398  21700536        588               3                     12       4   \n", "36    433  21700536        636               3                     11       4   \n", "37    436  21700536        640               3                     12       4   \n", "38    458  21700536        670               3                     11       4   \n", "39    459  21700536        671               3                     12       4   \n", "40    469  21700536        685               3                     11       4   \n", "41    472  21700536        690               3                     12       4   \n", "42    477  21700536        698               3                     11       4   \n", "43    479  21700536        701               3                     12       4   \n", "\n", "    seconds wctimestring                   home_description  \\\n", "0       526      9:18 PM   <PERSON> Throw 1 of 1 (3 PTS)   \n", "1       359      9:25 PM  <PERSON> Free Throw 1 of 1 (10 PTS)   \n", "2       312      9:26 PM                               None   \n", "3       312      9:26 PM                               None   \n", "4       312      9:27 PM                               None   \n", "5       244      9:29 PM   <PERSON> Throw 1 of 2 (1 PTS)   \n", "6       244      9:29 PM   <PERSON> Throw 2 of 2 (2 PTS)   \n", "7       206      9:30 PM  <PERSON> Throw 1 of 1 (10 PTS)   \n", "8       161      9:31 PM                               None   \n", "9       161      9:32 PM                               None   \n", "10      556      9:45 PM    Jokic Free Throw 1 of 1 (3 PTS)   \n", "11      370      9:49 PM  <PERSON> Throw 1 of 2 (10 PTS)   \n", "12      370      9:50 PM      <PERSON><PERSON> Free Throw 2 of 2   \n", "13      292      9:54 PM                               None   \n", "14      244      9:56 PM    <PERSON>ow 1 of 2 (1 PTS)   \n", "15      244      9:56 PM    <PERSON>ow 2 of 2 (2 PTS)   \n", "16      689     10:22 PM  <PERSON> Throw 1 of 1 (19 PTS)   \n", "17      683     10:22 PM  <PERSON> Throw 1 of 2 (20 PTS)   \n", "18      683     10:23 PM  <PERSON> Throw 2 of 2 (21 PTS)   \n", "19      617     10:25 PM    Jokic Free Throw 1 of 2 (6 PTS)   \n", "20      617     10:25 PM    Jokic Free Throw 2 of 2 (7 PTS)   \n", "21      449     10:32 PM    <PERSON><PERSON><PERSON> Free Throw 1 of 2 (8 PTS)   \n", "22      449     10:32 PM       <PERSON><PERSON> Throw 2 of 2   \n", "23      436     10:33 PM                               None   \n", "24      436     10:33 PM                               None   \n", "25      284     10:40 PM    Jokic Free Throw 1 of 2 (9 PTS)   \n", "26      284     10:40 PM   Jokic Free Throw 2 of 2 (10 PTS)   \n", "27      161     10:45 PM     MI<PERSON>lee Free Throw 1 of 2   \n", "28      161     10:45 PM     <PERSON><PERSON> Free Throw 2 of 2   \n", "29      134     10:46 PM     <PERSON><PERSON> Throw 1 of 1   \n", "30      134     10:46 PM     <PERSON><PERSON> Free Throw 1 of 1   \n", "31       26     10:49 PM  Plumlee Free Throw 1 of 2 (1 PTS)   \n", "32       26     10:50 PM     MI<PERSON>lee Free Throw 2 of 2   \n", "33      521     11:03 PM  <PERSON> Throw 1 of 1 (26 PTS)   \n", "34      418     11:06 PM  <PERSON> Throw 1 of 2 (27 PTS)   \n", "35      418     11:06 PM      <PERSON><PERSON> Free Throw 2 of 2   \n", "36      176     11:12 PM       <PERSON><PERSON> Free Throw 1 of 2   \n", "37      176     11:13 PM   <PERSON><PERSON> Free Throw 2 of 2 (16 PTS)   \n", "38       61     11:18 PM   <PERSON><PERSON><PERSON> Free Throw 1 of 2 (18 PTS)   \n", "39       61     11:19 PM   <PERSON><PERSON><PERSON> Throw 2 of 2 (19 PTS)   \n", "40       19     11:23 PM                               None   \n", "41       19     11:24 PM                               None   \n", "42       11     11:26 PM  <PERSON> Throw 1 of 2 (30 PTS)   \n", "43       11     11:26 PM  <PERSON> Throw 2 of 2 (31 PTS)   \n", "\n", "                     visitor_description  home_score  visitor_score  \\\n", "0                                   None        10.0            8.0   \n", "1                                   None        16.0           15.0   \n", "2        Saric Free Throw 1 of 3 (3 PTS)        19.0           15.0   \n", "3        Saric Free Throw 2 of 3 (4 PTS)        20.0           15.0   \n", "4        Saric Free Throw 3 of 3 (5 PTS)        21.0           15.0   \n", "5                                   None        21.0           18.0   \n", "6                                   None        21.0           19.0   \n", "7                                   None        23.0           22.0   \n", "8       Holmes Free Throw 1 of 2 (1 PTS)        24.0           24.0   \n", "9       Holmes Free Throw 2 of 2 (2 PTS)        25.0           24.0   \n", "10                                  None        33.0           40.0   \n", "11                                  None        41.0           47.0   \n", "12                                  None         NaN            NaN   \n", "13         MISS Holmes Free Throw 1 of 1         NaN            NaN   \n", "14                                  None        46.0           52.0   \n", "15                                  None        46.0           53.0   \n", "16                                  None        58.0           62.0   \n", "17                                  None        58.0           63.0   \n", "18                                  None        58.0           64.0   \n", "19                                  None        58.0           65.0   \n", "20                                  None        58.0           66.0   \n", "21                                  None        58.0           72.0   \n", "22                                  None         NaN            NaN   \n", "23      Saric Free Throw 1 of 2 (12 PTS)        59.0           72.0   \n", "24      Saric Free Throw 2 of 2 (13 PTS)        60.0           72.0   \n", "25                                  None        69.0           73.0   \n", "26                                  None        69.0           74.0   \n", "27                                  None         NaN            NaN   \n", "28                                  None         NaN            NaN   \n", "29                                  None         NaN            NaN   \n", "30                                  None         NaN            NaN   \n", "31                                  None        83.0           77.0   \n", "32                                  None         NaN            NaN   \n", "33                                  None        93.0           87.0   \n", "34                                  None        97.0           88.0   \n", "35                                  None         NaN            NaN   \n", "36                                  None         NaN            NaN   \n", "37                                  None       103.0           98.0   \n", "38                                  None       103.0           99.0   \n", "39                                  None       103.0          100.0   \n", "40  Covington Free Throw 1 of 2 (12 PTS)       104.0          100.0   \n", "41  Covington Free Throw 2 of 2 (13 PTS)       105.0          100.0   \n", "42                                  None       105.0          101.0   \n", "43                                  None       105.0          102.0   \n", "\n", "    person1_type  player1_id  person2_type  player2_id  person3_type  \\\n", "0              4      203914             0           0             0   \n", "1              4     1627750             0           0             0   \n", "2              5      203967             0           0             0   \n", "3              5      203967             0           0             0   \n", "4              5      203967             0           0             0   \n", "5              4      203115             0           0             0   \n", "6              4      203115             0           0             0   \n", "7              4      203914             0           0             0   \n", "8              5     1626158             0           0             0   \n", "9              5     1626158             0           0             0   \n", "10             4      203999             0           0             0   \n", "11             4      203115             0           0             0   \n", "12             4      203115             0           0             0   \n", "13             5     1626158             0           0             0   \n", "14             4     1628470             0           0             0   \n", "15             4     1628470             0           0             0   \n", "16             4     1627750             0           0             0   \n", "17             4     1627750             0           0             0   \n", "18             4     1627750             0           0             0   \n", "19             4      203999             0           0             0   \n", "20             4      203999             0           0             0   \n", "21             4      203999             0           0             0   \n", "22             4      203999             0           0             0   \n", "23             5      203967             0           0             0   \n", "24             5      203967             0           0             0   \n", "25             4      203999             0           0             0   \n", "26             4      203999             0           0             0   \n", "27             4      203486             0           0             0   \n", "28             4      203486             0           0             0   \n", "29             4      203486             0           0             0   \n", "30             4      203486             0           0             0   \n", "31             4      203486             0           0             0   \n", "32             4      203486             0           0             0   \n", "33             4     1627750             0           0             0   \n", "34             4     1627750             0           0             0   \n", "35             4     1627750             0           0             0   \n", "36             4     1626168             0           0             0   \n", "37             4     1626168             0           0             0   \n", "38             4      203999             0           0             0   \n", "39             4      203999             0           0             0   \n", "40             5      203496             0           0             0   \n", "41             5      203496             0           0             0   \n", "42             4     1627750             0           0             0   \n", "43             4     1627750             0           0             0   \n", "\n", "    player3_id  \n", "0            0  \n", "1            0  \n", "2            0  \n", "3            0  \n", "4            0  \n", "5            0  \n", "6            0  \n", "7            0  \n", "8            0  \n", "9            0  \n", "10           0  \n", "11           0  \n", "12           0  \n", "13           0  \n", "14           0  \n", "15           0  \n", "16           0  \n", "17           0  \n", "18           0  \n", "19           0  \n", "20           0  \n", "21           0  \n", "22           0  \n", "23           0  \n", "24           0  \n", "25           0  \n", "26           0  \n", "27           0  \n", "28           0  \n", "29           0  \n", "30           0  \n", "31           0  \n", "32           0  \n", "33           0  \n", "34           0  \n", "35           0  \n", "36           0  \n", "37           0  \n", "38           0  \n", "39           0  \n", "40           0  \n", "41           0  \n", "42           0  \n", "43           0  "]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["events.loc[events.event_msg_type == EventType.FREE_THROW.value].reset_index()"]}, {"cell_type": "code", "execution_count": 42, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["raw = events[['period', 'seconds', 'home_score', 'visitor_score']].dropna()\n", "q_times = raw.groupby('period')[['seconds']].max()\n", "home = [0]\n", "visitor = [0]\n", "for index, row in raw.iterrows():\n", "   h = row['home_score']\n", "   v = row['visitor_score']"]}, {"cell_type": "code", "execution_count": 43, "metadata": {"collapsed": false, "deletable": true, "editable": true, "scrolled": true}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plot_scores_progression(raw)"]}, {"cell_type": "markdown", "metadata": {"collapsed": true, "deletable": true, "editable": true}, "source": ["Calculate lineups (i.e who is on the court at any given time)"]}, {"cell_type": "code", "execution_count": 80, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>period</th>\n", "      <th>event_num</th>\n", "      <th>seconds</th>\n", "      <th>player1_id</th>\n", "      <th>player2_id</th>\n", "      <th>person1_type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>1</td>\n", "      <td>33</td>\n", "      <td>526</td>\n", "      <td>200755</td>\n", "      <td>204456</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>1</td>\n", "      <td>40</td>\n", "      <td>490</td>\n", "      <td>203967</td>\n", "      <td>201573</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>1</td>\n", "      <td>61</td>\n", "      <td>359</td>\n", "      <td>203486</td>\n", "      <td>203115</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>1</td>\n", "      <td>62</td>\n", "      <td>359</td>\n", "      <td>101161</td>\n", "      <td>203967</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>1</td>\n", "      <td>63</td>\n", "      <td>359</td>\n", "      <td>1627732</td>\n", "      <td>1626158</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>1</td>\n", "      <td>75</td>\n", "      <td>312</td>\n", "      <td>201163</td>\n", "      <td>1626168</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62</th>\n", "      <td>1</td>\n", "      <td>93</td>\n", "      <td>244</td>\n", "      <td>201573</td>\n", "      <td>1627789</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63</th>\n", "      <td>1</td>\n", "      <td>94</td>\n", "      <td>244</td>\n", "      <td>203496</td>\n", "      <td>200755</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70</th>\n", "      <td>1</td>\n", "      <td>106</td>\n", "      <td>206</td>\n", "      <td>1627750</td>\n", "      <td>1628470</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71</th>\n", "      <td>1</td>\n", "      <td>107</td>\n", "      <td>206</td>\n", "      <td>203967</td>\n", "      <td>1627732</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72</th>\n", "      <td>1</td>\n", "      <td>108</td>\n", "      <td>206</td>\n", "      <td>204456</td>\n", "      <td>202344</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>1</td>\n", "      <td>121</td>\n", "      <td>161</td>\n", "      <td>203999</td>\n", "      <td>203486</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>120</th>\n", "      <td>2</td>\n", "      <td>183</td>\n", "      <td>614</td>\n", "      <td>200755</td>\n", "      <td>101161</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>121</th>\n", "      <td>2</td>\n", "      <td>184</td>\n", "      <td>614</td>\n", "      <td>202344</td>\n", "      <td>203496</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>128</th>\n", "      <td>2</td>\n", "      <td>197</td>\n", "      <td>556</td>\n", "      <td>1627732</td>\n", "      <td>1627789</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>151</th>\n", "      <td>2</td>\n", "      <td>232</td>\n", "      <td>370</td>\n", "      <td>203999</td>\n", "      <td>203914</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>152</th>\n", "      <td>2</td>\n", "      <td>233</td>\n", "      <td>370</td>\n", "      <td>1627750</td>\n", "      <td>203486</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>153</th>\n", "      <td>2</td>\n", "      <td>234</td>\n", "      <td>370</td>\n", "      <td>101161</td>\n", "      <td>1626158</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>162</th>\n", "      <td>2</td>\n", "      <td>248</td>\n", "      <td>306</td>\n", "      <td>203115</td>\n", "      <td>1628470</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>163</th>\n", "      <td>2</td>\n", "      <td>249</td>\n", "      <td>306</td>\n", "      <td>1626168</td>\n", "      <td>1627750</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>164</th>\n", "      <td>2</td>\n", "      <td>250</td>\n", "      <td>306</td>\n", "      <td>1627789</td>\n", "      <td>1627732</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>165</th>\n", "      <td>2</td>\n", "      <td>251</td>\n", "      <td>306</td>\n", "      <td>203967</td>\n", "      <td>202344</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174</th>\n", "      <td>2</td>\n", "      <td>267</td>\n", "      <td>256</td>\n", "      <td>204456</td>\n", "      <td>200755</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>188</th>\n", "      <td>2</td>\n", "      <td>287</td>\n", "      <td>178</td>\n", "      <td>1628470</td>\n", "      <td>203115</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>189</th>\n", "      <td>2</td>\n", "      <td>288</td>\n", "      <td>178</td>\n", "      <td>203486</td>\n", "      <td>203999</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>190</th>\n", "      <td>2</td>\n", "      <td>289</td>\n", "      <td>178</td>\n", "      <td>203496</td>\n", "      <td>1627789</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>191</th>\n", "      <td>2</td>\n", "      <td>290</td>\n", "      <td>178</td>\n", "      <td>202344</td>\n", "      <td>203967</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>253</th>\n", "      <td>3</td>\n", "      <td>374</td>\n", "      <td>494</td>\n", "      <td>203967</td>\n", "      <td>201573</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>254</th>\n", "      <td>3</td>\n", "      <td>375</td>\n", "      <td>494</td>\n", "      <td>200755</td>\n", "      <td>1626158</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>255</th>\n", "      <td>3</td>\n", "      <td>376</td>\n", "      <td>494</td>\n", "      <td>101161</td>\n", "      <td>204456</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266</th>\n", "      <td>3</td>\n", "      <td>394</td>\n", "      <td>449</td>\n", "      <td>201163</td>\n", "      <td>203115</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>269</th>\n", "      <td>3</td>\n", "      <td>399</td>\n", "      <td>449</td>\n", "      <td>1627732</td>\n", "      <td>203967</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>280</th>\n", "      <td>3</td>\n", "      <td>416</td>\n", "      <td>394</td>\n", "      <td>203486</td>\n", "      <td>1626168</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>291</th>\n", "      <td>3</td>\n", "      <td>432</td>\n", "      <td>284</td>\n", "      <td>1627750</td>\n", "      <td>201163</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>292</th>\n", "      <td>3</td>\n", "      <td>433</td>\n", "      <td>284</td>\n", "      <td>203967</td>\n", "      <td>202344</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>293</th>\n", "      <td>3</td>\n", "      <td>434</td>\n", "      <td>284</td>\n", "      <td>1626158</td>\n", "      <td>101161</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>299</th>\n", "      <td>3</td>\n", "      <td>444</td>\n", "      <td>251</td>\n", "      <td>203999</td>\n", "      <td>203486</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>321</th>\n", "      <td>3</td>\n", "      <td>474</td>\n", "      <td>102</td>\n", "      <td>201163</td>\n", "      <td>1627750</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>322</th>\n", "      <td>3</td>\n", "      <td>476</td>\n", "      <td>102</td>\n", "      <td>203914</td>\n", "      <td>201163</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>333</th>\n", "      <td>3</td>\n", "      <td>491</td>\n", "      <td>26</td>\n", "      <td>101161</td>\n", "      <td>203967</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>351</th>\n", "      <td>4</td>\n", "      <td>524</td>\n", "      <td>654</td>\n", "      <td>203486</td>\n", "      <td>203999</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>364</th>\n", "      <td>4</td>\n", "      <td>543</td>\n", "      <td>580</td>\n", "      <td>202344</td>\n", "      <td>101161</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>384</th>\n", "      <td>4</td>\n", "      <td>569</td>\n", "      <td>479</td>\n", "      <td>200755</td>\n", "      <td>203496</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>396</th>\n", "      <td>4</td>\n", "      <td>584</td>\n", "      <td>418</td>\n", "      <td>203115</td>\n", "      <td>201163</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>397</th>\n", "      <td>4</td>\n", "      <td>585</td>\n", "      <td>418</td>\n", "      <td>201573</td>\n", "      <td>204456</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>408</th>\n", "      <td>4</td>\n", "      <td>600</td>\n", "      <td>357</td>\n", "      <td>101161</td>\n", "      <td>1626158</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>416</th>\n", "      <td>4</td>\n", "      <td>613</td>\n", "      <td>280</td>\n", "      <td>1627732</td>\n", "      <td>200755</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>431</th>\n", "      <td>4</td>\n", "      <td>632</td>\n", "      <td>176</td>\n", "      <td>1626158</td>\n", "      <td>101161</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>432</th>\n", "      <td>4</td>\n", "      <td>634</td>\n", "      <td>176</td>\n", "      <td>204456</td>\n", "      <td>1627732</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>435</th>\n", "      <td>4</td>\n", "      <td>638</td>\n", "      <td>176</td>\n", "      <td>203914</td>\n", "      <td>203115</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>441</th>\n", "      <td>4</td>\n", "      <td>647</td>\n", "      <td>135</td>\n", "      <td>101161</td>\n", "      <td>201573</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>446</th>\n", "      <td>4</td>\n", "      <td>654</td>\n", "      <td>109</td>\n", "      <td>201163</td>\n", "      <td>203914</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>466</th>\n", "      <td>4</td>\n", "      <td>679</td>\n", "      <td>21</td>\n", "      <td>203999</td>\n", "      <td>201163</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>467</th>\n", "      <td>4</td>\n", "      <td>680</td>\n", "      <td>21</td>\n", "      <td>1627750</td>\n", "      <td>1628470</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>470</th>\n", "      <td>4</td>\n", "      <td>686</td>\n", "      <td>19</td>\n", "      <td>1628470</td>\n", "      <td>203999</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>471</th>\n", "      <td>4</td>\n", "      <td>688</td>\n", "      <td>19</td>\n", "      <td>203967</td>\n", "      <td>204456</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>474</th>\n", "      <td>4</td>\n", "      <td>692</td>\n", "      <td>19</td>\n", "      <td>201163</td>\n", "      <td>1627750</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>475</th>\n", "      <td>4</td>\n", "      <td>693</td>\n", "      <td>19</td>\n", "      <td>204456</td>\n", "      <td>203967</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>478</th>\n", "      <td>4</td>\n", "      <td>699</td>\n", "      <td>11</td>\n", "      <td>203999</td>\n", "      <td>201163</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     period  event_num  seconds  player1_id  player2_id  person1_type\n", "19        1         33      526      200755      204456             5\n", "25        1         40      490      203967      201573             5\n", "39        1         61      359      203486      203115             4\n", "40        1         62      359      101161      203967             5\n", "41        1         63      359     1627732     1626158             5\n", "49        1         75      312      201163     1626168             4\n", "62        1         93      244      201573     1627789             5\n", "63        1         94      244      203496      200755             5\n", "70        1        106      206     1627750     1628470             4\n", "71        1        107      206      203967     1627732             5\n", "72        1        108      206      204456      202344             5\n", "81        1        121      161      203999      203486             4\n", "120       2        183      614      200755      101161             5\n", "121       2        184      614      202344      203496             5\n", "128       2        197      556     1627732     1627789             5\n", "151       2        232      370      203999      203914             4\n", "152       2        233      370     1627750      203486             4\n", "153       2        234      370      101161     1626158             5\n", "162       2        248      306      203115     1628470             4\n", "163       2        249      306     1626168     1627750             4\n", "164       2        250      306     1627789     1627732             5\n", "165       2        251      306      203967      202344             5\n", "174       2        267      256      204456      200755             5\n", "188       2        287      178     1628470      203115             4\n", "189       2        288      178      203486      203999             4\n", "190       2        289      178      203496     1627789             5\n", "191       2        290      178      202344      203967             5\n", "253       3        374      494      203967      201573             5\n", "254       3        375      494      200755     1626158             5\n", "255       3        376      494      101161      204456             5\n", "266       3        394      449      201163      203115             4\n", "269       3        399      449     1627732      203967             5\n", "280       3        416      394      203486     1626168             4\n", "291       3        432      284     1627750      201163             4\n", "292       3        433      284      203967      202344             5\n", "293       3        434      284     1626158      101161             5\n", "299       3        444      251      203999      203486             4\n", "321       3        474      102      201163     1627750             4\n", "322       3        476      102      203914      201163             4\n", "333       3        491       26      101161      203967             5\n", "351       4        524      654      203486      203999             4\n", "364       4        543      580      202344      101161             5\n", "384       4        569      479      200755      203496             5\n", "396       4        584      418      203115      201163             4\n", "397       4        585      418      201573      204456             5\n", "408       4        600      357      101161     1626158             5\n", "416       4        613      280     1627732      200755             5\n", "431       4        632      176     1626158      101161             5\n", "432       4        634      176      204456     1627732             5\n", "435       4        638      176      203914      203115             4\n", "441       4        647      135      101161      201573             5\n", "446       4        654      109      201163      203914             4\n", "466       4        679       21      203999      201163             4\n", "467       4        680       21     1627750     1628470             4\n", "470       4        686       19     1628470      203999             4\n", "471       4        688       19      203967      204456             5\n", "474       4        692       19      201163     1627750             4\n", "475       4        693       19      204456      203967             5\n", "478       4        699       11      203999      201163             4"]}, "execution_count": 80, "metadata": {}, "output_type": "execute_result"}], "source": ["substitutions = events[events.event_msg_type == EventType.SUBSTITUTION.value][['period', 'event_num', 'seconds', 'player1_id', 'player2_id', 'person1_type']]\n", "#substitutions = substitutions[substitutions.period == 1]\n", "substitutions"]}, {"cell_type": "code", "execution_count": 236, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["def _filter_starters(all_players: set, substitutions: pd.DataFrame):\n", "    # Check we have at least 5 candidate starters\n", "    assert len(all_players) > 5\n", "    starters = all_players.copy()\n", "    for p in all_players:\n", "        did_start = False\n", "        outs = substitutions[substitutions.player_id_out==p]\n", "        ins = substitutions[substitutions.player_id_in==p]\n", "        # Never checked in but recorded stats so must have started\n", "        if len(ins) == 0:\n", "            did_start = True\n", "        # If the the checked out before checking in then they started\n", "        if len(outs) > 0 and len(ins) > 0:\n", "            did_start = min(outs.event_num) < min(ins.event_num)\n", "        if not did_start:\n", "            starters.remove(p)\n", "    return list(starters)\n", "\n", "def starters(game_id: int, period: int):\n", "    period_events = db.query(\"select * from playbyplays where game_id = \" + str(GAME_ID) + \" order by event_num\")\n", "    period_events = period_events[period_events.period == period]\n", "    start_of_period = period_events[period_events.event_msg_type==EventType.START_OF_PERIOD.value].iloc[0]\n", "    substitutions = period_events[period_events.event_msg_type == EventType.SUBSTITUTION.value][['period', 'event_num', 'seconds', 'player1_id', 'player2_id', 'person1_type']].rename(columns={'player1_id': 'player_id_out', 'player2_id': 'player_id_in'})\n", "\n", "    home_players = set()\n", "    visitor_players = set()\n", "    for index, row in period_events.iterrows():\n", "        if row.person1_type == PersonType.HOME.value:\n", "            home_players.add(row.player1_id)\n", "        if row.person2_type == PersonType.HOME.value:\n", "            home_players.add(row.player2_id)\n", "        if row.person1_type == PersonType.VISITOR.value:\n", "            visitor_players.add(row.player1_id)\n", "        if row.person2_type == PersonType.VISITOR.value:\n", "            visitor_players.add(row.player2_id)\n", "\n", "    home_starters = _filter_starters(home_players, substitutions)\n", "    assert len(home_starters) == 5\n", "    visitor_starters = _filter_starters(visitor_players, substitutions)\n", "    assert len(visitor_starters) == 5\n", "    \n", "    columns = ['game_id','period','seconds','event_num', 'home_player_id_1','home_player_id_2','home_player_id_3','home_player_id_4', 'home_player_id_5', 'visitor_player_id_1','visitor_player_id_2','visitor_player_id_3','visitor_player_id_4', 'visitor_player_id_5']\n", "    df = pd.DataFrame(columns=columns)\n", "    return df.append(pd.DataFrame([[game_id, start_of_period.period, start_of_period.seconds, start_of_period.event_num, home_starters[0], home_starters[1], home_starters[2], home_starters[3], home_starters[4], visitor_starters[0], visitor_starters[1], visitor_starters[2], visitor_starters[3], visitor_starters[4]]], columns=columns))"]}, {"cell_type": "code", "execution_count": 237, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>game_id</th>\n", "      <th>period</th>\n", "      <th>seconds</th>\n", "      <th>event_num</th>\n", "      <th>home_player_id_1</th>\n", "      <th>home_player_id_2</th>\n", "      <th>home_player_id_3</th>\n", "      <th>home_player_id_4</th>\n", "      <th>home_player_id_5</th>\n", "      <th>visitor_player_id_1</th>\n", "      <th>visitor_player_id_2</th>\n", "      <th>visitor_player_id_3</th>\n", "      <th>visitor_player_id_4</th>\n", "      <th>visitor_player_id_5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>1</td>\n", "      <td>720</td>\n", "      <td>2</td>\n", "      <td>1627750</td>\n", "      <td>203914</td>\n", "      <td>201163</td>\n", "      <td>203486</td>\n", "      <td>203999</td>\n", "      <td>203496</td>\n", "      <td>101161</td>\n", "      <td>200755</td>\n", "      <td>1627732</td>\n", "      <td>203967</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    game_id period seconds event_num home_player_id_1 home_player_id_2  \\\n", "0  21700536      1     720         2          1627750           203914   \n", "\n", "  home_player_id_3 home_player_id_4 home_player_id_5 visitor_player_id_1  \\\n", "0           201163           203486           203999              203496   \n", "\n", "  visitor_player_id_2 visitor_player_id_3 visitor_player_id_4  \\\n", "0              101161              200755             1627732   \n", "\n", "  visitor_player_id_5  \n", "0              203967  "]}, "execution_count": 237, "metadata": {}, "output_type": "execute_result"}], "source": ["starters(GAME_ID, period=1)"]}, {"cell_type": "code", "execution_count": 224, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>period</th>\n", "      <th>event_num</th>\n", "      <th>seconds</th>\n", "      <th>player_id_out</th>\n", "      <th>player_id_in</th>\n", "      <th>person1_type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>1</td>\n", "      <td>33</td>\n", "      <td>526</td>\n", "      <td>200755</td>\n", "      <td>204456</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>1</td>\n", "      <td>40</td>\n", "      <td>490</td>\n", "      <td>203967</td>\n", "      <td>201573</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>1</td>\n", "      <td>61</td>\n", "      <td>359</td>\n", "      <td>203486</td>\n", "      <td>203115</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>1</td>\n", "      <td>62</td>\n", "      <td>359</td>\n", "      <td>101161</td>\n", "      <td>203967</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>1</td>\n", "      <td>63</td>\n", "      <td>359</td>\n", "      <td>1627732</td>\n", "      <td>1626158</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>1</td>\n", "      <td>75</td>\n", "      <td>312</td>\n", "      <td>201163</td>\n", "      <td>1626168</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62</th>\n", "      <td>1</td>\n", "      <td>93</td>\n", "      <td>244</td>\n", "      <td>201573</td>\n", "      <td>1627789</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63</th>\n", "      <td>1</td>\n", "      <td>94</td>\n", "      <td>244</td>\n", "      <td>203496</td>\n", "      <td>200755</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70</th>\n", "      <td>1</td>\n", "      <td>106</td>\n", "      <td>206</td>\n", "      <td>1627750</td>\n", "      <td>1628470</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71</th>\n", "      <td>1</td>\n", "      <td>107</td>\n", "      <td>206</td>\n", "      <td>203967</td>\n", "      <td>1627732</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72</th>\n", "      <td>1</td>\n", "      <td>108</td>\n", "      <td>206</td>\n", "      <td>204456</td>\n", "      <td>202344</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81</th>\n", "      <td>1</td>\n", "      <td>121</td>\n", "      <td>161</td>\n", "      <td>203999</td>\n", "      <td>203486</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>120</th>\n", "      <td>2</td>\n", "      <td>183</td>\n", "      <td>614</td>\n", "      <td>200755</td>\n", "      <td>101161</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>121</th>\n", "      <td>2</td>\n", "      <td>184</td>\n", "      <td>614</td>\n", "      <td>202344</td>\n", "      <td>203496</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>128</th>\n", "      <td>2</td>\n", "      <td>197</td>\n", "      <td>556</td>\n", "      <td>1627732</td>\n", "      <td>1627789</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>151</th>\n", "      <td>2</td>\n", "      <td>232</td>\n", "      <td>370</td>\n", "      <td>203999</td>\n", "      <td>203914</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>152</th>\n", "      <td>2</td>\n", "      <td>233</td>\n", "      <td>370</td>\n", "      <td>1627750</td>\n", "      <td>203486</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>153</th>\n", "      <td>2</td>\n", "      <td>234</td>\n", "      <td>370</td>\n", "      <td>101161</td>\n", "      <td>1626158</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>162</th>\n", "      <td>2</td>\n", "      <td>248</td>\n", "      <td>306</td>\n", "      <td>203115</td>\n", "      <td>1628470</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>163</th>\n", "      <td>2</td>\n", "      <td>249</td>\n", "      <td>306</td>\n", "      <td>1626168</td>\n", "      <td>1627750</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>164</th>\n", "      <td>2</td>\n", "      <td>250</td>\n", "      <td>306</td>\n", "      <td>1627789</td>\n", "      <td>1627732</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>165</th>\n", "      <td>2</td>\n", "      <td>251</td>\n", "      <td>306</td>\n", "      <td>203967</td>\n", "      <td>202344</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174</th>\n", "      <td>2</td>\n", "      <td>267</td>\n", "      <td>256</td>\n", "      <td>204456</td>\n", "      <td>200755</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>188</th>\n", "      <td>2</td>\n", "      <td>287</td>\n", "      <td>178</td>\n", "      <td>1628470</td>\n", "      <td>203115</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>189</th>\n", "      <td>2</td>\n", "      <td>288</td>\n", "      <td>178</td>\n", "      <td>203486</td>\n", "      <td>203999</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>190</th>\n", "      <td>2</td>\n", "      <td>289</td>\n", "      <td>178</td>\n", "      <td>203496</td>\n", "      <td>1627789</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>191</th>\n", "      <td>2</td>\n", "      <td>290</td>\n", "      <td>178</td>\n", "      <td>202344</td>\n", "      <td>203967</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>253</th>\n", "      <td>3</td>\n", "      <td>374</td>\n", "      <td>494</td>\n", "      <td>203967</td>\n", "      <td>201573</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>254</th>\n", "      <td>3</td>\n", "      <td>375</td>\n", "      <td>494</td>\n", "      <td>200755</td>\n", "      <td>1626158</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>255</th>\n", "      <td>3</td>\n", "      <td>376</td>\n", "      <td>494</td>\n", "      <td>101161</td>\n", "      <td>204456</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>266</th>\n", "      <td>3</td>\n", "      <td>394</td>\n", "      <td>449</td>\n", "      <td>201163</td>\n", "      <td>203115</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>269</th>\n", "      <td>3</td>\n", "      <td>399</td>\n", "      <td>449</td>\n", "      <td>1627732</td>\n", "      <td>203967</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>280</th>\n", "      <td>3</td>\n", "      <td>416</td>\n", "      <td>394</td>\n", "      <td>203486</td>\n", "      <td>1626168</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>291</th>\n", "      <td>3</td>\n", "      <td>432</td>\n", "      <td>284</td>\n", "      <td>1627750</td>\n", "      <td>201163</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>292</th>\n", "      <td>3</td>\n", "      <td>433</td>\n", "      <td>284</td>\n", "      <td>203967</td>\n", "      <td>202344</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>293</th>\n", "      <td>3</td>\n", "      <td>434</td>\n", "      <td>284</td>\n", "      <td>1626158</td>\n", "      <td>101161</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>299</th>\n", "      <td>3</td>\n", "      <td>444</td>\n", "      <td>251</td>\n", "      <td>203999</td>\n", "      <td>203486</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>321</th>\n", "      <td>3</td>\n", "      <td>474</td>\n", "      <td>102</td>\n", "      <td>201163</td>\n", "      <td>1627750</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>322</th>\n", "      <td>3</td>\n", "      <td>476</td>\n", "      <td>102</td>\n", "      <td>203914</td>\n", "      <td>201163</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>333</th>\n", "      <td>3</td>\n", "      <td>491</td>\n", "      <td>26</td>\n", "      <td>101161</td>\n", "      <td>203967</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>351</th>\n", "      <td>4</td>\n", "      <td>524</td>\n", "      <td>654</td>\n", "      <td>203486</td>\n", "      <td>203999</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>364</th>\n", "      <td>4</td>\n", "      <td>543</td>\n", "      <td>580</td>\n", "      <td>202344</td>\n", "      <td>101161</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>384</th>\n", "      <td>4</td>\n", "      <td>569</td>\n", "      <td>479</td>\n", "      <td>200755</td>\n", "      <td>203496</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>396</th>\n", "      <td>4</td>\n", "      <td>584</td>\n", "      <td>418</td>\n", "      <td>203115</td>\n", "      <td>201163</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>397</th>\n", "      <td>4</td>\n", "      <td>585</td>\n", "      <td>418</td>\n", "      <td>201573</td>\n", "      <td>204456</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>408</th>\n", "      <td>4</td>\n", "      <td>600</td>\n", "      <td>357</td>\n", "      <td>101161</td>\n", "      <td>1626158</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>416</th>\n", "      <td>4</td>\n", "      <td>613</td>\n", "      <td>280</td>\n", "      <td>1627732</td>\n", "      <td>200755</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>431</th>\n", "      <td>4</td>\n", "      <td>632</td>\n", "      <td>176</td>\n", "      <td>1626158</td>\n", "      <td>101161</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>432</th>\n", "      <td>4</td>\n", "      <td>634</td>\n", "      <td>176</td>\n", "      <td>204456</td>\n", "      <td>1627732</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>435</th>\n", "      <td>4</td>\n", "      <td>638</td>\n", "      <td>176</td>\n", "      <td>203914</td>\n", "      <td>203115</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>441</th>\n", "      <td>4</td>\n", "      <td>647</td>\n", "      <td>135</td>\n", "      <td>101161</td>\n", "      <td>201573</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>446</th>\n", "      <td>4</td>\n", "      <td>654</td>\n", "      <td>109</td>\n", "      <td>201163</td>\n", "      <td>203914</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>466</th>\n", "      <td>4</td>\n", "      <td>679</td>\n", "      <td>21</td>\n", "      <td>203999</td>\n", "      <td>201163</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>467</th>\n", "      <td>4</td>\n", "      <td>680</td>\n", "      <td>21</td>\n", "      <td>1627750</td>\n", "      <td>1628470</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>470</th>\n", "      <td>4</td>\n", "      <td>686</td>\n", "      <td>19</td>\n", "      <td>1628470</td>\n", "      <td>203999</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>471</th>\n", "      <td>4</td>\n", "      <td>688</td>\n", "      <td>19</td>\n", "      <td>203967</td>\n", "      <td>204456</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>474</th>\n", "      <td>4</td>\n", "      <td>692</td>\n", "      <td>19</td>\n", "      <td>201163</td>\n", "      <td>1627750</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>475</th>\n", "      <td>4</td>\n", "      <td>693</td>\n", "      <td>19</td>\n", "      <td>204456</td>\n", "      <td>203967</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>478</th>\n", "      <td>4</td>\n", "      <td>699</td>\n", "      <td>11</td>\n", "      <td>203999</td>\n", "      <td>201163</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     period  event_num  seconds  player_id_out  player_id_in  person1_type\n", "19        1         33      526         200755        204456             5\n", "25        1         40      490         203967        201573             5\n", "39        1         61      359         203486        203115             4\n", "40        1         62      359         101161        203967             5\n", "41        1         63      359        1627732       1626158             5\n", "49        1         75      312         201163       1626168             4\n", "62        1         93      244         201573       1627789             5\n", "63        1         94      244         203496        200755             5\n", "70        1        106      206        1627750       1628470             4\n", "71        1        107      206         203967       1627732             5\n", "72        1        108      206         204456        202344             5\n", "81        1        121      161         203999        203486             4\n", "120       2        183      614         200755        101161             5\n", "121       2        184      614         202344        203496             5\n", "128       2        197      556        1627732       1627789             5\n", "151       2        232      370         203999        203914             4\n", "152       2        233      370        1627750        203486             4\n", "153       2        234      370         101161       1626158             5\n", "162       2        248      306         203115       1628470             4\n", "163       2        249      306        1626168       1627750             4\n", "164       2        250      306        1627789       1627732             5\n", "165       2        251      306         203967        202344             5\n", "174       2        267      256         204456        200755             5\n", "188       2        287      178        1628470        203115             4\n", "189       2        288      178         203486        203999             4\n", "190       2        289      178         203496       1627789             5\n", "191       2        290      178         202344        203967             5\n", "253       3        374      494         203967        201573             5\n", "254       3        375      494         200755       1626158             5\n", "255       3        376      494         101161        204456             5\n", "266       3        394      449         201163        203115             4\n", "269       3        399      449        1627732        203967             5\n", "280       3        416      394         203486       1626168             4\n", "291       3        432      284        1627750        201163             4\n", "292       3        433      284         203967        202344             5\n", "293       3        434      284        1626158        101161             5\n", "299       3        444      251         203999        203486             4\n", "321       3        474      102         201163       1627750             4\n", "322       3        476      102         203914        201163             4\n", "333       3        491       26         101161        203967             5\n", "351       4        524      654         203486        203999             4\n", "364       4        543      580         202344        101161             5\n", "384       4        569      479         200755        203496             5\n", "396       4        584      418         203115        201163             4\n", "397       4        585      418         201573        204456             5\n", "408       4        600      357         101161       1626158             5\n", "416       4        613      280        1627732        200755             5\n", "431       4        632      176        1626158        101161             5\n", "432       4        634      176         204456       1627732             5\n", "435       4        638      176         203914        203115             4\n", "441       4        647      135         101161        201573             5\n", "446       4        654      109         201163        203914             4\n", "466       4        679       21         203999        201163             4\n", "467       4        680       21        1627750       1628470             4\n", "470       4        686       19        1628470        203999             4\n", "471       4        688       19         203967        204456             5\n", "474       4        692       19         201163       1627750             4\n", "475       4        693       19         204456        203967             5\n", "478       4        699       11         203999        201163             4"]}, "execution_count": 224, "metadata": {}, "output_type": "execute_result"}], "source": ["events[events.event_msg_type == EventType.SUBSTITUTION.value][['period', 'event_num', 'seconds', 'player1_id', 'player2_id', 'person1_type']].rename(columns={'player1_id': 'player_id_out', 'player2_id': 'player_id_in'})"]}, {"cell_type": "code", "execution_count": 225, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>player_id</th>\n", "      <th>player_id</th>\n", "      <th>last_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>201163</td>\n", "      <td>201163</td>\n", "      <td><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>203486</td>\n", "      <td>203486</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>203914</td>\n", "      <td>203914</td>\n", "      <td><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>203999</td>\n", "      <td>203999</td>\n", "      <td>Jokic</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>1627750</td>\n", "      <td>1627750</td>\n", "      <td><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>101161</td>\n", "      <td>101161</td>\n", "      <td><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>200755</td>\n", "      <td>200755</td>\n", "      <td>Redick</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>203496</td>\n", "      <td>203496</td>\n", "      <td>Covington</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>203967</td>\n", "      <td>203967</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>1627732</td>\n", "      <td>1627732</td>\n", "      <td><PERSON></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    player_id  player_id  last_name\n", "1      201163     201163   Chandler\n", "5      203486     203486    <PERSON><PERSON><PERSON>\n", "6      203914     203914     Harris\n", "7      203999     203999      Jokic\n", "11    1627750    1627750     Murray\n", "13     101161     101161    <PERSON>\n", "14     200755     200755     Redick\n", "17     203496     203496  Covington\n", "19     203967     203967      Saric\n", "23    1627732    1627732    Simmons"]}, "execution_count": 225, "metadata": {}, "output_type": "execute_result"}], "source": ["box_score[box_score.start_position!=''][['player_id', 'last_name']]"]}, {"cell_type": "code", "execution_count": 93, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>game_id</th>\n", "      <th>event_num</th>\n", "      <th>event_msg_type</th>\n", "      <th>event_msg_action_type</th>\n", "      <th>period</th>\n", "      <th>seconds</th>\n", "      <th>wctimestring</th>\n", "      <th>home_description</th>\n", "      <th>visitor_description</th>\n", "      <th>home_score</th>\n", "      <th>visitor_score</th>\n", "      <th>person1_type</th>\n", "      <th>player1_id</th>\n", "      <th>person2_type</th>\n", "      <th>player2_id</th>\n", "      <th>person3_type</th>\n", "      <th>player3_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>2</td>\n", "      <td>12</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>720</td>\n", "      <td>9:11 PM</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>106</th>\n", "      <td>21700536</td>\n", "      <td>165</td>\n", "      <td>12</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>720</td>\n", "      <td>9:39 PM</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>31.0</td>\n", "      <td>29.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>213</th>\n", "      <td>21700536</td>\n", "      <td>327</td>\n", "      <td>12</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>720</td>\n", "      <td>10:19 PM</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>58.0</td>\n", "      <td>61.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>341</th>\n", "      <td>21700536</td>\n", "      <td>507</td>\n", "      <td>12</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>720</td>\n", "      <td>10:53 PM</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>83.0</td>\n", "      <td>80.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      game_id  event_num  event_msg_type  event_msg_action_type  period  \\\n", "0    21700536          2              12                      0       1   \n", "106  21700536        165              12                      0       2   \n", "213  21700536        327              12                      0       3   \n", "341  21700536        507              12                      0       4   \n", "\n", "     seconds wctimestring home_description visitor_description  home_score  \\\n", "0        720      9:11 PM             None                None         NaN   \n", "106      720      9:39 PM             None                None        31.0   \n", "213      720     10:19 PM             None                None        58.0   \n", "341      720     10:53 PM             None                None        83.0   \n", "\n", "     visitor_score  person1_type  player1_id  person2_type  player2_id  \\\n", "0              NaN             0           0             0           0   \n", "106           29.0             0           0             0           0   \n", "213           61.0             0           0             0           0   \n", "341           80.0             0           0             0           0   \n", "\n", "     person3_type  player3_id  \n", "0               0           0  \n", "106             0           0  \n", "213             0           0  \n", "341             0           0  "]}, "execution_count": 93, "metadata": {}, "output_type": "execute_result"}], "source": ["events[events.event_msg_type==EventType.START_OF_PERIOD.value]"]}, {"cell_type": "code", "execution_count": 94, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>game_id</th>\n", "      <th>event_num</th>\n", "      <th>event_msg_type</th>\n", "      <th>event_msg_action_type</th>\n", "      <th>period</th>\n", "      <th>seconds</th>\n", "      <th>wctimestring</th>\n", "      <th>home_description</th>\n", "      <th>visitor_description</th>\n", "      <th>home_score</th>\n", "      <th>visitor_score</th>\n", "      <th>person1_type</th>\n", "      <th>player1_id</th>\n", "      <th>person2_type</th>\n", "      <th>player2_id</th>\n", "      <th>person3_type</th>\n", "      <th>player3_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>105</th>\n", "      <td>21700536</td>\n", "      <td>154</td>\n", "      <td>13</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>9:38 PM</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>31.0</td>\n", "      <td>29.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>212</th>\n", "      <td>21700536</td>\n", "      <td>320</td>\n", "      <td>13</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>10:04 PM</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>58.0</td>\n", "      <td>61.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>340</th>\n", "      <td>21700536</td>\n", "      <td>500</td>\n", "      <td>13</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>10:51 PM</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>83.0</td>\n", "      <td>80.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>483</th>\n", "      <td>21700536</td>\n", "      <td>706</td>\n", "      <td>13</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>11:29 PM</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>107.0</td>\n", "      <td>102.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      game_id  event_num  event_msg_type  event_msg_action_type  period  \\\n", "105  21700536        154              13                      0       1   \n", "212  21700536        320              13                      0       2   \n", "340  21700536        500              13                      0       3   \n", "483  21700536        706              13                      0       4   \n", "\n", "     seconds wctimestring home_description visitor_description  home_score  \\\n", "105        0      9:38 PM             None                None        31.0   \n", "212        0     10:04 PM             None                None        58.0   \n", "340        0     10:51 PM             None                None        83.0   \n", "483        0     11:29 PM             None                None       107.0   \n", "\n", "     visitor_score  person1_type  player1_id  person2_type  player2_id  \\\n", "105           29.0             0           0             0           0   \n", "212           61.0             0           0             0           0   \n", "340           80.0             0           0             0           0   \n", "483          102.0             0           0             0           0   \n", "\n", "     person3_type  player3_id  \n", "105             0           0  \n", "212             0           0  \n", "340             0           0  \n", "483             0           0  "]}, "execution_count": 94, "metadata": {}, "output_type": "execute_result"}], "source": ["events[events.event_msg_type==EventType.END_OF_PERIOD.value]"]}, {"cell_type": "code", "execution_count": 95, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["def sub_lineup(lineup, in_id, out_id):\n", "    #print(lineup, in_id, out_id)\n", "    new_lineup = lineup.copy()\n", "    return [in_id\n", "        if x==out_id\n", "        else x\n", "        for x in new_lineup]"]}, {"cell_type": "code", "execution_count": 96, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [{"data": {"text/plain": ["[[203496, 101161, 200755, 1627732, 203967]]"]}, "execution_count": 96, "metadata": {}, "output_type": "execute_result"}], "source": ["lineups = [list(starters(events[events.period==1]))]\n", "lineups"]}, {"cell_type": "code", "execution_count": 201, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 204456 - 200755 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 201573 - 203967 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 203115 - 203486 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 203967 - 101161 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 1626158 - 1627732 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 1626168 - 201163 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 1627789 - 201573 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 200755 - 203496 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 1628470 - 1627750 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 1627732 - 203967 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 202344 - 204456 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 203486 - 203999 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 101161 - 200755 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 203496 - 202344 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 1627789 - 1627732 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 203914 - 203999 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 203486 - 1627750 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 1626158 - 101161 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 1628470 - 203115 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 1627750 - 1626168 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 1627732 - 1627789 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 202344 - 203967 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 200755 - 204456 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 203115 - 1628470 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 203999 - 203486 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 1627789 - 203496 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 203967 - 202344 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 201573 - 203967 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 1626158 - 200755 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 204456 - 101161 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 203115 - 201163 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 203967 - 1627732 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 1626168 - 203486 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 201163 - 1627750 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 202344 - 203967 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 101161 - 1626158 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 203486 - 203999 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 1627750 - 201163 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 201163 - 203914 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 203967 - 101161 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 203999 - 203486 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 101161 - 202344 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 203496 - 200755 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 201163 - 203115 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 204456 - 201573 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 1626158 - 101161 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 200755 - 1627732 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 101161 - 1626158 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 1627732 - 204456 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 203115 - 203914 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 201573 - 101161 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 203914 - 201163 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 201163 - 203999 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 1628470 - 1627750 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 203999 - 1628470 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 204456 - 203967 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 1627750 - 201163 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 203967 - 204456 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n", "['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5'] + 201163 - 203999 -> ['game_id', 'period', 'seconds', 'event_num', 'home_player_id_1', 'home_player_id_2', 'home_player_id_3', 'home_player_id_4', 'home_player_id_5', 'visitor_player_id_1', 'visitor_player_id_2', 'visitor_player_id_3', 'visitor_player_id_4', 'visitor_player_id_5']\n"]}], "source": ["#columns = ['game_id','period','seconds','home_player_id_1','home_player_id_2','home_player_id_3','home_player_id_4', 'home_player_id_5']\n", "df = pd.DataFrame(columns=columns)\n", "prev_period = 0\n", "for index, row in substitutions.iterrows():\n", "    if row['period'] != prev_period:\n", "        lineups = [list(starters(events, period=row['period']))]\n", "        prev_period = row['period']\n", "        df = df.append(pd.DataFrame([[GAME_ID, row['period'], 720, lineups[-1][0], lineups[-1][1], lineups[-1][2], lineups[-1][3], lineups[-1][4]]], columns=columns))\n", "    \n", "    new_lineup = sub_lineup(lineups[-1], row['player2_id'], row['player1_id'])\n", "    print(lineups[-1], '+', row['player2_id'],'-', row['player1_id'], '->', new_lineup)\n", "    lineups.append(new_lineup)\n", "    df = df.append(pd.DataFrame([[GAME_ID, row['period'], row['seconds'], new_lineup[0], new_lineup[1], new_lineup[2], new_lineup[3], new_lineup[4]]], columns=columns))"]}, {"cell_type": "code", "execution_count": 202, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>game_id</th>\n", "      <th>period</th>\n", "      <th>seconds</th>\n", "      <th>home_player_id_1</th>\n", "      <th>home_player_id_2</th>\n", "      <th>home_player_id_3</th>\n", "      <th>home_player_id_4</th>\n", "      <th>home_player_id_5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>1</td>\n", "      <td>720</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>1</td>\n", "      <td>526</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>1</td>\n", "      <td>490</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>1</td>\n", "      <td>359</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>1</td>\n", "      <td>359</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>1</td>\n", "      <td>359</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>1</td>\n", "      <td>312</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>1</td>\n", "      <td>244</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>1</td>\n", "      <td>244</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>1</td>\n", "      <td>206</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>1</td>\n", "      <td>206</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>1</td>\n", "      <td>206</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>1</td>\n", "      <td>161</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>2</td>\n", "      <td>720</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>2</td>\n", "      <td>614</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>2</td>\n", "      <td>614</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>2</td>\n", "      <td>556</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>2</td>\n", "      <td>370</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>2</td>\n", "      <td>370</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>2</td>\n", "      <td>370</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>2</td>\n", "      <td>306</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>2</td>\n", "      <td>306</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>2</td>\n", "      <td>306</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>2</td>\n", "      <td>306</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>2</td>\n", "      <td>256</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>2</td>\n", "      <td>178</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>2</td>\n", "      <td>178</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>2</td>\n", "      <td>178</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>2</td>\n", "      <td>178</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>3</td>\n", "      <td>720</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>3</td>\n", "      <td>449</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>3</td>\n", "      <td>449</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>3</td>\n", "      <td>394</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>3</td>\n", "      <td>284</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>3</td>\n", "      <td>284</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>3</td>\n", "      <td>284</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>3</td>\n", "      <td>251</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>3</td>\n", "      <td>102</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>3</td>\n", "      <td>102</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>3</td>\n", "      <td>26</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>4</td>\n", "      <td>720</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>4</td>\n", "      <td>654</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>4</td>\n", "      <td>580</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>4</td>\n", "      <td>479</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>4</td>\n", "      <td>418</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>4</td>\n", "      <td>418</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>4</td>\n", "      <td>357</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>4</td>\n", "      <td>280</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>4</td>\n", "      <td>176</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>4</td>\n", "      <td>176</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>4</td>\n", "      <td>176</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>4</td>\n", "      <td>135</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>4</td>\n", "      <td>109</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>4</td>\n", "      <td>21</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>4</td>\n", "      <td>21</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>4</td>\n", "      <td>19</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>4</td>\n", "      <td>19</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>4</td>\n", "      <td>19</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>4</td>\n", "      <td>19</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>21700536</td>\n", "      <td>4</td>\n", "      <td>11</td>\n", "      <td>game_id</td>\n", "      <td>period</td>\n", "      <td>seconds</td>\n", "      <td>event_num</td>\n", "      <td>home_player_id_1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>63 rows × 8 columns</p>\n", "</div>"], "text/plain": ["     game_id period seconds home_player_id_1 home_player_id_2  \\\n", "0   21700536      1     720          game_id           period   \n", "0   21700536      1     526          game_id           period   \n", "0   21700536      1     490          game_id           period   \n", "0   21700536      1     359          game_id           period   \n", "0   21700536      1     359          game_id           period   \n", "0   21700536      1     359          game_id           period   \n", "0   21700536      1     312          game_id           period   \n", "0   21700536      1     244          game_id           period   \n", "0   21700536      1     244          game_id           period   \n", "0   21700536      1     206          game_id           period   \n", "0   21700536      1     206          game_id           period   \n", "0   21700536      1     206          game_id           period   \n", "0   21700536      1     161          game_id           period   \n", "0   21700536      2     720          game_id           period   \n", "0   21700536      2     614          game_id           period   \n", "0   21700536      2     614          game_id           period   \n", "0   21700536      2     556          game_id           period   \n", "0   21700536      2     370          game_id           period   \n", "0   21700536      2     370          game_id           period   \n", "0   21700536      2     370          game_id           period   \n", "0   21700536      2     306          game_id           period   \n", "0   21700536      2     306          game_id           period   \n", "0   21700536      2     306          game_id           period   \n", "0   21700536      2     306          game_id           period   \n", "0   21700536      2     256          game_id           period   \n", "0   21700536      2     178          game_id           period   \n", "0   21700536      2     178          game_id           period   \n", "0   21700536      2     178          game_id           period   \n", "0   21700536      2     178          game_id           period   \n", "0   21700536      3     720          game_id           period   \n", "..       ...    ...     ...              ...              ...   \n", "0   21700536      3     449          game_id           period   \n", "0   21700536      3     449          game_id           period   \n", "0   21700536      3     394          game_id           period   \n", "0   21700536      3     284          game_id           period   \n", "0   21700536      3     284          game_id           period   \n", "0   21700536      3     284          game_id           period   \n", "0   21700536      3     251          game_id           period   \n", "0   21700536      3     102          game_id           period   \n", "0   21700536      3     102          game_id           period   \n", "0   21700536      3      26          game_id           period   \n", "0   21700536      4     720          game_id           period   \n", "0   21700536      4     654          game_id           period   \n", "0   21700536      4     580          game_id           period   \n", "0   21700536      4     479          game_id           period   \n", "0   21700536      4     418          game_id           period   \n", "0   21700536      4     418          game_id           period   \n", "0   21700536      4     357          game_id           period   \n", "0   21700536      4     280          game_id           period   \n", "0   21700536      4     176          game_id           period   \n", "0   21700536      4     176          game_id           period   \n", "0   21700536      4     176          game_id           period   \n", "0   21700536      4     135          game_id           period   \n", "0   21700536      4     109          game_id           period   \n", "0   21700536      4      21          game_id           period   \n", "0   21700536      4      21          game_id           period   \n", "0   21700536      4      19          game_id           period   \n", "0   21700536      4      19          game_id           period   \n", "0   21700536      4      19          game_id           period   \n", "0   21700536      4      19          game_id           period   \n", "0   21700536      4      11          game_id           period   \n", "\n", "   home_player_id_3 home_player_id_4  home_player_id_5  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "..              ...              ...               ...  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "0           seconds        event_num  home_player_id_1  \n", "\n", "[63 rows x 8 columns]"]}, "execution_count": 202, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 75, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/github/michael-quinlan/filmlab/bazel-bin/jupyter/jupyter.runfiles/pypi__ipykernel_5_1_1/ipykernel_launcher.py:2: UserWarning: registration of accessor <class '__main__.PlayByPlayAccessor'> under name 'playbyplay' for type <class 'pandas.core.frame.DataFrame'> is overriding a preexisting attribute with the same name.\n", "  \n"]}], "source": ["import pandas as pd\n", "@pd.api.extensions.register_dataframe_accessor(\"playbyplay\")\n", "class PlayByPlayAccessor:\n", "    def __init__(self, pandas_obj):\n", "        self._validate(pandas_obj)\n", "        self._obj = pandas_obj\n", "\n", "    @staticmethod\n", "    def _validate(obj):\n", "        pass\n", "\n", "    @property\n", "    def <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(self):\n", "        # return the geographic center point of this DataFrame\n", "        return self._obj[self._obj.event_msg_type==EventType.START_OF_PERIOD.value]\n", "    \n", "    @property\n", "    def <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(self):\n", "        # return the geographic center point of this DataFrame\n", "        return self._obj[self._obj.event_msg_type==EventType.END_OF_PERIOD.value]\n", "    "]}, {"cell_type": "code", "execution_count": 77, "metadata": {"collapsed": false}, "outputs": [{"ename": "AttributeError", "evalue": "'PlayByPlayAccessor' object has no attribute 'EndOfPeriod'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-77-b0c3e527fc9e>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mevents\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mplaybyplay\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mEndOfPeriod\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m'period'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'home_score'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'visitor_score'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfillna\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m: 'PlayByPlayAccessor' object has no attribute 'EndOfPeriod'"]}], "source": ["events.playbyplay.EndOfPeriod[['period', 'home_score', 'visitor_score']].fillna(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": ["def starters(events):\n", "    all_players = set()\n", "    for index, row in events.iterrows():\n", "        if row.person1_type == PersonType.VISITOR.value:\n", "            starters.add(row.player1_id)\n", "        if row.person2_type == PersonType.VISITOR.value:\n", "            starters.add(row.player2_id)\n", "    \n", "    all_players = starters.copy()    \n", "    for p in all_players.copy():\n", "        did_start = False\n", "        outs = substitutions[substitutions.player1_id==p]\n", "        ins = substitutions[substitutions.player2_id==p]\n", "        if len(outs) > 0 and len(ins) == 0:\n", "            did_start = True\n", "        if len(outs) > 0 and len(ins) > 0:\n", "            did_start = min(outs.event_num) < min(ins.event_num)\n", "        if not did_start:\n", "            starters.remove(p)\n", "\n", "    return starters"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 2}