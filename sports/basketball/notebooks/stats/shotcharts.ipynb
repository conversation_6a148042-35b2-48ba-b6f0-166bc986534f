{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["%matplotlib inline\n", "%load_ext autoreload\n", "%autoreload 2\n", "import pandas as pd\n", "from sports.basketball.database.database import Database\n", "from plotly.offline import download_plotlyjs, init_notebook_mode\n", "from sports.basketball.stats.shotcharts import plot as plot_shotchart\n", "db = Database()\n", "init_notebook_mode(connected=False)"]}, {"cell_type": "markdown", "metadata": {"deletable": true, "editable": true}, "source": ["## Plot the shot chart for a given game"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["GAME_ID = 21700536\n", "game_df = db.query(\"select * from shotcharts where game_id = \" + str(GAME_ID) + \" order by game_event_id\")\n", "plot_shotchart(game_df, title='All Shots', subtitle=GAME_ID)"]}, {"cell_type": "markdown", "metadata": {"deletable": true, "editable": true}, "source": ["#### Output the html div ####"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["GAME_ID = 21700536\n", "game_df = db.query(\"select * from shotcharts where game_id = \" + str(GAME_ID) + \" order by game_event_id\")\n", "plot_shotchart(game_df, title='All Shots', output_div=True)"]}, {"cell_type": "markdown", "metadata": {"deletable": true, "editable": true}, "source": ["## Plot the shot chart for <PERSON><PERSON> in 2018-19"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true, "scrolled": true}, "outputs": [], "source": ["STEPH_ID = 201939\n", "steph_df = db.query(\"select * from shotcharts where player_id= \" + str(STEPH_ID) + \" and game_id in (select game_id from games where season=2018 and season_type='regular')\")\n", "plot_shotchart(steph_df, title='<PERSON><PERSON> (2018-19)', output_file='/tmp/steph_chart.png')"]}, {"cell_type": "markdown", "metadata": {"deletable": true, "editable": true}, "source": ["### Specific shot types"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true, "scrolled": true}, "outputs": [], "source": ["plot_shotchart(steph_df[steph_df.shot_type=='3PT Field Goal'], title='<PERSON><PERSON> 3pts (2018-19)', output_file='/tmp/steph_chart.png')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["plot_shotchart(steph_df[steph_df.shot_zone_basic=='Mid-Range'], title='Steph <PERSON> Mid Range (2018-19)', output_file='/tmp/steph_chart.png')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["plot_shotchart(steph_df[steph_df.action_type=='Step Back Jump shot'], title='<PERSON><PERSON> Step Backs (2018-19)', output_file='/tmp/steph_chart.png')"]}, {"cell_type": "markdown", "metadata": {"collapsed": true, "deletable": true, "editable": true}, "source": ["### Create a sequence of images"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["STEPH_ID = 2747\n", "import math\n", "df = db.query(\"select * from shotcharts join games on shotcharts.game_id=games.game_id where shotcharts.player_id= \" + str(STEPH_ID) + \" and games.season_type='regular' order by games.game_date_est, shotcharts.game_event_id\")\n", "splits = np.array_split(df, math.ceil(len(df)/500.0))\n", "idx = 0\n", "for s in splits:\n", "    file_name = \"/tmp/steph_chart_\"+str(idx)+\".png\"\n", "    idx+=1\n", "    plot_shotchart(s, title=\"Steph Chart\", subtitle='Last 500 shots at '+ max(s['game_date_est']).strftime(\"%B %d, %Y\"), scatter_format='Minimal', output_file=file_name)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 2}