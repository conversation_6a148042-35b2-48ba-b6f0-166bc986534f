{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "import argparse\n", "import tensorflow as tf\n", "import pandas as pd\n", "\n", "from sports.basketball.database.database import Database\n", "from sports.basketball.stats import advanced, basic\n", "\n", "import itertools"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["db = Database()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["def load_data():\n", "    \"\"\"Returns the iris dataset as (train_x, train_y), (test_x, test_y).\"\"\"\n", "    df = db.query(\"select * from boxscores\")\n", "    \n", "    train = df.sample(frac=0.8,random_state=200)\n", "    test = df.drop(train.index)\n", "    \n", "    train['ts%'] = train.apply(advanced.true_shooting_percentage, axis=1)\n", "    test['ts%'] = test.apply(advanced.true_shooting_percentage, axis=1)\n", "    \n", "    train.fillna(value=0, inplace=True)\n", "    test.fillna(value=0, inplace=True)\n", "    \n", "    return train, test"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["train, test = load_data()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["FEATURES = ['pts', 'fgm' , 'fga', 'ftm', 'fta', 'fg3m', 'fg3a', 'oreb', 'dreb', 'ast', 'stl', 'blk', 'pf', 'to'] \n", "            #[\"pts\", \"fga\", \"fta\"]\n", "LABEL = \"ts%\" #\"ts%\"\n", "\n", "def get_input_fn(data_set, num_epochs=None, shuffle=True):\n", "  return tf.estimator.inputs.pandas_input_fn(\n", "      x=pd.DataFrame({k: data_set[k].values for k in FEATURES}),\n", "      y=pd.Series(data_set[LABEL].values),\n", "      num_epochs=num_epochs,\n", "      shuffle=shuffle)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["feature_cols = [tf.feature_column.numeric_column(k) for k in FEATURES]\n", "\n", "# Build 2 layer fully connected DNN with 10, 10 units respectively.\n", "regressor = tf.estimator.DNNRegressor(feature_columns=feature_cols,\n", "                                      hidden_units=[64, 32, 16],\n", "                                      model_dir=\"/tmp/true_shooting_full_model\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": [" # Train\n", "regressor.train(input_fn=get_input_fn(train), steps=50000)\n", "\n", "# Evaluate loss over one epoch of test_set.\n", "ev = regressor.evaluate(input_fn=get_input_fn(test, num_epochs=1, shuffle=False))\n", "loss_score = ev[\"loss\"]\n", "print(\"Loss: {0:f}\".format(loss_score))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["# Print out predictions over a slice of prediction_set.\n", "y = regressor.predict(\n", "      input_fn=get_input_fn(test, num_epochs=1, shuffle=False))\n", "  # .predict() returns an iterator of dicts; convert to a list and print\n", "  # predictions\n", "predictions = list(p[\"predictions\"] for p in y)\n", "#print(\"Predictions: {}\".format(str(predictions)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["predictions[152]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["test['ts%'].iloc[152]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 2}