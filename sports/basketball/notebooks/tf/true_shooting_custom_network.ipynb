{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "import argparse\n", "import tensorflow as tf\n", "import pandas as pd\n", "\n", "from sports.basketball.database.database import Database\n", "from sports.basketball.stats import advanced, basic\n", "\n", "import itertools"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["db = Database()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["def load_data():\n", "    \"\"\"Returns the iris dataset as (train_x, train_y), (test_x, test_y).\"\"\"\n", "    df = db.query(\"select * from boxscores\")\n", "    \n", "    train = df.sample(frac=0.8,random_state=200)\n", "    test = df.drop(train.index)\n", "    \n", "    train['ts%'] = train.apply(advanced.true_shooting_percentage, axis=1)\n", "    train['gs'] = train.apply(advanced.game_score, axis=1)\n", "    \n", "    test['ts%'] = test.apply(advanced.true_shooting_percentage, axis=1)\n", "    test['gs'] = test.apply(advanced.game_score, axis=1)\n", "    \n", "    \n", "    return train, test"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["train, test = load_data()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["FEATURES = ['pts','fgm' , 'fga', 'ftm', 'fta', 'fg3m', 'fg3a', 'oreb', 'dreb', 'ast', 'stl', 'blk', 'pf', 'to'] \n", "            #[\"pts\", \"fga\", \"fta\"]\n", "LABEL = \"ts%\" #\"ts%\"\n", "\n", "feature_cols = [tf.feature_column.numeric_column(k) for k in FEATURES]\n", "\n", "def get_input_fn(data_set, num_epochs=None, shuffle=True):\n", "  return tf.estimator.inputs.pandas_input_fn(\n", "      x=pd.DataFrame({k: data_set[k].values for k in FEATURES}),\n", "      y=pd.Series(data_set[LABEL].values),\n", "      num_epochs=num_epochs,\n", "      shuffle=shuffle)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["def model_fn(features, labels, mode, params):\n", "  \"\"\"Model function for Estimator.\"\"\"\n", "\n", "  # Connect the first hidden layer to input layer\n", "  # (features[\"x\"]) with relu activation\n", "  input_layer = tf.feature_column.input_layer(features=features, \n", "                                                feature_columns=feature_cols)\n", "  \n", "    \n", "  first_hidden_layer = tf.layers.dense(input_layer, 64, activation=tf.nn.relu)\n", "\n", "  # Connect the second hidden layer to first hidden layer with relu\n", "  second_hidden_layer = tf.layers.dense(\n", "      first_hidden_layer, 32, activation=tf.nn.relu)\n", "\n", "  # Connect the third hidden layer to second hidden layer with relu\n", "  third_hidden_layer = tf.layers.dense(\n", "      second_hidden_layer, 16, activation=tf.nn.relu)\n", "\n", "  # Connect the output layer to second hidden layer (no activation fn)\n", "  output_layer = tf.layers.dense(third_hidden_layer, 1)\n", "\n", "  # Reshape output layer to 1-dim Tensor to return predictions\n", "  predictions = tf.reshape(output_layer, [-1])\n", "\n", "  # Provide an estimator spec for `ModeKeys.PREDICT`.\n", "  if mode == tf.estimator.ModeKeys.PREDICT:\n", "    return tf.estimator.EstimatorSpec(\n", "        mode=mode,\n", "        predictions={\"ts%\": predictions})\n", "\n", "  # Calculate loss using mean squared error\n", "  loss = tf.losses.mean_squared_error(labels, predictions)\n", "\n", "  optimizer = tf.train.GradientDescentOptimizer(\n", "      learning_rate=params[\"learning_rate\"])\n", "  train_op = optimizer.minimize(\n", "      loss=loss, global_step=tf.train.get_global_step())\n", "\n", "  # Calculate root mean squared error as additional eval metric\n", "  eval_metric_ops = {\n", "      \"rmse\": tf.metrics.root_mean_squared_error(\n", "          tf.cast(labels, tf.float32), predictions)\n", "  }\n", "\n", "  # Provide an estimator spec for `ModeKeys.EVAL` and `ModeKeys.TRAIN` modes.\n", "  return tf.estimator.EstimatorSpec(\n", "      mode=mode,\n", "      loss=loss,\n", "      train_op=train_op,\n", "      eval_metric_ops=eval_metric_ops)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["model_params = {\"learning_rate\": 0.001}\n", "nn = tf.estimator.Estimator(model_fn=model_fn, params=model_params)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["nn.train(input_fn=get_input_fn(train), steps=50000)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["ev = nn.evaluate(input_fn=get_input_fn(test, num_epochs=1, shuffle=False))\n", "loss_score = ev[\"loss\"]\n", "print(\"Loss: {0:f}\".format(loss_score))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["# Print out predictions over a slice of prediction_set.\n", "predictions= nn.predict(\n", "      input_fn=get_input_fn(test, num_epochs=1, shuffle=False))\n", "  # .predict() returns an iterator of dicts; convert to a list and print\n", "  # predictions\n", "for i, p in enumerate(predictions):\n", "    print(\"Prediction %s: %s  %s\" % (i + 1, p[\"ts%\"], test['ts%'].iloc[i]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["test['ts%'].iloc[1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 2}