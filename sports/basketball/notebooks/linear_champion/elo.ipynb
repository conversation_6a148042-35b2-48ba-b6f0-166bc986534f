{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["%matplotlib inline\n", "%load_ext autoreload\n", "%autoreload 2\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "import math\n", "import pandas as pd\n", "from sports.basketball.linear_champion.calculator import Calculator\n", "from sports.basketball.linear_champion.result import Result\n", "from sports.basketball.scrape.fivethirtyeight.nba_elo_download import Downloader"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["calc = Calculator()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["elo_truth = calc.raw_dataframe[['team1', 'team2', 'score1', 'score2', 'elo1_pre', 'elo2_pre', 'elo1_post', 'elo2_post']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["elo_truth"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["K = 20\n", "HOME_COURT = 100"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["def margin_of_victory_mult(score_diff, elo_diff):\n", "    return math.pow((score_diff + 3.0),0.8) / (7.5+.006*(elo_diff))\n", "\n", "def year_carrover(elo):\n", "    return (0.75 * elo) + (0.25 * 1505)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["margin_of_victory_mult(4,218)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["year_carrover(1754)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["def new_elos(score1:float, score2:float, elo1:float, elo2:float):\n", "    elo1 = elo1 + HOME_COURT\n", "    ea1 = 1.0 / (1 + math.pow(10,(elo2-elo1)/400.0))\n", "    ea2 = 1.0 / (1 + math.pow(10,(elo1-elo2)/400.0))\n", "    if (score1>score2):\n", "        mov = margin_of_victory_mult(score1-score2, (elo1)-elo2)\n", "        return elo1-HOME_COURT + K*mov*(1 - ea1), elo2 + K*mov*(0 - ea2)\n", "    else:\n", "        mov = margin_of_victory_mult(score2-score1, elo2-(elo1))\n", "        return elo1-HOME_COURT + K*mov*(0 - ea1), elo2 + K*mov*(1 - ea2)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["new_elos(68,66,1300,1300)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["def elo_update(row: pd.core.series.Series):\n", "    return new_elos(row.score1, row.score2, row.elo1_pre, row.elo2_pre)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true, "scrolled": false}, "outputs": [], "source": ["elo_truth.apply(elo_update, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["import math\n", "class Calculator(object):\n", "\n", "    def __init__(self, K=20, home_court=100):\n", "        self._K = K\n", "        self._home_court = 100\n", "\n", "    @staticmethod\n", "    def __margin_of_victory_mult(score_diff, elo_diff):\n", "        return math.pow((score_diff + 3.0),0.8) / (7.5+.006*(elo_diff))\n", "\n", "    @staticmethod\n", "    def __year_carrover(elo):\n", "        return (0.75 * elo) + (0.25 * 1505)\n", "\n", "    # Function that calculates the sequence of champions given a set of games and an optional starting champion.\n", "    # Where possible use the above methods with know filters, but this is provided to allow arbitrary filters and\n", "    # queries.\n", "    def calculate(self, score1:float, score2:float, elo1:float, elo2:float):\n", "        elo1 = elo1 + HOME_COURT\n", "        ea1 = 1.0 / (1 + math.pow(10,(elo2-elo1)/400.0))\n", "        ea2 = 1.0 / (1 + math.pow(10,(elo1-elo2)/400.0))\n", "        if (score1>score2):\n", "            mov = margin_of_victory_mult(score1-score2, (elo1)-elo2)\n", "            return elo1-HOME_COURT + K*mov*(1 - ea1), elo2 + K*mov*(0 - ea2)\n", "        else:\n", "            mov = margin_of_victory_mult(score2-score1, elo2-(elo1))\n", "            return elo1-HOME_COURT + K*mov*(0 - ea1), elo2 + K*mov*(1 - ea2)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["elo_calc = Calculator()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["elo_calc.calculate(66,68,1300,1300)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 2}