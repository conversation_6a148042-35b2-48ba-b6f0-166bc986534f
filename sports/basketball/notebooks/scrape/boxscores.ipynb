{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings('ignore')\n", "from sports.basketball.scrape.nba.boxscore import Boxscore\n", "from sports.basketball.database.database import Database\n", "from datetime import date\n", "import pandas as pd\n", "import time"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["db = Database()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["sql = 'SELECT DISTINCT(game_id_string), season FROM games WHERE NOT EXISTS (SELECT 1 FROM boxscores WHERE boxscores.game_id = games.game_id)'"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["missing_boxscores=db.query(sql)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["for i,x in enumerate(missing_boxscores.values):\n", "    print i, len(missing_boxscores), x[0]\n", "    Boxscore(game_id=x[0], season=x[1])\n", "    time.sleep(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 2}