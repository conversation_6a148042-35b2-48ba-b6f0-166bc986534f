{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "from sports.basketball.scrape.nba.scoreboard import Scoreboard\n", "from sports.basketball.scrape.nba.constants import WNBA, WNBA_REGULAR_SEASON_DATES, WNBA_PLAYOFF_SEASON_DATES\n", "from sports.basketball.database.database import Database\n", "from datetime import date\n", "import pandas as pd\n", "import time"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["db = Database()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["WNBA_REGULAR_SEASON_DATES"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["WNBA_PLAYOFF_SEASON_DATES"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true, "scrolled": true}, "outputs": [], "source": ["end_date = WNBA_PLAYOFF_SEASON_DATES[2018][1]\n", "start_date = WNBA_REGULAR_SEASON_DATES[2018][0]\n", "daterange = pd.date_range(start_date, end_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true, "scrolled": true}, "outputs": [], "source": ["for d in reversed(daterange):\n", "    print(d)\n", "    Scoreboard(day=d, league=WNBA)\n", "    time.sleep(5) "]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 2}