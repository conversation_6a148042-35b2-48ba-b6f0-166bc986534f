{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import datetime"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["raw_df = pd.read_csv('~/Downloads/nba_elo.csv')\n", "raw_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["raw_df = raw_df.fillna(value='0') \n", "season = raw_df.groupby('season')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true, "scrolled": false}, "outputs": [], "source": ["#Print Reguluar Season\n", "for g in season:\n", "    g_df = g[1]\n", "    reg = g_df[g_df.playoff=='0']\n", "    start_day = datetime.datetime.strptime(min(reg.date), \"%Y-%m-%d\")\n", "    end_day = datetime.datetime.strptime(max(reg.date), \"%Y-%m-%d\")\n", "    print(\"{s}: (date(day={sd}, month={sm}, year={sy}), date(day={ed}, month={em}, year={ey})),\".format(s=min(reg.season)-1, sd=start_day.day, sm=start_day.month, sy=start_day.year, ed=end_day.day, em=end_day.month, ey=end_day.year))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["#Print Playoffs\n", "for g in season:\n", "    g_df = g[1]\n", "    reg = g_df[g_df.playoff!='0']\n", "    start_day = datetime.datetime.strptime(min(reg.date), \"%Y-%m-%d\")\n", "    end_day = datetime.datetime.strptime(max(reg.date), \"%Y-%m-%d\")\n", "    print(\"{s}: (date(day={sd}, month={sm}, year={sy}), date(day={ed}, month={em}, year={ey})),\".format(s=min(reg.season)-1, sd=start_day.day, sm=start_day.month, sy=start_day.year, ed=end_day.day, em=end_day.month, ey=end_day.year))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 2}