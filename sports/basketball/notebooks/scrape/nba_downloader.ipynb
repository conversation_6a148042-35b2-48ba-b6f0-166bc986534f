{"cells": [{"cell_type": "code", "execution_count": 1, "id": "51fcdc77", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "from sports.basketball.scrape.nba.nba_download import NBADownload\n", "from sports.basketball.scrape.nba.scoreboard import Scoreboard\n", "from sports.basketball.scrape.nba.boxscore import Boxscore\n", "from sports.basketball.scrape.nba.play_by_play import PlayByPlay\n", "from sports.basketball.scrape.nba.shotchart import <PERSON><PERSON><PERSON>\n", "from sports.basketball.scrape.nba.constants import NBA_REGULAR_SEASON_DATES, NBA_PLAYOFF_SEASON_DATES\n", "from sports.basketball.database.database import Database\n", "from datetime import date\n", "import pandas as pd\n", "import time"]}, {"cell_type": "code", "execution_count": 2, "id": "306c4b2a", "metadata": {}, "outputs": [], "source": ["def get_nba_download_row_count(database: Database) -> int:\n", "    return db.count(\"select count(*) from nba_downloads\")"]}, {"cell_type": "code", "execution_count": 3, "id": "3ae958d8", "metadata": {}, "outputs": [], "source": ["db = Database()"]}, {"cell_type": "code", "execution_count": 1, "id": "32af6b0c", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'NBA_PLAYOFF_SEASON_DATES' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-1-e897644269aa>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mend_date\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mNBA_PLAYOFF_SEASON_DATES\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m2021\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      2\u001b[0m \u001b[0mstart_date\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mNBA_REGULAR_SEASON_DATES\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m2021\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[0mdaterange\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpd\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdate_range\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mstart_date\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mend_date\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mNameError\u001b[0m: name 'NBA_PLAYOFF_SEASON_DATES' is not defined"]}], "source": ["end_date = NBA_PLAYOFF_SEASON_DATES[2021][1]\n", "start_date = NBA_REGULAR_SEASON_DATES[2021][0]\n", "daterange = pd.date_range(start_date, end_date)"]}, {"cell_type": "code", "execution_count": 2, "id": "78dca838", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'daterange' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-2-8b60c7d1e9f2>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0;32mfor\u001b[0m \u001b[0md\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mreversed\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdaterange\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      2\u001b[0m     \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0md\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      3\u001b[0m     \u001b[0mbefore\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mget_nba_download_row_count\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdb\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      4\u001b[0m     \u001b[0msb\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mScoreboard\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mday\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0md\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mforce_fetch\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      5\u001b[0m     \u001b[0mNBADownload\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mscoreboard\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0msb\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdatabase\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mdb\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mNameError\u001b[0m: name 'daterange' is not defined"]}], "source": ["for d in reversed(daterange):\n", "    print(d)\n", "    before = get_nba_download_row_count(db)\n", "    sb = Scoreboard(day=d, force_fetch=True)\n", "    NBADownload(scoreboard=sb, database=db)\n", "    after = get_nba_download_row_count(db)\n", "    assert (before + len(sb.dataframe)) == after, f\"{before} + {len(sb.dataframe)} != {after}\"\n", "    time.sleep(5) "]}, {"cell_type": "code", "execution_count": 3, "id": "a837f1ff", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'sc' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-3-c89afa88e067>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0msc\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mtransform\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mNameError\u001b[0m: name 'sc' is not defined"]}], "source": ["sc.transform()"]}, {"cell_type": "code", "execution_count": 12, "id": "1f16c959", "metadata": {}, "outputs": [], "source": ["nd = NBADownloader(scoreboard=sc, database=db)"]}, {"cell_type": "code", "execution_count": 20, "id": "a06b78c4", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>game_id</th>\n", "      <th>game_id_string</th>\n", "      <th>game_date_est</th>\n", "      <th>season</th>\n", "      <th>game_status_id</th>\n", "      <th>status</th>\n", "      <th>num_box_score</th>\n", "      <th>num_play_by_play</th>\n", "      <th>num_shot_chart</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>41600405</td>\n", "      <td>0041600405</td>\n", "      <td>2017-06-12</td>\n", "      <td>2016</td>\n", "      <td>3</td>\n", "      <td>Final</td>\n", "      <td>26</td>\n", "      <td>465</td>\n", "      <td>178.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>41600402</td>\n", "      <td>0041600402</td>\n", "      <td>2017-06-04</td>\n", "      <td>2016</td>\n", "      <td>3</td>\n", "      <td>Final</td>\n", "      <td>26</td>\n", "      <td>486</td>\n", "      <td>189.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>41600401</td>\n", "      <td>0041600401</td>\n", "      <td>2017-06-01</td>\n", "      <td>2016</td>\n", "      <td>3</td>\n", "      <td>Final</td>\n", "      <td>26</td>\n", "      <td>510</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>41600305</td>\n", "      <td>0041600305</td>\n", "      <td>2017-05-25</td>\n", "      <td>2016</td>\n", "      <td>3</td>\n", "      <td>Final</td>\n", "      <td>26</td>\n", "      <td>444</td>\n", "      <td>171.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>41600304</td>\n", "      <td>0041600304</td>\n", "      <td>2017-05-23</td>\n", "      <td>2016</td>\n", "      <td>3</td>\n", "      <td>Final</td>\n", "      <td>26</td>\n", "      <td>392</td>\n", "      <td>153.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>74</th>\n", "      <td>41600404</td>\n", "      <td>0041600404</td>\n", "      <td>2017-06-09</td>\n", "      <td>2016</td>\n", "      <td>3</td>\n", "      <td>Final</td>\n", "      <td>26</td>\n", "      <td>510</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75</th>\n", "      <td>41600164</td>\n", "      <td>0041600164</td>\n", "      <td>2017-04-23</td>\n", "      <td>2016</td>\n", "      <td>3</td>\n", "      <td>Final</td>\n", "      <td>25</td>\n", "      <td>492</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>76</th>\n", "      <td>41600202</td>\n", "      <td>0041600202</td>\n", "      <td>2017-05-02</td>\n", "      <td>2016</td>\n", "      <td>3</td>\n", "      <td>Final</td>\n", "      <td>26</td>\n", "      <td>525</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>41600403</td>\n", "      <td>0041600403</td>\n", "      <td>2017-06-07</td>\n", "      <td>2016</td>\n", "      <td>3</td>\n", "      <td>Final</td>\n", "      <td>26</td>\n", "      <td>486</td>\n", "      <td>173.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78</th>\n", "      <td>41600144</td>\n", "      <td>0041600144</td>\n", "      <td>2017-04-24</td>\n", "      <td>2016</td>\n", "      <td>3</td>\n", "      <td>Final</td>\n", "      <td>26</td>\n", "      <td>452</td>\n", "      <td>171.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>79 rows × 9 columns</p>\n", "</div>"], "text/plain": ["     game_id game_id_string game_date_est  season  game_status_id status  \\\n", "0   41600405     0041600405    2017-06-12    2016               3  Final   \n", "1   41600402     0041600402    2017-06-04    2016               3  Final   \n", "2   41600401     0041600401    2017-06-01    2016               3  Final   \n", "3   41600305     0041600305    2017-05-25    2016               3  Final   \n", "4   41600304     0041600304    2017-05-23    2016               3  Final   \n", "..       ...            ...           ...     ...             ...    ...   \n", "74  41600404     0041600404    2017-06-09    2016               3  Final   \n", "75  41600164     0041600164    2017-04-23    2016               3  Final   \n", "76  41600202     0041600202    2017-05-02    2016               3  Final   \n", "77  41600403     0041600403    2017-06-07    2016               3  Final   \n", "78  41600144     0041600144    2017-04-24    2016               3  Final   \n", "\n", "    num_box_score  num_play_by_play  num_shot_chart  \n", "0              26               465           178.0  \n", "1              26               486           189.0  \n", "2              26               510             NaN  \n", "3              26               444           171.0  \n", "4              26               392           153.0  \n", "..            ...               ...             ...  \n", "74             26               510             NaN  \n", "75             25               492             NaN  \n", "76             26               525             NaN  \n", "77             26               486           173.0  \n", "78             26               452           171.0  \n", "\n", "[79 rows x 9 columns]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["db.query(\"select * from nba_downloads where game_id>0041600000 and season=2016\")"]}, {"cell_type": "code", "execution_count": 21, "id": "66d709f7", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'resource': 'boxscore',\n", " 'parameters': {'GameID': '0041600405',\n", "  'StartPeriod': 0,\n", "  'EndPeriod': 0,\n", "  'StartRange': 0,\n", "  'EndRange': 0,\n", "  'RangeType': 0},\n", " 'resultSets': [{'name': 'PlayerStats',\n", "   'headers': ['GAME_ID',\n", "    'TEAM_ID',\n", "    'TEAM_ABBREVIATION',\n", "    'TEAM_CITY',\n", "    'PLAYER_ID',\n", "    'PLAYER_NAME',\n", "    'NICKNAME',\n", "    'START_POSITION',\n", "    'COMMENT',\n", "    'MIN',\n", "    'FGM',\n", "    'FGA',\n", "    'FG_PCT',\n", "    'FG3M',\n", "    'FG3A',\n", "    'FG3_PCT',\n", "    'FTM',\n", "    'FTA',\n", "    'FT_PCT',\n", "    'OREB',\n", "    'DREB',\n", "    'REB',\n", "    'AST',\n", "    'STL',\n", "    'BLK',\n", "    'TO',\n", "    'PF',\n", "    'PTS',\n", "    'PLUS_MINUS'],\n", "   'rowSet': [['0041600405',\n", "     1610612739,\n", "     'CLE',\n", "     'Cleveland',\n", "     2544,\n", "     '<PERSON><PERSON><PERSON>',\n", "     'Le<PERSON><PERSON>',\n", "     'F',\n", "     '',\n", "     '46:13',\n", "     19,\n", "     30,\n", "     0.633,\n", "     2,\n", "     5,\n", "     0.4,\n", "     1,\n", "     4,\n", "     0.25,\n", "     2,\n", "     11,\n", "     13,\n", "     8,\n", "     2,\n", "     1,\n", "     2,\n", "     3,\n", "     41,\n", "     -13.0],\n", "    ['0041600405',\n", "     1610612739,\n", "     'CLE',\n", "     'Cleveland',\n", "     201567,\n", "     '<PERSON>',\n", "     '<PERSON>',\n", "     'F',\n", "     '',\n", "     '29:55',\n", "     2,\n", "     8,\n", "     0.25,\n", "     0,\n", "     3,\n", "     0.0,\n", "     2,\n", "     5,\n", "     0.4,\n", "     3,\n", "     7,\n", "     10,\n", "     2,\n", "     0,\n", "     1,\n", "     0,\n", "     2,\n", "     6,\n", "     -23.0],\n", "    ['0041600405',\n", "     1610612739,\n", "     'CLE',\n", "     'Cleveland',\n", "     202684,\n", "     '<PERSON>',\n", "     'Tristan',\n", "     'C',\n", "     '',\n", "     '29:52',\n", "     6,\n", "     8,\n", "     0.75,\n", "     0,\n", "     0,\n", "     0.0,\n", "     3,\n", "     4,\n", "     0.75,\n", "     4,\n", "     4,\n", "     8,\n", "     3,\n", "     1,\n", "     1,\n", "     3,\n", "     1,\n", "     15,\n", "     -7.0],\n", "    ['0041600405',\n", "     1610612739,\n", "     'CLE',\n", "     'Cleveland',\n", "     2747,\n", "     '<PERSON>',\n", "     '<PERSON>',\n", "     'G',\n", "     '',\n", "     '40:49',\n", "     9,\n", "     11,\n", "     0.818,\n", "     7,\n", "     8,\n", "     0.875,\n", "     0,\n", "     1,\n", "     0.0,\n", "     0,\n", "     3,\n", "     3,\n", "     1,\n", "     0,\n", "     2,\n", "     0,\n", "     2,\n", "     25,\n", "     -2.0],\n", "    ['0041600405',\n", "     1610612739,\n", "     'CLE',\n", "     'Cleveland',\n", "     202681,\n", "     '<PERSON><PERSON><PERSON>',\n", "     '<PERSON><PERSON><PERSON>',\n", "     'G',\n", "     '',\n", "     '41:47',\n", "     9,\n", "     22,\n", "     0.409,\n", "     1,\n", "     2,\n", "     0.5,\n", "     7,\n", "     7,\n", "     1.0,\n", "     1,\n", "     1,\n", "     2,\n", "     6,\n", "     2,\n", "     0,\n", "     4,\n", "     3,\n", "     26,\n", "     4.0],\n", "    ['0041600405',\n", "     1610612739,\n", "     'CLE',\n", "     'Cleveland',\n", "     2210,\n", "     '<PERSON>',\n", "     'Richard',\n", "     '',\n", "     '',\n", "     '17:27',\n", "     1,\n", "     3,\n", "     0.333,\n", "     0,\n", "     1,\n", "     0.0,\n", "     2,\n", "     2,\n", "     1.0,\n", "     2,\n", "     0,\n", "     2,\n", "     0,\n", "     0,\n", "     0,\n", "     1,\n", "     4,\n", "     4,\n", "     3.0],\n", "    ['0041600405',\n", "     1610612739,\n", "     'CLE',\n", "     'Cleveland',\n", "     2594,\n", "     '<PERSON>',\n", "     '<PERSON>',\n", "     '',\n", "     '',\n", "     '17:48',\n", "     1,\n", "     3,\n", "     0.333,\n", "     1,\n", "     2,\n", "     0.5,\n", "     0,\n", "     0,\n", "     0.0,\n", "     0,\n", "     0,\n", "     0,\n", "     0,\n", "     0,\n", "     0,\n", "     1,\n", "     3,\n", "     3,\n", "     0.0],\n", "    ['0041600405',\n", "     1610612739,\n", "     'CLE',\n", "     'Cleveland',\n", "     101114,\n", "     '<PERSON><PERSON>',\n", "     'Deron',\n", "     '',\n", "     '',\n", "     '12:24',\n", "     0,\n", "     2,\n", "     0.0,\n", "     0,\n", "     2,\n", "     0.0,\n", "     0,\n", "     0,\n", "     0.0,\n", "     0,\n", "     2,\n", "     2,\n", "     2,\n", "     1,\n", "     0,\n", "     2,\n", "     1,\n", "     0,\n", "     2.0],\n", "    ['0041600405',\n", "     1610612739,\n", "     'CLE',\n", "     'Cleveland',\n", "     202697,\n", "     '<PERSON><PERSON>',\n", "     'Iman',\n", "     '',\n", "     '',\n", "     '3:45',\n", "     0,\n", "     1,\n", "     0.0,\n", "     0,\n", "     1,\n", "     0.0,\n", "     0,\n", "     0,\n", "     0.0,\n", "     0,\n", "     0,\n", "     0,\n", "     0,\n", "     0,\n", "     0,\n", "     1,\n", "     3,\n", "     0,\n", "     -9.0],\n", "    ['0041600405',\n", "     1610612739,\n", "     'CLE',\n", "     'Cleveland',\n", "     101112,\n", "     'Channing <PERSON>',\n", "     'Channing',\n", "     '',\n", "     \"DNP - Coach's Decision\",\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None],\n", "    ['0041600405',\n", "     1610612739,\n", "     'CLE',\n", "     'Cleveland',\n", "     2563,\n", "     '<PERSON><PERSON><PERSON><PERSON>',\n", "     '<PERSON><PERSON><PERSON><PERSON>',\n", "     '',\n", "     \"DNP - Coach's Decision\",\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None],\n", "    ['0041600405',\n", "     1610612739,\n", "     'CLE',\n", "     'Cleveland',\n", "     2592,\n", "     '<PERSON>',\n", "     '<PERSON>',\n", "     '',\n", "     \"DNP - Coach's Decision\",\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None],\n", "    ['0041600405',\n", "     1610612739,\n", "     'CLE',\n", "     'Cleveland',\n", "     202682,\n", "     '<PERSON>',\n", "     '<PERSON>',\n", "     '',\n", "     \"DNP - Coach's Decision\",\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None],\n", "    ['0041600405',\n", "     1610612744,\n", "     'GSW',\n", "     'Golden State',\n", "     201142,\n", "     '<PERSON>',\n", "     '<PERSON>',\n", "     'F',\n", "     '',\n", "     '40:15',\n", "     14,\n", "     20,\n", "     0.7,\n", "     5,\n", "     8,\n", "     0.625,\n", "     6,\n", "     6,\n", "     1.0,\n", "     1,\n", "     5,\n", "     6,\n", "     5,\n", "     1,\n", "     0,\n", "     4,\n", "     3,\n", "     39,\n", "     18.0],\n", "    ['0041600405',\n", "     1610612744,\n", "     'GSW',\n", "     'Golden State',\n", "     203110,\n", "     'Dr<PERSON>mond Green',\n", "     '<PERSON><PERSON><PERSON>',\n", "     'F',\n", "     '',\n", "     '43:43',\n", "     3,\n", "     10,\n", "     0.3,\n", "     2,\n", "     5,\n", "     0.4,\n", "     2,\n", "     4,\n", "     0.5,\n", "     3,\n", "     9,\n", "     12,\n", "     5,\n", "     2,\n", "     0,\n", "     0,\n", "     4,\n", "     10,\n", "     19.0],\n", "    ['0041600405',\n", "     1610612744,\n", "     'GSW',\n", "     'Golden State',\n", "     2585,\n", "     'Zaza Pachulia',\n", "     '<PERSON><PERSON><PERSON>',\n", "     'C',\n", "     '',\n", "     '9:53',\n", "     0,\n", "     1,\n", "     0.0,\n", "     0,\n", "     0,\n", "     0.0,\n", "     0,\n", "     0,\n", "     0.0,\n", "     3,\n", "     0,\n", "     3,\n", "     1,\n", "     0,\n", "     0,\n", "     2,\n", "     4,\n", "     0,\n", "     -9.0],\n", "    ['0041600405',\n", "     1610612744,\n", "     'GSW',\n", "     'Golden State',\n", "     202691,\n", "     '<PERSON><PERSON>',\n", "     '<PERSON><PERSON>',\n", "     'G',\n", "     '',\n", "     '34:05',\n", "     4,\n", "     13,\n", "     0.308,\n", "     3,\n", "     7,\n", "     0.429,\n", "     0,\n", "     0,\n", "     0.0,\n", "     1,\n", "     4,\n", "     5,\n", "     2,\n", "     1,\n", "     1,\n", "     0,\n", "     4,\n", "     11,\n", "     -6.0],\n", "    ['0041600405',\n", "     1610612744,\n", "     'GSW',\n", "     'Golden State',\n", "     201939,\n", "     '<PERSON>',\n", "     '<PERSON>',\n", "     'G',\n", "     '',\n", "     '40:48',\n", "     10,\n", "     20,\n", "     0.5,\n", "     2,\n", "     9,\n", "     0.222,\n", "     12,\n", "     15,\n", "     0.8,\n", "     2,\n", "     4,\n", "     6,\n", "     10,\n", "     3,\n", "     0,\n", "     4,\n", "     3,\n", "     34,\n", "     3.0],\n", "    ['0041600405',\n", "     1610612744,\n", "     'GSW',\n", "     'Golden State',\n", "     2738,\n", "     '<PERSON>',\n", "     '<PERSON>',\n", "     '',\n", "     '',\n", "     '38:07',\n", "     9,\n", "     14,\n", "     0.643,\n", "     2,\n", "     7,\n", "     0.286,\n", "     0,\n", "     0,\n", "     0.0,\n", "     1,\n", "     3,\n", "     4,\n", "     3,\n", "     0,\n", "     0,\n", "     1,\n", "     1,\n", "     20,\n", "     18.0],\n", "    ['0041600405',\n", "     1610612744,\n", "     'GSW',\n", "     'Golden State',\n", "     2733,\n", "     '<PERSON>',\n", "     '<PERSON>',\n", "     '',\n", "     '',\n", "     '10:02',\n", "     2,\n", "     3,\n", "     0.667,\n", "     0,\n", "     0,\n", "     0.0,\n", "     1,\n", "     1,\n", "     1.0,\n", "     0,\n", "     0,\n", "     0,\n", "     0,\n", "     0,\n", "     0,\n", "     0,\n", "     2,\n", "     5,\n", "     -6.0],\n", "    ['0041600405',\n", "     1610612744,\n", "     'GSW',\n", "     'Golden State',\n", "     1627775,\n", "     '<PERSON>',\n", "     'Patrick',\n", "     '',\n", "     '',\n", "     '11:32',\n", "     2,\n", "     5,\n", "     0.4,\n", "     0,\n", "     2,\n", "     0.0,\n", "     2,\n", "     2,\n", "     1.0,\n", "     2,\n", "     1,\n", "     3,\n", "     1,\n", "     1,\n", "     0,\n", "     1,\n", "     2,\n", "     6,\n", "     -3.0],\n", "    ['0041600405',\n", "     1610612744,\n", "     'GSW',\n", "     'Golden State',\n", "     2440,\n", "     '<PERSON>',\n", "     '<PERSON>',\n", "     '',\n", "     '',\n", "     '1:04',\n", "     0,\n", "     0,\n", "     0.0,\n", "     0,\n", "     0,\n", "     0.0,\n", "     0,\n", "     0,\n", "     0.0,\n", "     0,\n", "     0,\n", "     0,\n", "     0,\n", "     0,\n", "     0,\n", "     0,\n", "     0,\n", "     0,\n", "     -5.0],\n", "    ['0041600405',\n", "     1610612744,\n", "     'GSW',\n", "     'Golden State',\n", "     2561,\n", "     '<PERSON>',\n", "     '<PERSON>',\n", "     '',\n", "     '',\n", "     '10:31',\n", "     2,\n", "     4,\n", "     0.5,\n", "     0,\n", "     0,\n", "     0.0,\n", "     0,\n", "     0,\n", "     0.0,\n", "     0,\n", "     3,\n", "     3,\n", "     0,\n", "     0,\n", "     1,\n", "     1,\n", "     1,\n", "     4,\n", "     16.0],\n", "    ['0041600405',\n", "     1610612744,\n", "     'GSW',\n", "     'Golden State',\n", "     203546,\n", "     '<PERSON>',\n", "     '<PERSON>',\n", "     '',\n", "     \"DNP - Coach's Decision\",\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None],\n", "    ['0041600405',\n", "     1610612744,\n", "     'GSW',\n", "     'Golden State',\n", "     203949,\n", "     '<PERSON>',\n", "     '<PERSON>',\n", "     '',\n", "     \"DNP - Coach's Decision\",\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None],\n", "    ['0041600405',\n", "     1610612744,\n", "     'GSW',\n", "     'Golden State',\n", "     201580,\n", "     '<PERSON><PERSON><PERSON><PERSON> McGee',\n", "     '<PERSON><PERSON><PERSON><PERSON>',\n", "     '',\n", "     \"DNP - Coach's Decision\",\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None,\n", "     None]]},\n", "  {'name': 'TeamStats',\n", "   'headers': ['GAME_ID',\n", "    'TEAM_ID',\n", "    'TEAM_NAME',\n", "    'TEAM_ABBREVIATION',\n", "    'TEAM_CITY',\n", "    'MIN',\n", "    'FGM',\n", "    'FGA',\n", "    'FG_PCT',\n", "    'FG3M',\n", "    'FG3A',\n", "    'FG3_PCT',\n", "    'FTM',\n", "    'FTA',\n", "    'FT_PCT',\n", "    'OREB',\n", "    'DREB',\n", "    'REB',\n", "    'AST',\n", "    'STL',\n", "    'BLK',\n", "    'TO',\n", "    'PF',\n", "    'PTS',\n", "    'PLUS_MINUS'],\n", "   'rowSet': [['0041600405',\n", "     1610612744,\n", "     'Warriors',\n", "     'GSW',\n", "     'Golden State',\n", "     '240:00',\n", "     46,\n", "     90,\n", "     0.511,\n", "     14,\n", "     38,\n", "     0.368,\n", "     23,\n", "     28,\n", "     0.821,\n", "     13,\n", "     29,\n", "     42,\n", "     27,\n", "     8,\n", "     2,\n", "     13,\n", "     24,\n", "     129,\n", "     9.0],\n", "    ['0041600405',\n", "     1610612739,\n", "     'Cavaliers',\n", "     'CLE',\n", "     'Cleveland',\n", "     '240:00',\n", "     47,\n", "     88,\n", "     0.534,\n", "     11,\n", "     24,\n", "     0.458,\n", "     15,\n", "     23,\n", "     0.652,\n", "     12,\n", "     28,\n", "     40,\n", "     22,\n", "     6,\n", "     5,\n", "     14,\n", "     22,\n", "     120,\n", "     -9.0]]},\n", "  {'name': 'TeamStarterBenchStats',\n", "   'headers': ['GAME_ID',\n", "    'TEAM_ID',\n", "    'TEAM_NAME',\n", "    'TEAM_ABBREVIATION',\n", "    'TEAM_CITY',\n", "    'STARTERS_BENCH',\n", "    'MIN',\n", "    'FGM',\n", "    'FGA',\n", "    'FG_PCT',\n", "    'FG3M',\n", "    'FG3A',\n", "    'FG3_PCT',\n", "    'FTM',\n", "    'FTA',\n", "    'FT_PCT',\n", "    'OREB',\n", "    'DREB',\n", "    'REB',\n", "    'AST',\n", "    'STL',\n", "    'BLK',\n", "    'TO',\n", "    'PF',\n", "    'PTS'],\n", "   'rowSet': [['0041600405',\n", "     1610612744,\n", "     'Warriors',\n", "     'GSW',\n", "     'Golden State',\n", "     'Starters',\n", "     '168:44',\n", "     31,\n", "     64,\n", "     0.484,\n", "     12,\n", "     29,\n", "     0.414,\n", "     20,\n", "     25,\n", "     0.8,\n", "     10,\n", "     22,\n", "     32,\n", "     23,\n", "     7,\n", "     1,\n", "     10,\n", "     18,\n", "     94],\n", "    ['0041600405',\n", "     1610612744,\n", "     'Warriors',\n", "     'GSW',\n", "     'Golden State',\n", "     'Bench',\n", "     '60:10',\n", "     13,\n", "     23,\n", "     0.565,\n", "     2,\n", "     9,\n", "     0.222,\n", "     2,\n", "     2,\n", "     1.0,\n", "     3,\n", "     7,\n", "     10,\n", "     4,\n", "     1,\n", "     1,\n", "     3,\n", "     4,\n", "     30],\n", "    ['0041600405',\n", "     1610612739,\n", "     'Cavaliers',\n", "     'CLE',\n", "     'Cleveland',\n", "     'Starters',\n", "     '188:36',\n", "     45,\n", "     79,\n", "     0.57,\n", "     10,\n", "     18,\n", "     0.556,\n", "     13,\n", "     21,\n", "     0.619,\n", "     10,\n", "     26,\n", "     36,\n", "     20,\n", "     5,\n", "     5,\n", "     9,\n", "     11,\n", "     113],\n", "    ['0041600405',\n", "     1610612739,\n", "     'Cavaliers',\n", "     'CLE',\n", "     'Cleveland',\n", "     'Bench',\n", "     '30:12',\n", "     1,\n", "     5,\n", "     0.2,\n", "     1,\n", "     4,\n", "     0.25,\n", "     0,\n", "     0,\n", "     0.0,\n", "     0,\n", "     2,\n", "     2,\n", "     2,\n", "     1,\n", "     0,\n", "     3,\n", "     4,\n", "     3]]}]}"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["Boxscore(game_id='0041600405', force_fetch=True).json"]}, {"cell_type": "code", "execution_count": 25, "id": "682805bb", "metadata": {}, "outputs": [], "source": ["p = PlayByPlay(game_id='0041600405', force_fetch=True)"]}, {"cell_type": "code", "execution_count": 26, "id": "ea020aea", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>game_id</th>\n", "      <th>eventnum</th>\n", "      <th>eventmsgtype</th>\n", "      <th>eventmsgactiontype</th>\n", "      <th>period</th>\n", "      <th>wctimestring</th>\n", "      <th>pctimestring</th>\n", "      <th>homedescription</th>\n", "      <th>neutraldescription</th>\n", "      <th>visitordescription</th>\n", "      <th>...</th>\n", "      <th>player2_team_nickname</th>\n", "      <th>player2_team_abbreviation</th>\n", "      <th>person3type</th>\n", "      <th>player3_id</th>\n", "      <th>player3_name</th>\n", "      <th>player3_team_id</th>\n", "      <th>player3_team_city</th>\n", "      <th>player3_team_nickname</th>\n", "      <th>player3_team_abbreviation</th>\n", "      <th>video_available_flag</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0041600405</td>\n", "      <td>0</td>\n", "      <td>12</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>9:11 PM</td>\n", "      <td>12:00</td>\n", "      <td>None</td>\n", "      <td>Start of 1st Period (9:11 PM EST)</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0041600405</td>\n", "      <td>1</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>9:11 PM</td>\n", "      <td>12:00</td>\n", "      <td>Jump Ball Pachul<PERSON> vs. <PERSON>: Tip to Love</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>Cavaliers</td>\n", "      <td>CLE</td>\n", "      <td>5</td>\n", "      <td>201567</td>\n", "      <td><PERSON></td>\n", "      <td>1.610613e+09</td>\n", "      <td>Cleveland</td>\n", "      <td>Cavaliers</td>\n", "      <td>CLE</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0041600405</td>\n", "      <td>2</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>9:11 PM</td>\n", "      <td>11:48</td>\n", "      <td>Thompson <PERSON>FOUL (P1.T1) (<PERSON><PERSON>)</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>Cavaliers</td>\n", "      <td>CLE</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0041600405</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>42</td>\n", "      <td>1</td>\n", "      <td>9:12 PM</td>\n", "      <td>11:42</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Thompson 1' Driving Layup (2 PTS)</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0041600405</td>\n", "      <td>4</td>\n", "      <td>6</td>\n", "      <td>17</td>\n", "      <td>1</td>\n", "      <td>9:12 PM</td>\n", "      <td>11:30</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Cavaliers <PERSON><PERSON> (Def. 3 Sec Thompson ) (D.Cra...</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>461</th>\n", "      <td>0041600405</td>\n", "      <td>535</td>\n", "      <td>1</td>\n", "      <td>42</td>\n", "      <td>4</td>\n", "      <td>11:44 PM</td>\n", "      <td>0:36</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td><PERSON> 2' Driving Layup (41 PTS)</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>462</th>\n", "      <td>0041600405</td>\n", "      <td>536</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>11:44 PM</td>\n", "      <td>0:20</td>\n", "      <td>MI<PERSON> Curry 24' 3PT Jump Shot</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>463</th>\n", "      <td>0041600405</td>\n", "      <td>537</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>11:44 PM</td>\n", "      <td>0:19</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Thompson REBOUND (Off:4 Def:4)</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>464</th>\n", "      <td>0041600405</td>\n", "      <td>538</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>11:44 PM</td>\n", "      <td>0:15</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>Smith 24' 3PT Jump Shot (25 PTS) (<PERSON> 8 AST)</td>\n", "      <td>...</td>\n", "      <td>Cavaliers</td>\n", "      <td>CLE</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>465</th>\n", "      <td>0041600405</td>\n", "      <td>539</td>\n", "      <td>13</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>11:45 PM</td>\n", "      <td>0:00</td>\n", "      <td>None</td>\n", "      <td>End of 4th Period (11:45 PM EST)</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>466 rows × 34 columns</p>\n", "</div>"], "text/plain": ["        game_id  eventnum  eventmsgtype  eventmsgactiontype  period  \\\n", "0    0041600405         0            12                   0       1   \n", "1    0041600405         1            10                   0       1   \n", "2    0041600405         2             6                   1       1   \n", "3    0041600405         3             1                  42       1   \n", "4    0041600405         4             6                  17       1   \n", "..          ...       ...           ...                 ...     ...   \n", "461  0041600405       535             1                  42       4   \n", "462  0041600405       536             2                   1       4   \n", "463  0041600405       537             4                   0       4   \n", "464  0041600405       538             1                   1       4   \n", "465  0041600405       539            13                   0       4   \n", "\n", "    wctimestring pctimestring                               homedescription  \\\n", "0        9:11 PM        12:00                                          None   \n", "1        9:11 PM        12:00  Jump Ball <PERSON> vs. <PERSON>: Tip to <PERSON>   \n", "2        9:11 PM        11:48          <PERSON>.FOUL (P1.T1) (<PERSON><PERSON>)   \n", "3        9:12 PM        11:42                                          None   \n", "4        9:12 PM        11:30                                          None   \n", "..           ...          ...                                           ...   \n", "461     11:44 PM         0:36                                          None   \n", "462     11:44 PM         0:20                  MISS Curry 24' 3PT Jump Shot   \n", "463     11:44 PM         0:19                                          None   \n", "464     11:44 PM         0:15                                          None   \n", "465     11:45 PM         0:00                                          None   \n", "\n", "                    neutraldescription  \\\n", "0    Start of 1st Period (9:11 PM EST)   \n", "1                                 None   \n", "2                                 None   \n", "3                                 None   \n", "4                                 None   \n", "..                                 ...   \n", "461                               None   \n", "462                               None   \n", "463                               None   \n", "464                               None   \n", "465   End of 4th Period (11:45 PM EST)   \n", "\n", "                                    visitordescription  ...  \\\n", "0                                                 None  ...   \n", "1                                                 None  ...   \n", "2                                                 None  ...   \n", "3                    Thompson 1' Driving Layup (2 PTS)  ...   \n", "4    Cavaliers <PERSON><PERSON> (Def. 3 <PERSON><PERSON> Thompson ) (D.Cra...  ...   \n", "..                                                 ...  ...   \n", "461                    <PERSON> 2' Driving Layup (41 PTS)  ...   \n", "462                                               None  ...   \n", "463                     Thompson REBOUND (Off:4 Def:4)  ...   \n", "464     <PERSON> 24' 3PT Jump Shot (25 PTS) (<PERSON> 8 AST)  ...   \n", "465                                               None  ...   \n", "\n", "    player2_team_nickname player2_team_abbreviation  person3type  player3_id  \\\n", "0                    None                      None            0           0   \n", "1               Cavaliers                       CLE            5      201567   \n", "2               Cavaliers                       CLE            1           0   \n", "3                    None                      None            0           0   \n", "4                    None                      None            1           0   \n", "..                    ...                       ...          ...         ...   \n", "461                  None                      None            0           0   \n", "462                  None                      None            0           0   \n", "463                  None                      None            0           0   \n", "464             Cavaliers                       CLE            0           0   \n", "465                  None                      None            0           0   \n", "\n", "    player3_name  player3_team_id player3_team_city player3_team_nickname  \\\n", "0           None              NaN              None                  None   \n", "1     <PERSON>     1.610613e+09         Cleveland             Cavaliers   \n", "2           None              NaN              None                  None   \n", "3           None              NaN              None                  None   \n", "4           None              NaN              None                  None   \n", "..           ...              ...               ...                   ...   \n", "461         None              NaN              None                  None   \n", "462         None              NaN              None                  None   \n", "463         None              NaN              None                  None   \n", "464         None              NaN              None                  None   \n", "465         None              NaN              None                  None   \n", "\n", "    player3_team_abbreviation  video_available_flag  \n", "0                        None                     0  \n", "1                         CLE                     1  \n", "2                        None                     1  \n", "3                        None                     1  \n", "4                        None                     1  \n", "..                        ...                   ...  \n", "461                      None                     1  \n", "462                      None                     1  \n", "463                      None                     1  \n", "464                      None                     1  \n", "465                      None                     1  \n", "\n", "[466 rows x 34 columns]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["p.dataframe"]}, {"cell_type": "code", "execution_count": 35, "id": "3e365654", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["00\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>grid_type</th>\n", "      <th>game_id</th>\n", "      <th>game_event_id</th>\n", "      <th>player_id</th>\n", "      <th>player_name</th>\n", "      <th>team_id</th>\n", "      <th>team_name</th>\n", "      <th>period</th>\n", "      <th>minutes_remaining</th>\n", "      <th>seconds_remaining</th>\n", "      <th>...</th>\n", "      <th>shot_zone_area</th>\n", "      <th>shot_zone_range</th>\n", "      <th>shot_distance</th>\n", "      <th>loc_x</th>\n", "      <th>loc_y</th>\n", "      <th>shot_attempted_flag</th>\n", "      <th>shot_made_flag</th>\n", "      <th>game_date</th>\n", "      <th>htm</th>\n", "      <th>vtm</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Shot Chart Detail</td>\n", "      <td>0020000047</td>\n", "      <td>2</td>\n", "      <td>1722</td>\n", "      <td><PERSON></td>\n", "      <td>1610612763</td>\n", "      <td>Vancouver Grizzlies</td>\n", "      <td>1</td>\n", "      <td>11</td>\n", "      <td>44</td>\n", "      <td>...</td>\n", "      <td>Left Side(L)</td>\n", "      <td>8-16 ft.</td>\n", "      <td>15</td>\n", "      <td>-132</td>\n", "      <td>76</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>20001104</td>\n", "      <td>VAN</td>\n", "      <td>LAL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Shot Chart Detail</td>\n", "      <td>0020000047</td>\n", "      <td>4</td>\n", "      <td>977</td>\n", "      <td><PERSON></td>\n", "      <td>1610612747</td>\n", "      <td>Los Angeles Lakers</td>\n", "      <td>1</td>\n", "      <td>11</td>\n", "      <td>26</td>\n", "      <td>...</td>\n", "      <td>Right Side(R)</td>\n", "      <td>16-24 ft.</td>\n", "      <td>17</td>\n", "      <td>163</td>\n", "      <td>76</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>20001104</td>\n", "      <td>VAN</td>\n", "      <td>LAL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Shot Chart Detail</td>\n", "      <td>0020000047</td>\n", "      <td>6</td>\n", "      <td>1710</td>\n", "      <td><PERSON></td>\n", "      <td>1610612763</td>\n", "      <td>Vancouver Grizzlies</td>\n", "      <td>1</td>\n", "      <td>11</td>\n", "      <td>20</td>\n", "      <td>...</td>\n", "      <td>Left Side Center(LC)</td>\n", "      <td>16-24 ft.</td>\n", "      <td>21</td>\n", "      <td>-153</td>\n", "      <td>150</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>20001104</td>\n", "      <td>VAN</td>\n", "      <td>LAL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Shot Chart Detail</td>\n", "      <td>0020000047</td>\n", "      <td>8</td>\n", "      <td>977</td>\n", "      <td><PERSON></td>\n", "      <td>1610612747</td>\n", "      <td>Los Angeles Lakers</td>\n", "      <td>1</td>\n", "      <td>10</td>\n", "      <td>58</td>\n", "      <td>...</td>\n", "      <td>Right Side Center(RC)</td>\n", "      <td>16-24 ft.</td>\n", "      <td>20</td>\n", "      <td>70</td>\n", "      <td>194</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>20001104</td>\n", "      <td>VAN</td>\n", "      <td>LAL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Shot Chart Detail</td>\n", "      <td>0020000047</td>\n", "      <td>10</td>\n", "      <td>970</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>1610612763</td>\n", "      <td>Vancouver Grizzlies</td>\n", "      <td>1</td>\n", "      <td>10</td>\n", "      <td>49</td>\n", "      <td>...</td>\n", "      <td>Center(C)</td>\n", "      <td>Less Than 8 ft.</td>\n", "      <td>1</td>\n", "      <td>-18</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>20001104</td>\n", "      <td>VAN</td>\n", "      <td>LAL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>149</th>\n", "      <td>Shot Chart Detail</td>\n", "      <td>0020000047</td>\n", "      <td>449</td>\n", "      <td>1710</td>\n", "      <td><PERSON></td>\n", "      <td>1610612763</td>\n", "      <td>Vancouver Grizzlies</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>Center(C)</td>\n", "      <td>24+ ft.</td>\n", "      <td>27</td>\n", "      <td>-18</td>\n", "      <td>278</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>20001104</td>\n", "      <td>VAN</td>\n", "      <td>LAL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>150</th>\n", "      <td>Shot Chart Detail</td>\n", "      <td>0020000047</td>\n", "      <td>456</td>\n", "      <td>1800</td>\n", "      <td><PERSON></td>\n", "      <td>1610612763</td>\n", "      <td>Vancouver Grizzlies</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>33</td>\n", "      <td>...</td>\n", "      <td>Center(C)</td>\n", "      <td>Less Than 8 ft.</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>20001104</td>\n", "      <td>VAN</td>\n", "      <td>LAL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>151</th>\n", "      <td>Shot Chart Detail</td>\n", "      <td>0020000047</td>\n", "      <td>462</td>\n", "      <td>949</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>1610612763</td>\n", "      <td>Vancouver Grizzlies</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>21</td>\n", "      <td>...</td>\n", "      <td>Center(C)</td>\n", "      <td>Less Than 8 ft.</td>\n", "      <td>0</td>\n", "      <td>-4</td>\n", "      <td>9</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>20001104</td>\n", "      <td>VAN</td>\n", "      <td>LAL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>152</th>\n", "      <td>Shot Chart Detail</td>\n", "      <td>0020000047</td>\n", "      <td>464</td>\n", "      <td>949</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>1610612763</td>\n", "      <td>Vancouver Grizzlies</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>15</td>\n", "      <td>...</td>\n", "      <td>Right Side Center(RC)</td>\n", "      <td>16-24 ft.</td>\n", "      <td>19</td>\n", "      <td>108</td>\n", "      <td>167</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>20001104</td>\n", "      <td>VAN</td>\n", "      <td>LAL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>153</th>\n", "      <td>Shot Chart Detail</td>\n", "      <td>0020000047</td>\n", "      <td>468</td>\n", "      <td>1800</td>\n", "      <td><PERSON></td>\n", "      <td>1610612763</td>\n", "      <td>Vancouver Grizzlies</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>...</td>\n", "      <td>Right Side Center(RC)</td>\n", "      <td>24+ ft.</td>\n", "      <td>25</td>\n", "      <td>184</td>\n", "      <td>171</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>20001104</td>\n", "      <td>VAN</td>\n", "      <td>LAL</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>154 rows × 24 columns</p>\n", "</div>"], "text/plain": ["             grid_type     game_id  game_event_id  player_id  \\\n", "0    Shot Chart Detail  0020000047              2       1722   \n", "1    Shot Chart Detail  0020000047              4        977   \n", "2    Shot Chart Detail  0020000047              6       1710   \n", "3    Shot Chart Detail  0020000047              8        977   \n", "4    Shot Chart Detail  0020000047             10        970   \n", "..                 ...         ...            ...        ...   \n", "149  Shot Chart Detail  0020000047            449       1710   \n", "150  Shot Chart Detail  0020000047            456       1800   \n", "151  Shot Chart Detail  0020000047            462        949   \n", "152  Shot Chart Detail  0020000047            464        949   \n", "153  Shot Chart Detail  0020000047            468       1800   \n", "\n", "             player_name     team_id            team_name  period  \\\n", "0      <PERSON>  1610612763  Vancouver Grizzlies       1   \n", "1            <PERSON>  1610612747   Los Angeles Lakers       1   \n", "2             <PERSON>  1610612763  Vancouver Grizzlies       1   \n", "3            <PERSON> Bryant  1610612747   Los Angeles Lakers       1   \n", "4     <PERSON><PERSON><PERSON> Harrington  1610612763  Vancouver Grizzlies       1   \n", "..                   ...         ...                  ...     ...   \n", "149           <PERSON>  1610612763  Vancouver Grizzlies       4   \n", "150          <PERSON>  1610612763  Vancouver Grizzlies       4   \n", "151  <PERSON><PERSON><PERSON><PERSON><PERSON>  1610612763  Vancouver Grizzlies       4   \n", "152  <PERSON><PERSON><PERSON><PERSON><PERSON>  1610612763  Vancouver Grizzlies       4   \n", "153          <PERSON>  1610612763  Vancouver Grizzlies       4   \n", "\n", "     minutes_remaining  seconds_remaining  ...         shot_zone_area  \\\n", "0                   11                 44  ...           Left Side(L)   \n", "1                   11                 26  ...          Right Side(R)   \n", "2                   11                 20  ...   Left Side Center(LC)   \n", "3                   10                 58  ...  Right Side Center(RC)   \n", "4                   10                 49  ...              Center(C)   \n", "..                 ...                ...  ...                    ...   \n", "149                  1                  1  ...              Center(C)   \n", "150                  0                 33  ...              Center(C)   \n", "151                  0                 21  ...              Center(C)   \n", "152                  0                 15  ...  Right Side Center(RC)   \n", "153                  0                  3  ...  Right Side Center(RC)   \n", "\n", "     shot_zone_range shot_distance loc_x loc_y shot_attempted_flag  \\\n", "0           8-16 ft.            15  -132    76                   1   \n", "1          16-24 ft.            17   163    76                   1   \n", "2          16-24 ft.            21  -153   150                   1   \n", "3          16-24 ft.            20    70   194                   1   \n", "4    Less Than 8 ft.             1   -18     0                   1   \n", "..               ...           ...   ...   ...                 ...   \n", "149          24+ ft.            27   -18   278                   1   \n", "150  Less Than 8 ft.             0     1     4                   1   \n", "151  Less Than 8 ft.             0    -4     9                   1   \n", "152        16-24 ft.            19   108   167                   1   \n", "153          24+ ft.            25   184   171                   1   \n", "\n", "     shot_made_flag  game_date  htm  vtm  \n", "0                 0   20001104  VAN  LAL  \n", "1                 0   20001104  VAN  LAL  \n", "2                 0   20001104  VAN  LAL  \n", "3                 0   20001104  VAN  LAL  \n", "4                 1   20001104  VAN  LAL  \n", "..              ...        ...  ...  ...  \n", "149               0   20001104  VAN  LAL  \n", "150               0   20001104  VAN  LAL  \n", "151               1   20001104  VAN  LAL  \n", "152               1   20001104  VAN  LAL  \n", "153               0   20001104  VAN  LAL  \n", "\n", "[154 rows x 24 columns]"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["ShotChart(game_id='0020000047', force_fetch=True).dataframe"]}, {"cell_type": "code", "execution_count": 28, "id": "bf93a2be", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 's1948' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-28-de658d7ba0f2>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0ms1948\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mjson\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mNameError\u001b[0m: name 's1948' is not defined"]}], "source": ["s1948.json"]}, {"cell_type": "code", "execution_count": 63, "id": "2e89d5c0", "metadata": {}, "outputs": [], "source": ["db.delete(\"delete from nba_downloads where game_id=42100402\")"]}, {"cell_type": "code", "execution_count": 13, "id": "92547605", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>game_id_string</th>\n", "      <th>season</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0022100001</td>\n", "      <td>2021</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0022100002</td>\n", "      <td>2021</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0022100003</td>\n", "      <td>2021</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0022100004</td>\n", "      <td>2021</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0022100005</td>\n", "      <td>2021</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1320</th>\n", "      <td>0052100111</td>\n", "      <td>2021</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1321</th>\n", "      <td>0052100121</td>\n", "      <td>2021</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1322</th>\n", "      <td>0052100131</td>\n", "      <td>2021</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1323</th>\n", "      <td>0052100201</td>\n", "      <td>2021</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1324</th>\n", "      <td>0052100211</td>\n", "      <td>2021</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1325 rows × 2 columns</p>\n", "</div>"], "text/plain": ["     game_id_string  season\n", "0        0022100001    2021\n", "1        0022100002    2021\n", "2        0022100003    2021\n", "3        0022100004    2021\n", "4        0022100005    2021\n", "...             ...     ...\n", "1320     0052100111    2021\n", "1321     0052100121    2021\n", "1322     0052100131    2021\n", "1323     0052100201    2021\n", "1324     0052100211    2021\n", "\n", "[1325 rows x 2 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["season=2021\n", "sql = f\"SELECT DISTINCT(game_id_string), season FROM nba_downloads WHERE season={season};\"\n", "db.query(sql)\n"]}, {"cell_type": "code", "execution_count": 77, "id": "5b22986c", "metadata": {}, "outputs": [{"ename": "ResourceClosedError", "evalue": "This result object does not return rows. It has been closed automatically.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mResourceClosedError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-77-e0f6453e7246>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mdb\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mquery\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"DROP TABLE nba_downloads\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m~/code/michael-quinlan/repo/bazel-bin/jupyter/jupyter.runfiles/repo/sports/basketball/database/database.py\u001b[0m in \u001b[0;36mquery\u001b[0;34m(self, sql)\u001b[0m\n\u001b[1;32m     49\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mquery\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0msql\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     50\u001b[0m         \u001b[0;31m# Returns a pandas dataframe\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 51\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0msqlio\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mread_sql_query\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0msql\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_conn\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     52\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     53\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mdelete\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0msql\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/code/michael-quinlan/repo/bazel-bin/jupyter/jupyter.runfiles/third_party_pypi__pandas/pandas/io/sql.py\u001b[0m in \u001b[0;36mread_sql_query\u001b[0;34m(sql, con, index_col, coerce_float, params, parse_dates, chunksize)\u001b[0m\n\u001b[1;32m    392\u001b[0m         \u001b[0mcoerce_float\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mcoerce_float\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    393\u001b[0m         \u001b[0mparse_dates\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mparse_dates\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 394\u001b[0;31m         \u001b[0mchunksize\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mchunksize\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    395\u001b[0m     )\n\u001b[1;32m    396\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/code/michael-quinlan/repo/bazel-bin/jupyter/jupyter.runfiles/third_party_pypi__pandas/pandas/io/sql.py\u001b[0m in \u001b[0;36mread_query\u001b[0;34m(self, sql, index_col, coerce_float, parse_dates, params, chunksize)\u001b[0m\n\u001b[1;32m   1319\u001b[0m             )\n\u001b[1;32m   1320\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1321\u001b[0;31m             \u001b[0mdata\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mresult\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfetchall\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1322\u001b[0m             frame = _wrap_result(\n\u001b[1;32m   1323\u001b[0m                 \u001b[0mdata\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/code/michael-quinlan/repo/bazel-bin/jupyter/jupyter.runfiles/third_party_pypi__sqlalchemy/sqlalchemy/engine/result.py\u001b[0m in \u001b[0;36mfetchall\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    981\u001b[0m         \u001b[0;34m\"\"\"A synonym for the :meth:`_engine.Result.all` method.\"\"\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    982\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 983\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_allrows\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    984\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    985\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mfetchone\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/code/michael-quinlan/repo/bazel-bin/jupyter/jupyter.runfiles/third_party_pypi__sqlalchemy/sqlalchemy/engine/result.py\u001b[0m in \u001b[0;36m_allrows\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    396\u001b[0m         \u001b[0mpost_creational_filter\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_post_creational_filter\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    397\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 398\u001b[0;31m         \u001b[0mmake_row\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_row_getter\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    399\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    400\u001b[0m         \u001b[0mrows\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_fetchall_impl\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/code/michael-quinlan/repo/bazel-bin/jupyter/jupyter.runfiles/third_party_pypi__sqlalchemy/sqlalchemy/util/langhelpers.py\u001b[0m in \u001b[0;36m__get__\u001b[0;34m(self, obj, cls)\u001b[0m\n\u001b[1;32m   1162\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0mobj\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1163\u001b[0m                 \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1164\u001b[0;31m             \u001b[0mobj\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__dict__\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__name__\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mresult\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfget\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mobj\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1165\u001b[0m             \u001b[0mobj\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_memoized_keys\u001b[0m \u001b[0;34m|=\u001b[0m \u001b[0;34m{\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__name__\u001b[0m\u001b[0;34m}\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1166\u001b[0m             \u001b[0;32mreturn\u001b[0m \u001b[0mresult\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/code/michael-quinlan/repo/bazel-bin/jupyter/jupyter.runfiles/third_party_pypi__sqlalchemy/sqlalchemy/engine/result.py\u001b[0m in \u001b[0;36m_row_getter\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    317\u001b[0m         \u001b[0mmetadata\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_metadata\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    318\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 319\u001b[0;31m         \u001b[0mkeymap\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mmetadata\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_keymap\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    320\u001b[0m         \u001b[0mprocessors\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mmetadata\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_processors\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    321\u001b[0m         \u001b[0mtf\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mmetadata\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_tuplefilter\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/code/michael-quinlan/repo/bazel-bin/jupyter/jupyter.runfiles/third_party_pypi__sqlalchemy/sqlalchemy/engine/cursor.py\u001b[0m in \u001b[0;36m_keymap\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1195\u001b[0m     \u001b[0;34m@\u001b[0m\u001b[0mproperty\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1196\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0m_keymap\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1197\u001b[0;31m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_we_dont_return_rows\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1198\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1199\u001b[0m     \u001b[0;34m@\u001b[0m\u001b[0mproperty\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/code/michael-quinlan/repo/bazel-bin/jupyter/jupyter.runfiles/third_party_pypi__sqlalchemy/sqlalchemy/engine/cursor.py\u001b[0m in \u001b[0;36m_we_dont_return_rows\u001b[0;34m(self, err)\u001b[0m\n\u001b[1;32m   1181\u001b[0m                 \u001b[0;34m\"It has been closed automatically.\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1182\u001b[0m             ),\n\u001b[0;32m-> 1183\u001b[0;31m             \u001b[0mreplace_context\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0merr\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1184\u001b[0m         )\n\u001b[1;32m   1185\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/code/micha<PERSON>-quinlan/repo/bazel-bin/jupyter/jupyter.runfiles/third_party_pypi__sqlalchemy/sqlalchemy/util/compat.py\u001b[0m in \u001b[0;36mraise_\u001b[0;34m(***failed resolving arguments***)\u001b[0m\n\u001b[1;32m    205\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    206\u001b[0m         \u001b[0;32mtry\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 207\u001b[0;31m             \u001b[0;32mraise\u001b[0m \u001b[0mexception\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    208\u001b[0m         \u001b[0;32mfinally\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    209\u001b[0m             \u001b[0;31m# credit to\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mResourceClosedError\u001b[0m: This result object does not return rows. It has been closed automatically."]}], "source": ["db.query(\"DROP TABLE nba_downloads\")"]}, {"cell_type": "code", "execution_count": 24, "id": "ad291582", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>game_id_string</th>\n", "      <th>season</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [game_id_string, season]\n", "Index: []"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["sql = f\"SELECT DISTINCT(game_id_string), season FROM nba_downloads WHERE season>1998 AND game_status_id=3 and num_box_score IS NULL;\"\n", "db.query(sql)"]}, {"cell_type": "code", "execution_count": 5, "id": "20ec4741", "metadata": {}, "outputs": [], "source": ["game_id = '0021900112'"]}, {"cell_type": "code", "execution_count": 10, "id": "40cd836c", "metadata": {}, "outputs": [], "source": ["bs = Boxscore(game_id=game_id, season=2019)"]}, {"cell_type": "code", "execution_count": 110, "id": "29e187e6", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>team_id</th>\n", "      <th>pts</th>\n", "      <th>fg_pct</th>\n", "      <th>ft_pct</th>\n", "      <th>fg3_pct</th>\n", "      <th>reb</th>\n", "      <th>ast</th>\n", "      <th>to</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1610612738</td>\n", "      <td>108</td>\n", "      <td>0.430</td>\n", "      <td>0.783</td>\n", "      <td>0.303</td>\n", "      <td>52</td>\n", "      <td>29</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1610612766</td>\n", "      <td>87</td>\n", "      <td>0.384</td>\n", "      <td>0.714</td>\n", "      <td>0.194</td>\n", "      <td>49</td>\n", "      <td>21</td>\n", "      <td>20</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      team_id  pts  fg_pct  ft_pct  fg3_pct  reb  ast  to\n", "0  1610612738  108   0.430   0.783    0.303   52   29  12\n", "1  1610612766   87   0.384   0.714    0.194   49   21  20"]}, "execution_count": 110, "metadata": {}, "output_type": "execute_result"}], "source": ["bs_team_summary=bs.dataframe_at(1).sort_values(by=['team_id'], ignore_index=True)[['team_id','pts','fg_pct','ft_pct','fg3_pct','reb','ast','to']]\n", "bs_team_summary\n"]}, {"cell_type": "code", "execution_count": 105, "id": "3ee784db", "metadata": {}, "outputs": [], "source": ["game_date = db.query(\"select game_date_est from nba_downloads where game_id_string='0021900112';\")['game_date_est'][0].date()\n", "    "]}, {"cell_type": "code", "execution_count": 113, "id": "e998e781", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>team_id</th>\n", "      <th>pts</th>\n", "      <th>fg_pct</th>\n", "      <th>ft_pct</th>\n", "      <th>fg3_pct</th>\n", "      <th>reb</th>\n", "      <th>ast</th>\n", "      <th>to</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1610612738</td>\n", "      <td>108</td>\n", "      <td>0.430</td>\n", "      <td>0.783</td>\n", "      <td>0.303</td>\n", "      <td>52</td>\n", "      <td>29</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1610612766</td>\n", "      <td>87</td>\n", "      <td>0.384</td>\n", "      <td>0.714</td>\n", "      <td>0.194</td>\n", "      <td>49</td>\n", "      <td>21</td>\n", "      <td>20</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      team_id  pts  fg_pct  ft_pct  fg3_pct  reb  ast  to\n", "0  1610612738  108   0.430   0.783    0.303   52   29  12\n", "1  1610612766   87   0.384   0.714    0.194   49   21  20"]}, "execution_count": 113, "metadata": {}, "output_type": "execute_result"}], "source": ["sc_team_summary = Scoreboard(day=game_date).dataframe_at(1)\n", "sc_team_summary=sc_team_summary[sc_team_summary.game_id==game_id].sort_values(by=['team_id'], ignore_index=True)[['team_id','pts','fg_pct','ft_pct','fg3_pct','reb','ast','tov']].rename(columns={\"tov\": \"to\"})\n", "sc_team_summary"]}, {"cell_type": "code", "execution_count": 115, "id": "1f151c14", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 115, "metadata": {}, "output_type": "execute_result"}], "source": ["sc_team_summary.equals(bs_team_summary)"]}, {"cell_type": "code", "execution_count": 120, "id": "b368d795", "metadata": {}, "outputs": [], "source": ["def validate_box_score_matches_scoreboard(game_id: str, season:int) -> bool:\n", "    game_date = db.query(\"select game_date_est from nba_downloads where game_id_string='0021900112';\")['game_date_est'][0].date()\n", "    bs = Boxscore(game_id=game_id, season=season)\n", "    sb = Scoreboard(game_date)\n", "    \n", "    bs_team_summary=bs.dataframe_at(1).sort_values(by=['team_id'], ignore_index=True)[['team_id','pts','fg_pct','ft_pct','fg3_pct','reb','ast','to']]\n", "    sc_team_summary = Scoreboard(day=game_date).dataframe_at(1)\n", "    sc_team_summary=sc_team_summary[sc_team_summary.game_id==game_id].sort_values(by=['team_id'], ignore_index=True)[['team_id','pts','fg_pct','ft_pct','fg3_pct','reb','ast','tov']].rename(columns={\"tov\": \"to\"})\n", "    return sc_team_summary.equals(bs_team_summary)"]}, {"cell_type": "code", "execution_count": 127, "id": "cb1feed6", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 127, "metadata": {}, "output_type": "execute_result"}], "source": ["validate_box_score_matches_scoreboard('0021900112', 2019)"]}, {"cell_type": "code", "execution_count": 42, "id": "b79b0fbe", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'sb' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-42-442924562e72>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0msb\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdataframe\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mNameError\u001b[0m: name 'sb' is not defined"]}], "source": ["sb.dataframe"]}, {"cell_type": "code", "execution_count": 164, "id": "57b7041d", "metadata": {}, "outputs": [], "source": ["def infer_season(game_id: str) -> int:\n", "    assert(len(game_id)==10)\n", "    return [int(year) for year in list(NBA_REGULAR_SEASON_DATES.keys()) if str(year).endswith(game_id[3:5])][0]"]}, {"cell_type": "code", "execution_count": 165, "id": "c3629d15", "metadata": {}, "outputs": [{"data": {"text/plain": ["2019"]}, "execution_count": 165, "metadata": {}, "output_type": "execute_result"}], "source": ["infer_season('0021900112')"]}, {"cell_type": "code", "execution_count": 141, "id": "72e8b83d", "metadata": {}, "outputs": [], "source": ["year = '0021900112'[3:5]"]}, {"cell_type": "code", "execution_count": 142, "id": "4981cb12", "metadata": {}, "outputs": [{"data": {"text/plain": ["'19'"]}, "execution_count": 142, "metadata": {}, "output_type": "execute_result"}], "source": ["year"]}, {"cell_type": "code", "execution_count": 147, "id": "3fcf7035", "metadata": {}, "outputs": [], "source": ["years=list(NBA_REGULAR_SEASON_DATES.keys())"]}, {"cell_type": "code", "execution_count": 152, "id": "caf758e9", "metadata": {}, "outputs": [{"data": {"text/plain": ["2019"]}, "execution_count": 152, "metadata": {}, "output_type": "execute_result"}], "source": ["[int(y) for y in years if str(y).endswith(year)][0]"]}, {"cell_type": "code", "execution_count": 66, "id": "a18c958c", "metadata": {}, "outputs": [], "source": ["def infer_season_type(game_id: str) -> str:\n", "    assert len(game_id) == 10\n", "    key_char = game_id[2]\n", "    if key_char == '4':\n", "        return PLAYOFFS_KEY\n", "    else:\n", "        return REGULAR_SEASON_KEY\n"]}, {"cell_type": "code", "execution_count": 68, "id": "2cf94b72", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'PLAYOFFS_KEY' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-68-e00928fbe0a5>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0minfer_season_type\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'0041900112'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m<ipython-input-66-2df2cd12fb0f>\u001b[0m in \u001b[0;36minfer_season_type\u001b[0;34m(game_id)\u001b[0m\n\u001b[1;32m      3\u001b[0m     \u001b[0mkey_char\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mgame_id\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m2\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      4\u001b[0m     \u001b[0;32mif\u001b[0m \u001b[0mkey_char\u001b[0m \u001b[0;34m==\u001b[0m \u001b[0;34m'4'\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 5\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mPLAYOFFS_KEY\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      6\u001b[0m     \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      7\u001b[0m         \u001b[0;32mreturn\u001b[0m \u001b[0mREGULAR_SEASON_KEY\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mNameError\u001b[0m: name 'PLAYOFFS_KEY' is not defined"]}], "source": ["infer_season_type('0041900112')"]}, {"cell_type": "code", "execution_count": 22, "id": "67766faf", "metadata": {}, "outputs": [{"data": {"text/plain": ["37710"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["get_nba_download_row_count(db)"]}, {"cell_type": "code", "execution_count": 21, "id": "4adceb39", "metadata": {}, "outputs": [{"ename": "ResourceClosedError", "evalue": "This result object does not return rows. It has been closed automatically.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mResourceClosedError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-21-c614fe568764>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mdb\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mquery\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"drop owned by pi;\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m~/code/michael-quinlan/repo/bazel-bin/jupyter/jupyter.runfiles/repo/sports/basketball/database/database.py\u001b[0m in \u001b[0;36mquery\u001b[0;34m(self, sql)\u001b[0m\n\u001b[1;32m     49\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mquery\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0msql\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     50\u001b[0m         \u001b[0;31m# Returns a pandas dataframe\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 51\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0msqlio\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mread_sql_query\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0msql\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_conn\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     52\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     53\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mdelete\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0msql\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/code/michael-quinlan/repo/bazel-bin/jupyter/jupyter.runfiles/third_party_pypi__pandas/pandas/io/sql.py\u001b[0m in \u001b[0;36mread_sql_query\u001b[0;34m(sql, con, index_col, coerce_float, params, parse_dates, chunksize)\u001b[0m\n\u001b[1;32m    386\u001b[0m     \"\"\"\n\u001b[1;32m    387\u001b[0m     \u001b[0mpandas_sql\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mpandasSQL_builder\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mcon\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 388\u001b[0;31m     return pandas_sql.read_query(\n\u001b[0m\u001b[1;32m    389\u001b[0m         \u001b[0msql\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    390\u001b[0m         \u001b[0mindex_col\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mindex_col\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/code/michael-quinlan/repo/bazel-bin/jupyter/jupyter.runfiles/third_party_pypi__pandas/pandas/io/sql.py\u001b[0m in \u001b[0;36mread_query\u001b[0;34m(self, sql, index_col, coerce_float, parse_dates, params, chunksize)\u001b[0m\n\u001b[1;32m   1319\u001b[0m             )\n\u001b[1;32m   1320\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1321\u001b[0;31m             \u001b[0mdata\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mresult\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfetchall\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1322\u001b[0m             frame = _wrap_result(\n\u001b[1;32m   1323\u001b[0m                 \u001b[0mdata\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/code/michael-quinlan/repo/bazel-bin/jupyter/jupyter.runfiles/third_party_pypi__sqlalchemy/sqlalchemy/engine/result.py\u001b[0m in \u001b[0;36mfetchall\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    981\u001b[0m         \u001b[0;34m\"\"\"A synonym for the :meth:`_engine.Result.all` method.\"\"\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    982\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 983\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_allrows\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    984\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    985\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mfetchone\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/code/michael-quinlan/repo/bazel-bin/jupyter/jupyter.runfiles/third_party_pypi__sqlalchemy/sqlalchemy/engine/result.py\u001b[0m in \u001b[0;36m_allrows\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    396\u001b[0m         \u001b[0mpost_creational_filter\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_post_creational_filter\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    397\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 398\u001b[0;31m         \u001b[0mmake_row\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_row_getter\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    399\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    400\u001b[0m         \u001b[0mrows\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_fetchall_impl\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/code/michael-quinlan/repo/bazel-bin/jupyter/jupyter.runfiles/third_party_pypi__sqlalchemy/sqlalchemy/util/langhelpers.py\u001b[0m in \u001b[0;36m__get__\u001b[0;34m(self, obj, cls)\u001b[0m\n\u001b[1;32m   1162\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0mobj\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1163\u001b[0m                 \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1164\u001b[0;31m             \u001b[0mobj\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__dict__\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__name__\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mresult\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfget\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mobj\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1165\u001b[0m             \u001b[0mobj\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_memoized_keys\u001b[0m \u001b[0;34m|=\u001b[0m \u001b[0;34m{\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__name__\u001b[0m\u001b[0;34m}\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1166\u001b[0m             \u001b[0;32mreturn\u001b[0m \u001b[0mresult\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/code/michael-quinlan/repo/bazel-bin/jupyter/jupyter.runfiles/third_party_pypi__sqlalchemy/sqlalchemy/engine/result.py\u001b[0m in \u001b[0;36m_row_getter\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    317\u001b[0m         \u001b[0mmetadata\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_metadata\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    318\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 319\u001b[0;31m         \u001b[0mkeymap\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mmetadata\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_keymap\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    320\u001b[0m         \u001b[0mprocessors\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mmetadata\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_processors\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    321\u001b[0m         \u001b[0mtf\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mmetadata\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_tuplefilter\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/code/michael-quinlan/repo/bazel-bin/jupyter/jupyter.runfiles/third_party_pypi__sqlalchemy/sqlalchemy/engine/cursor.py\u001b[0m in \u001b[0;36m_keymap\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1195\u001b[0m     \u001b[0;34m@\u001b[0m\u001b[0mproperty\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1196\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0m_keymap\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1197\u001b[0;31m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_we_dont_return_rows\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1198\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1199\u001b[0m     \u001b[0;34m@\u001b[0m\u001b[0mproperty\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/code/michael-quinlan/repo/bazel-bin/jupyter/jupyter.runfiles/third_party_pypi__sqlalchemy/sqlalchemy/engine/cursor.py\u001b[0m in \u001b[0;36m_we_dont_return_rows\u001b[0;34m(self, err)\u001b[0m\n\u001b[1;32m   1176\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1177\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0m_we_dont_return_rows\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0merr\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mNone\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1178\u001b[0;31m         util.raise_(\n\u001b[0m\u001b[1;32m   1179\u001b[0m             exc.ResourceClosedError(\n\u001b[1;32m   1180\u001b[0m                 \u001b[0;34m\"This result object does not return rows. \"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/code/micha<PERSON>-quinlan/repo/bazel-bin/jupyter/jupyter.runfiles/third_party_pypi__sqlalchemy/sqlalchemy/util/compat.py\u001b[0m in \u001b[0;36mraise_\u001b[0;34m(***failed resolving arguments***)\u001b[0m\n\u001b[1;32m    205\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    206\u001b[0m         \u001b[0;32mtry\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 207\u001b[0;31m             \u001b[0;32mraise\u001b[0m \u001b[0mexception\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    208\u001b[0m         \u001b[0;32mfinally\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    209\u001b[0m             \u001b[0;31m# credit to\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mResourceClosedError\u001b[0m: This result object does not return rows. It has been closed automatically."]}], "source": []}, {"cell_type": "code", "execution_count": 16, "id": "52f11c51", "metadata": {}, "outputs": [{"data": {"text/plain": ["'1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021'"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["list_of_ints = list(NBA_REGULAR_SEASON_DATES.keys())\n", "','.join([str(i) for i in list_of_ints])"]}, {"cell_type": "code", "execution_count": null, "id": "553ea1a7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.2"}}, "nbformat": 4, "nbformat_minor": 5}