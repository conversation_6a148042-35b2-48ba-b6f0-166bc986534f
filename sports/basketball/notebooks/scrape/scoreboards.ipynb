{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "from sports.basketball.scrape.nba.scoreboard import Scoreboard\n", "from sports.basketball.scrape.nba.constants import NBA_REGULAR_SEASON_DATES, NBA_PLAYOFF_SEASON_DATES\n", "from sports.basketball.database.database import Database\n", "from datetime import date\n", "import pandas as pd\n", "import time"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["db = Database()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["end_date = NBA_PLAYOFF_SEASON_DATES[2021][1]\n", "start_date = NBA_REGULAR_SEASON_DATES[2021][0]\n", "daterange = pd.date_range(start_date, end_date)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["for d in reversed(daterange):\n", "    print(d)\n", "    Scoreboard(day=d)\n", "    time.sleep(5) "]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}}, "nbformat": 4, "nbformat_minor": 2}