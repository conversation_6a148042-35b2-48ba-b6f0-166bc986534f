{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings('ignore')\n", "from sports.basketball.scrape.nba.player import Player\n", "from sports.basketball.database.database import Database\n", "from datetime import date\n", "import pandas as pd\n", "import time"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["db = Database()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["sql = 'SELECT DISTINCT(player_id) FROM boxscores INNER JOIN teams on boxscores.team_id=teams.team_id WHERE NOT EXISTS (SELECT 1 FROM players WHERE players.player_id = boxscores.player_id)'"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["missing_players=db.query(sql)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true, "scrolled": true}, "outputs": [], "source": ["for i,x in enumerate(missing_players.values):\n", "    print i, len(missing_players), x[0]\n", "    try:\n", "        Player(player_id=str(x[0]))\n", "    except:\n", "        p = [{'player_id': x[0], 'first_name': 'unknown', 'last_name': 'unknown'}]\n", "        df = pd.DataFrame(p)\n", "        db.insert(df, table='players')\n", "        print('Raw inserted')\n", "        print(df)\n", "    time.sleep(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 2}