{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings('ignore')\n", "from sports.basketball.scrape.nba.shotchart import <PERSON><PERSON><PERSON>\n", "from sports.basketball.1database.database import Database\n", "from sports.basketball.scrape.nba.constants import REGULAR_SEASON, PLAYOFFS\n", "from datetime import date\n", "import pandas as pd\n", "import time"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["db = Database()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["seasons = db.query(\"select season from games group by season order by season;\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["for season in seasons.values:\n", "    if season < 2005 or season >=2018:\n", "        continue\n", "    season_val = season[0]\n", "    print(season_val)\n", "    reg = ShotChart(game_id='', season=season_val, season_type=REGULAR_SEASON)\n", "    print(season, REGULAR_SEASON, len(reg.dataframe))\n", "    pl = ShotChart(game_id='', season=season_val, season_type=PLAYOFFS)\n", "    print(season, PLAYOFFS, len(pl.dataframe))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 2}