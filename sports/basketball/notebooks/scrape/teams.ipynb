{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings('ignore')\n", "from sports.basketball.scrape.nba.teamlist import TeamList\n", "from sports.basketball.database.database import Database"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["tl = TeamList()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true, "scrolled": true}, "outputs": [], "source": ["tl.dataframe"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["db = Database()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["db.query('select * from teams')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true, "scrolled": false}, "outputs": [], "source": ["tl.database_update(db)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["db.query('select * from teams')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 2}