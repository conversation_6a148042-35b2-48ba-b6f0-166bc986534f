{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings('ignore')\n", "from sports.basketball.database.database import Database\n", "from sports.basketball.fantasy.roster_optimizer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "from sports.basketball.fantasy.yahoo_daily import Contest, mean_games_at_perc, fantasy_points\n", "from matplotlib import pyplot as plt\n", "import pandas as pd\n", "import numpy as np\n", "\n", "plt.style.use('fivethirtyeight')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["db = Database()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["contest = Contest(78400513)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true, "scrolled": true}, "outputs": [], "source": ["contest.compute(mean_games_at_perc, games=10, percentage=15, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["print(contest.roster.sum()[['salary', 'points']])\n", "plt.plot(contest.roster.sum()['values'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["plt.hist(contest.sample())"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["def perc_over(results, value = 300):\n", "    return sum(1 for i in results if i > value) / len(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["perc_over_300(contest.sample())       "]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 2}