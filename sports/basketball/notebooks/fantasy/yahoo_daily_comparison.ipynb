{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings('ignore')\n", "from sports.basketball.database.database import Database\n", "from sports.basketball.fantasy.roster_optimizer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "from sports.basketball.fantasy.yahoo_daily import Contest, mean_games_at_perc, fantasy_points\n", "from matplotlib import pyplot as plt\n", "import pandas as pd\n", "import numpy as np\n", "\n", "plt.style.use('fivethirtyeight')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["def perc_over(results, value = 300):\n", "    return sum(1 for i in results if i > value) / len(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": ["db = Database()\n", "contest_id = 20190121"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["contests = []\n", "for games in [10]:\n", "    for percentage in range(10,100,10):\n", "        print(games,percentage)\n", "        contest = Contest(contest_id)\n", "        contest.force_player(\"<PERSON><PERSON>\", \"<PERSON>\", \"<PERSON><PERSON>\")\n", "        contest.exclude_player(\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON>\")\n", "        contest.exclude_player(\"<PERSON>\",\"<PERSON>\")\n", "        contest.compute(mean_games_at_perc, games=games, percentage=percentage, season=2018, axis=1)\n", "        contests.append((games, percentage, contest))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["for contest in contests:\n", "    over = perc_over(contest[2].sample(), value=300)\n", "    result = (contest[0], contest[1], over)\n", "    plt.plot(over)\n", "    print(result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true, "scrolled": true}, "outputs": [], "source": ["idx = 4\n", "contests[idx][2].roster"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["print(contests[idx][2].roster.sum()[['salary', 'points']])\n", "plt.plot(contests[idx][2].roster.sum()['values'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "deletable": true, "editable": true}, "outputs": [], "source": ["plt.hist(contests[idx][2].sample())"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "deletable": true, "editable": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 2}