package(default_visibility = ["//visibility:public"])

load("@pip//:requirements.bzl", "requirement")

py_library(
    name = "fivethirtyeight",
    deps = [
        ":constants",
        ":nba_elo_download",
    ],
)

py_library(
    name = "constants",
    srcs = ["constants.py"],
)

py_library(
    name = "scores",
    srcs = ["scores.py"],
    deps = [
        ":constants",
        "//sports/basketball/database:dbtransformer",
        "//sports/basketball/database:scores_fivethirtyeight",
    ],
)

py_binary(
    name = "nba_elo_download",
    srcs = ["nba_elo_download.py"],
    deps = [
        ":constants",
        ":scores",
        "//slack_wrapper",
        "//sports/basketball/database",
        requirement("requests"),
    ],
)

py_test(
    name = "nba_elo_download_test",
    size = "small",
    srcs = [
        "nba_elo_download_test.py",
    ],
    data = [
        ":nba_elo_test_file",
    ],
    deps = [
        "//sports/basketball/scrape/fivethirtyeight:nba_elo_download",
    ],
)

filegroup(
    name = "nba_elo_test_file",
    srcs = ["nba_elo_test.csv"],
)
