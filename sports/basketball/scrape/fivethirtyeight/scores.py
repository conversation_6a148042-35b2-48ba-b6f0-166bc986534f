from sports.basketball.database.dbtransformer import DbTransformer
from sports.basketball.database.scores_fivethirtyeight import ScoresFiveThirtyEight


class Scores(DbTransformer):
    """Inserts five thirty eight scores into the database"""

    def __init__(self, dataframe, **kwargs):
        self._dataframe = dataframe
        if "database" in kwargs:
            DbTransformer.__init__(self, kwargs["database"])

    def orm(self):
        return ScoresFiveThirtyEight()

    def transform(self):
        df = self._dataframe
        df = df.dropna(subset=["score1", "score2"])
        # df['playoff'] = df['playoff'].apply(lambda x: None if "NaN" else x)
        return df[
            [
                "date",
                "season",
                "neutral",
                "playoff",
                "team1",
                "team2",
                "score1",
                "score2",
            ]
        ]
