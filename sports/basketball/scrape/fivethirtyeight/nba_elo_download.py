import os
from os.path import getsize, exists
from requests import get, head
from sports.basketball.database.database import Database
from sports.basketball.scrape.fivethirtyeight.constants import NBA_ELO_CSV_PATH
from sports.basketball.scrape.fivethirtyeight.scores import Scores
from slack_wrapper.slack_wrapper import SlackWrapper
import pandas as pd

DOWNLOAD_URL = "https://projects.fivethirtyeight.com/nba-model/nba_elo.csv"


class Downloader(object):

    # url and file_path should only passed in during testing
    def __init__(self, url=DOWNLOAD_URL, file_path=NBA_ELO_CSV_PATH, incremental=True):
        self._url = url
        self._file_path = file_path
        # Do incremental db update, if false then update every row
        self._incremental = incremental

    def update(self):
        if self.__fetch_content_length() > self.__on_disk_size():
            self.__fetch_file()
            self.__update_db()
            return True
        return False

    def __fetch_content_length(self):
        _response = head(self._url)
        _response.raise_for_status()
        return eval(_response.headers["Content-Length"])

    def __fetch_file(self):
        _response = get(self._url, allow_redirects=True)
        _response.raise_for_status()
        os.makedirs(os.path.dirname(self._file_path), exist_ok=True)
        open(self._file_path, "wb").write(_response.content)

    def __update_db(self):
        raw_dataframe = pd.read_csv(self._file_path)
        db = Database()
        if self._incremental:
            raw_dataframe["date"] = pd.to_datetime(raw_dataframe["date"])
            max_date = db.query("select max(date) from scores_fivethirtyeight")
            if max_date.iat[0, 0] is not None:
                raw_dataframe = raw_dataframe[raw_dataframe.date >= max_date.iat[0, 0]]
        Scores(raw_dataframe, database=db)

    def __on_disk_size(self):
        return getsize(self._file_path) if exists(self._file_path) else 0


def execute_download():
    downloader = Downloader()
    slack = SlackWrapper(
        bot_name="538 Elo Scraper",
        icon_emoji=":chart_with_upwards_trend:",
        channel="#scrape-updates",
    )
    updated = downloader.update()
    slack.display("Done (New file = ", updated, ")")
    return updated


if __name__ == "__main__":
    execute_download()
