import unittest

from sports.basketball.scrape.fivethirtyeight.nba_elo_download import Downloader
import os

file_path = os.path.dirname(__file__) + '/nba_elo_test.csv'


class TestNBAEloDownload(unittest.TestCase):
    def setUp(self):
        self.downloader = Downloader(file_path=file_path)

    def test_disk_size(self):
        self.assertEqual(self.downloader._Downloader__on_disk_size(), 9115473)

    def test_url_size(self):
        self.assertGreater(self.downloader._Downloader__fetch_content_length(), 9115473)


if __name__ == '__main__':
    unittest.main()
