from sports.basketball.scrape.nba.scoreboard import Scoreboard
from sports.basketball.scrape.nba.boxscore import Boxscore


def validate_box_score_matches_scoreboard(boxscore: Boxscore, scoreboard: Scoreboard) -> bool:
    bs_team_summary = boxscore.dataframe_at(1).sort_values(by=["team_id"], ignore_index=True)[
        ["team_id", "pts", "fg_pct", "ft_pct", "fg3_pct", "reb", "ast", "to"]
    ]
    sc_team_summary = scoreboard.dataframe_at(1)
    sc_team_summary = (
        sc_team_summary[sc_team_summary.game_id == boxscore.game_id]
        .sort_values(by=["team_id"], ignore_index=True)[
            ["team_id", "pts", "fg_pct", "ft_pct", "fg3_pct", "reb", "ast", "tov"]
        ]
        .rename(columns={"tov": "to"})
    )
    return sc_team_summary.equals(bs_team_summary)
