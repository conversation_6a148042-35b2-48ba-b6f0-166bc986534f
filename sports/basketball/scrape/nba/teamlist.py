from sports.basketball.database.dbtransformer import DbTransformer
from sports.basketball.database.teams import Teams
from sports.basketball.scrape.nba.constants import NBA_KEY
from sports.basketball.scrape.nba.endpoint import Endpoint


class TeamList(Endpoint, DbTransformer):
    """Downloads the team list for a league"""

    def __init__(self, league: str = NBA_KEY, **kwargs):
        self._league = league
        Endpoint.__init__(self, **kwargs)
        if "database" in kwargs:
            DbTransformer.__init__(self, kwargs["database"])

    @property
    def _endpoint(self):
        return "http://stats.nba.com/stats/commonteamyears"

    @property
    def _params(self):
        return {"LeagueID": self._league}

    @property
    def _filename(self):
        return "teamlist_" + str(self._league) + ".json"

    def orm(self):
        return Teams()

    def transform(self):
        # In this case the dataframe has exactly the fields we want in the db
        return self.dataframe
