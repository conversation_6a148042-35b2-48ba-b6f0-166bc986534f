import pandas as pd

from sports.basketball.database.dbtransformer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from sports.basketball.database.playbyplays import PlayByPlays
from sports.basketball.scrape.nba.constants import infer_season
from sports.basketball.scrape.nba.endpoint import Endpoint


class PlayByPlay(Endpoint, DbTransformer):
    """Downloads the play by play for a given game"""

    def __init__(self, game_id: str, **kwargs):
        self._game_id = game_id
        self._season = infer_season(game_id)
        Endpoint.__init__(self, **kwargs)
        if "database" in kwargs:
            DbTransformer.__init__(self, kwargs["database"])

    @property
    def _endpoint(self):
        return "http://stats.nba.com/stats/playbyplayv2"

    @property
    def _params(self):
        return {
            "GameID": self._game_id,
            "Season": str(self._season),
            "RangeType": 2,
            "StartPeriod": 1,
            "EndPeriod": 10,
            "StartRange": 0,
            "EndRange": 0,
        }

    @property
    def _filename(self):
        return "playbyplay_" + str(self._game_id) + ".json"

    @staticmethod
    def min_to_seconds(min_str):
        if not min_str:
            return None
        if isinstance(min_str, int):
            return min_str * 60
        splits = min_str.split(":")
        return int(splits[0]) * 60 + int(splits[1])

    @staticmethod
    def score_to_home(score_str):
        if not score_str or "-" not in score_str:
            return None
        return int(score_str.split("-")[0])

    @staticmethod
    def score_to_visitor(score_str):
        if not score_str or "-" not in score_str:
            return None
        return int(score_str.split("-")[1])

    def orm(self):
        return PlayByPlays()

    def transform(self):
        df = self.dataframe.rename(
            columns={
                "eventnum": "event_num",
                "eventmsgtype": "event_msg_type",
                "eventmsgactiontype": "event_msg_action_type",
                "homedescription": "home_description",
                "visitordescription": "visitor_description",
                "person1type": "person1_type",
                "person2type": "person2_type",
                "person3type": "person3_type",
            }
        )
        df["seconds"] = df["pctimestring"].apply(self.min_to_seconds)
        if df["seconds"].sum() < 1:
            return pd.DataFrame()
        df["home_score"] = df["score"].apply(self.score_to_home)
        df["visitor_score"] = df["score"].apply(self.score_to_visitor)
        df = df.where(df.notnull(), None)
        return df[
            [
                "game_id",
                "event_num",
                "event_msg_type",
                "event_msg_action_type",
                "period",
                "seconds",
                "wctimestring",
                "home_description",
                "visitor_description",
                "home_score",
                "visitor_score",
                "person1_type",
                "player1_id",
                "person2_type",
                "player2_id",
                "person3_type",
                "player3_id",
            ]
        ]

    def __str__(self):
        if len(self.dataframe.index) == 0:
            return "No data    ({})".format(self._game_id)

        teams = self.dataframe.player1_team_abbreviation.unique()
        teams = [x for x in teams if x is not None]
        return "{} v {} : Rows={}   ({})".format(
            teams[0], teams[1], len(self.dataframe.index), self._game_id
        )
