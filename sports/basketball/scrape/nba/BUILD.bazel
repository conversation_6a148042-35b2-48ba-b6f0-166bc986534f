package(default_visibility = ["//visibility:public"])

load("@pip//:requirements.bzl", "requirement")

py_library(
    name = "nba",
    deps = [
        ":boxscore",
        ":game_detail",
        ":nba_download",
        ":play_by_play",
        ":player",
        ":scoreboard",
        ":scoreboard_today",
        ":shotchart",
        ":teamlist",
    ],
)

py_library(
    name = "scoreboard",
    srcs = ["scoreboard.py"],
    deps = [
        ":constants",
        ":endpoint",
        "//sports/basketball/database:dbtransformer",
        "//sports/basketball/database:games",
    ],
)

py_library(
    name = "scoreboard_today",
    srcs = ["scoreboard_today.py"],
    deps = [
        ":constants",
        ":endpoint",
    ],
)

py_test(
    name = "scoreboard_test",
    size = "small",
    srcs = [
        "scoreboard_test.py",
    ],
    data = [
        "test_data/scoreboard_00_20171225.json",
    ],
    deps = [
        "//sports/basketball/database:postgress_test_case",
        "//sports/basketball/scrape/nba:scoreboard",
        requirement("requests-mock"),
    ],
)

py_library(
    name = "boxscore",
    srcs = ["boxscore.py"],
    deps = [
        ":constants",
        ":endpoint",
        "//sports/basketball/database:boxscores",
        "//sports/basketball/database:dbtransformer",
    ],
)

py_test(
    name = "boxscore_test",
    size = "small",
    srcs = [
        "boxscore_test.py",
    ],
    data = [
        "test_data/boxscore_0021700493.json",
    ],
    deps = [
        "//sports/basketball/database:postgress_test_case",
        "//sports/basketball/scrape/nba:boxscore",
        requirement("requests-mock"),
    ],
)

py_library(
    name = "play_by_play",
    srcs = ["play_by_play.py"],
    deps = [
        ":constants",
        ":endpoint",
        "//sports/basketball/database:dbtransformer",
        "//sports/basketball/database:playbyplays",
    ],
)

py_test(
    name = "play_by_play_test",
    size = "small",
    srcs = [
        "play_by_play_test.py",
    ],
    data = [
        "test_data/playbyplay_0021700493.json",
    ],
    deps = [
        "//sports/basketball/database:postgress_test_case",
        "//sports/basketball/scrape/nba:play_by_play",
        requirement("requests-mock"),
    ],
)

py_library(
    name = "shotchart",
    srcs = ["shotchart.py"],
    deps = [
        ":constants",
        ":endpoint",
        "//sports/basketball/database:dbtransformer",
        "//sports/basketball/database:shortcharts",
    ],
)

py_test(
    name = "shotchart_test",
    size = "small",
    srcs = [
        "shotchart_test.py",
    ],
    data = [
        "test_data/shotchart.json",
    ],
    deps = [
        "//sports/basketball/database:postgress_test_case",
        "//sports/basketball/scrape/nba:shotchart",
        requirement("requests-mock"),
    ],
)

py_library(
    name = "teamlist",
    srcs = ["teamlist.py"],
    deps = [
        ":constants",
        ":endpoint",
        "//sports/basketball/database:dbtransformer",
        "//sports/basketball/database:teams",
    ],
)

py_test(
    name = "teamlist_test",
    size = "small",
    srcs = [
        "teamlist_test.py",
    ],
    data = [
        "test_data/teamlist.json",
    ],
    deps = [
        "//sports/basketball/database:postgress_test_case",
        "//sports/basketball/scrape/nba:teamlist",
        requirement("requests-mock"),
    ],
)

py_library(
    name = "player",
    srcs = ["player.py"],
    deps = [
        ":endpoint",
        "//sports/basketball/database:dbtransformer",
        "//sports/basketball/database:players",
    ],
)

py_test(
    name = "player_test",
    size = "small",
    srcs = [
        "player_test.py",
    ],
    data = [
        "test_data/player.json",
    ],
    deps = [
        "//sports/basketball/database:postgress_test_case",
        "//sports/basketball/scrape/nba:player",
        requirement("requests-mock"),
    ],
)

py_library(
    name = "game_detail",
    srcs = ["game_detail.py"],
    deps = [
        ":endpoint",
        "//sports/basketball/database:dbtransformer",
        "//sports/basketball/database:players",
    ],
)

py_test(
    name = "game_detail_test",
    size = "small",
    srcs = [
        "game_detail_test.py",
    ],
    data = [
        "test_data/game_detail.json",
    ],
    deps = [
        "//sports/basketball/database:postgress_test_case",
        "//sports/basketball/scrape/nba:game_detail",
        requirement("requests-mock"),
    ],
)

py_library(
    name = "nba_download",
    srcs = ["nba_download.py"],
    deps = [
        "//sports/basketball/database:dbtransformer",
        "//sports/basketball/scrape/nba:boxscore",
        "//sports/basketball/scrape/nba:play_by_play",
        "//sports/basketball/scrape/nba:scoreboard",
        "//sports/basketball/scrape/nba:shotchart",
    ],
)

py_test(
    name = "nba_download_test",
    srcs = ["nba_download_test.py"],
    data = [
        "test_data/scoreboard_00_20171225.json",
    ],
    deps = [
        "//sports/basketball/database:postgress_test_case",
        "//sports/basketball/scrape/nba:nba_download",
        requirement("requests-mock"),
    ],
)

py_library(
    name = "endpoint",
    srcs = ["endpoint.py"],
    deps = [
        requirement("pandas"),
        requirement("requests"),
    ],
)

py_library(
    name = "constants",
    srcs = ["constants.py"],
    deps = [
        "//sports/basketball/database:league_utils",
        requirement("pandas"),
    ],
)

py_test(
    name = "constants_test",
    size = "small",
    srcs = [
        "constants_test.py",
    ],
    tags = ["local"],
    deps = [
        "//sports/basketball/database:league_utils",
        "//sports/basketball/scrape/nba:constants",
        requirement("pandas"),
    ],
)

py_binary(
    name = "update",
    srcs = ["update.py"],
    deps = [
        ":nba",
        "//slack_wrapper",
        "//sports/basketball/database",
    ],
)

py_binary(
    name = "scrape_validator",
    srcs = ["scrape_validator.py"],
    deps = [
        ":boxscore",
        ":scoreboard",
    ],
)

py_test(
    name = "scrape_validator_test",
    size = "small",
    srcs = [
        "scrape_validator_test.py",
    ],
    data = [
        "test_data/boxscore_0021700493.json",
        "test_data/boxscore_0021800493.json",
        "test_data/scoreboard_00_20171225.json",
    ],
    deps = [
        ":scrape_validator",
    ],
)
