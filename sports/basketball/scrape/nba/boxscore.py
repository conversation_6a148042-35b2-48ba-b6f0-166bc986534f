import pandas as pd

from sports.basketball.database.boxscores import Boxscores
from sports.basketball.database.dbtransformer import DbT<PERSON>s<PERSON>
from sports.basketball.scrape.nba.endpoint import Endpoint
from sports.basketball.scrape.nba.constants import infer_season


class Boxscore(Endpoint, DbTransformer):
    """Loads from disk or Downloads the boxscore for a given game"""

    def __init__(self, game_id: str, **kwargs):
        self._game_id = game_id
        self._season = infer_season(game_id)
        Endpoint.__init__(self, **kwargs)
        if "database" in kwargs:
            DbTransformer.__init__(self, kwargs["database"])

    @property
    def _endpoint(self):
        return "http://stats.nba.com/stats/boxscoretraditionalv2"

    @property
    def _params(self):
        return {
            "GameID": self._game_id,
            "Season": str(self._season),
            "RangeType": 0,
            "StartPeriod": 0,
            "EndPeriod": 0,
            "StartRange": 0,
            "EndRange": 0,
        }

    @property
    def _filename(self):
        return "boxscore_" + str(self._game_id) + ".json"

    @property
    def game_id(self) -> str:
        return self._game_id

    @staticmethod
    def min_to_seconds(minutes: str):
        if not minutes:
            return None
        if isinstance(minutes, int):
            return minutes * 60
        splits = minutes.split(":")
        return int(splits[0]) * 60 + int(splits[1])

    def orm(self):
        return Boxscores()

    def transform(self):
        df = self.dataframe
        df["seconds"] = df["min"].apply(self.min_to_seconds)
        df.fillna(0, inplace=True)
        if df["seconds"].sum() < 1:
            return pd.DataFrame()
        return df[
            [
                "game_id",
                "team_id",
                "player_id",
                "start_position",
                "comment",
                "seconds",
                "fgm",
                "fga",
                "fg3m",
                "fg3a",
                "ftm",
                "fta",
                "oreb",
                "dreb",
                "reb",
                "ast",
                "stl",
                "blk",
                "to",
                "pf",
                "pts",
                "plus_minus",
            ]
        ]

    def __str__(self):
        teams = self.dataframe.team_abbreviation.unique()
        return "{} v {} : Rows={}   ({})".format(
            teams[0], teams[1], len(self.dataframe.index), self._game_id
        )
