import unittest
import os
import shutil
import pandas as pd
import json
import requests_mock
from sports.basketball.scrape.nba.game_detail import GameDetail
from sports.basketball.database.postgress_test_case import PostgressTestCase

GAME_ID = "1021900076"
SEASON = 2019
LEAGUE = "WNBA"

TEST_BACKUP_PATH = "/tmp/game_detail_test/"
with open(os.path.dirname(__file__) + "/test_data/game_detail.json", "r") as input_file:
    JSON_RESPONSE = json.loads(input_file.read())


class TestGameDetail(PostgressTestCase, unittest.TestCase):
    def setUp(self):
        super(TestGameDetail, self).setUp()

    def test_default_extract(self):
        shutil.rmtree(TEST_BACKUP_PATH, ignore_errors=True)
        self.assertFalse(os.path.isdir(TEST_BACKUP_PATH))

        # Should fetch since no file
        with requests_mock.Mocker() as m:
            m.get(
                requests_mock.ANY,
                json=JSON_RESPONSE,
                status_code=200,
            )
            gd = GameDetail(
                game_id=GAME_ID,
                season=SEASON,
                league=LEAGUE,
                backup_path=TEST_BACKUP_PATH,
            )

        self.assertTrue(os.path.isfile(gd.output_path))
        self.assertGreater(len(gd.json), 0)
        df = gd.dataframe
        self.assertEqual(df.shape[0], 23)
        self.assertEqual(df.shape[1], 38)

        # Should load since file exists
        gd2 = GameDetail(
            game_id=GAME_ID, season=SEASON, league=LEAGUE, backup_path=TEST_BACKUP_PATH
        )
        self.assertGreater(len(gd2.json), 0)
        df2 = gd2.dataframe
        self.assertEqual(df2.shape[0], 23)
        self.assertEqual(df2.shape[1], 38)

    def test_database_load(self):
        df = pd.DataFrame(
            {
                "game_id": ["1021900076"],
                "season": [2019],
                "home_team_id": [1],
                "visitor_team_id": [1],
                "league": [1],
            }
        )
        self.db.insert(df, table="games")
        sql = "select count(*) from boxscores;"
        start_count = self.db.count(sql)
        self.assertEqual(start_count, 0)

        with requests_mock.Mocker() as m:
            m.get(
                requests_mock.ANY,
                json=JSON_RESPONSE,
                status_code=200,
            )
            gd = GameDetail(
                database=self.db,
                game_id=GAME_ID,
                season=SEASON,
                league=LEAGUE,
                backup_path=TEST_BACKUP_PATH,
            )
        # Check we instered the correct number of rows
        end_count = self.db.count(sql)
        self.assertEqual(end_count, gd.dataframe.shape[0])
        # Smoke test one entry
        pts = self.db.query("select pts from boxscores where player_id=1629479")
        self.assertEqual(pts.values[0], 9)


if __name__ == "__main__":
    unittest.main()
