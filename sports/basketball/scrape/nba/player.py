from sports.basketball.database.dbtransformer import DbTransformer
from sports.basketball.database.players import Players
from sports.basketball.scrape.nba.endpoint import Endpoint


class Player(Endpoint, DbTransformer):
    """Downloads the team list for a league and season"""

    def __init__(self, player_id, **kwargs):
        self._player_id = player_id
        Endpoint.__init__(self, **kwargs)
        if "database" in kwargs:
            DbTransformer.__init__(self, kwargs["database"])

    @property
    def _endpoint(self):
        return "http://stats.nba.com/stats/commonplayerinfo"

    @property
    def _params(self):
        return {"PlayerID": self._player_id}

    @property
    def _filename(self):
        return "player_" + str(self._player_id) + ".json"

    def orm(self):
        return Players()

    def transform(self):
        df = self.dataframe.rename(
            columns={"person_id": "player_id", "draft_number": "draft_position"}
        )
        df = df.replace({"Undrafted": None})
        return df[
            [
                "player_id",
                "first_name",
                "last_name",
                "birthdate",
                "school",
                "country",
                "playercode",
                "from_year",
                "to_year",
                "draft_year",
                "draft_round",
                "draft_position",
            ]
        ]
