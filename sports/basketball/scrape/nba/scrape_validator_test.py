import datetime
import unittest
import os
from sports.basketball.scrape.nba.boxscore import Boxscore
from sports.basketball.scrape.nba.scoreboard import Scoreboard
from sports.basketball.scrape.nba.scrape_validator import (
    validate_box_score_matches_scoreboard,
)

GAME_ID = "0021700493"

TEST_FILE_PATH = f"{os.path.dirname(__file__)}/test_data/"


class TestScrapeValidator(unittest.TestCase):
    def test_validate_box_score_matches_scoreboard(self):
        boxscore = Boxscore(game_id="0021700493", backup_path=TEST_FILE_PATH)
        scoreboard = Scoreboard(day=datetime.date(year=2017, month=12, day=25), backup_path=TEST_FILE_PATH)
        self.assertTrue(validate_box_score_matches_scoreboard(boxscore, scoreboard))

    def test_validate_wrong_box_score_matches_scoreboard(self):
        boxscore = Boxscore(game_id="0021800493", backup_path=TEST_FILE_PATH)
        scoreboard = Scoreboard(day=datetime.date(year=2017, month=12, day=25), backup_path=TEST_FILE_PATH)
        self.assertFalse(validate_box_score_matches_scoreboard(boxscore, scoreboard))


if __name__ == "__main__":
    unittest.main()
