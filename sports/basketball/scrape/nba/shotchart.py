from sports.basketball.database.dbtransformer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from sports.basketball.database.shotcharts import <PERSON><PERSON><PERSON><PERSON>
from sports.basketball.scrape.nba.constants import (
    NBA_KEY,
    infer_season,
    infer_season_type,
    infer_league,
)
from sports.basketball.scrape.nba.endpoint import Endpoint


class <PERSON><PERSON><PERSON>(Endpoint, DbTransformer):
    """Downloads all shots for a given season"""

    # Passing '' to game_id will get all games for a given season.
    def __init__(self, game_id: str, **kwargs):
        # This api takes season like 2017-18 not just 2017
        self._season = infer_season(game_id)
        self._full_season = str(self._season) + "-" + str(int(self._season) + 1)[-2:]
        self._game_id = game_id
        self._season_type = infer_season_type(game_id)
        self._league = infer_league(game_id)
        Endpoint.__init__(self, **kwargs)
        if "database" in kwargs:
            DbTransformer.__init__(self, kwargs["database"])

    @property
    def _endpoint(self):
        return "http://stats.nba.com/stats/shotchartdetail"

    @property
    def _params(self):
        return {
            "PlayerID": 0,
            "TeamID": 0,
            "GameID": self._game_id,
            "LeagueID": self._league,
            "Season": self._full_season,
            "SeasonType": self._season_type,
            "Outcome": "",
            "Location": "",
            "Month": 0,
            "SeasonSegment": "",
            "DateFrom": "",
            "DateTo": "",
            "OpponentTeamID": 0,
            "VsConference": "",
            "VsDivision": "",
            "PlayerPosition": "",
            "GameSegment": "",
            "Period": 0,
            "LastNGames": 0,
            "AheadBehind": "",
            "ContextMeasure": "FGM",
            "ClutchTime": "",
            "RookieYear": "",
        }

    @property
    def _filename(self):
        return "shotchart_" + str(self._game_id) + ".json"

    @staticmethod
    def min_to_seconds(minutes, seconds):
        return int(minutes * 60 + seconds)

    def orm(self):
        return ShotCharts()

    def transform(self):
        df = self.dataframe
        df["seconds"] = df["minutes_remaining"] * 60 + df["seconds_remaining"]
        df = df.where(df.notnull(), None)
        return df[
            [
                "game_id",
                "game_event_id",
                "player_id",
                "player_name",
                "team_id",
                "team_name",
                "period",
                "seconds",
                "event_type",
                "action_type",
                "shot_type",
                "shot_zone_basic",
                "shot_zone_area",
                "shot_zone_range",
                "shot_distance",
                "loc_x",
                "loc_y",
                "shot_attempted_flag",
                "shot_made_flag",
            ]
        ]

    def __str__(self):
        teams = self.dataframe.team_name.unique()
        teams = [x for x in teams if x is not None]
        if len(self.dataframe) <= 1:
            return "Shotchart failure : Rows={}   ({})".format(
                len(self.dataframe.index), self._game_id
            )
        return "{} v {} : Rows={}   ({})".format(
            teams[0], teams[1], len(self.dataframe.index), self._game_id
        )
