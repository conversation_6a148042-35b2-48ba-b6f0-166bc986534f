import unittest
import os
import shutil
import json
import requests_mock
from datetime import date

from sports.basketball.scrape.nba.scoreboard import Scoreboard
from sports.basketball.scrape.nba.nba_download import NBADownload
from sports.basketball.database.postgress_test_case import PostgressTestCase

GAME_DAY = date(2017, 12, 25)
TEST_BACKUP_PATH = "/tmp/nba_download_test/"
with open(os.path.dirname(__file__) + "/test_data/scoreboard_00_20171225.json", "r") as input_file:
    JSON_SCOREBOARD_RESPONSE = json.loads(input_file.read())


class TestNBADownload(PostgressTestCase, unittest.TestCase):
    def setUp(self):
        super(TestNBADownload, self).setUp()

    def test_add_scoreboard(self):
        # Verify the db is empty
        sql = "select count(*) from nba_downloads;"
        start_count = self.db.count(sql)
        self.assertEqual(start_count, 0)

        shutil.rmtree(TEST_BACKUP_PATH, ignore_errors=True)
        self.assertFalse(os.path.isdir(TEST_BACKUP_PATH))

        # Should fetch since no file
        with requests_mock.Mocker() as m:
            m.get(
                requests_mock.ANY,
                json=JSON_SCOREBOARD_RESPONSE,
                status_code=200,
            )
            sc = Scoreboard(day=GAME_DAY, backup_path=TEST_BACKUP_PATH)
        self.assertTrue(os.path.isfile(sc.output_path))

        # Insert the box score
        _ = NBADownload(scoreboard=sc, database=self.db)

        # Verify the db is now populated
        end_count = self.db.count(sql)
        self.assertEqual(end_count, sc.dataframe.shape[0])
        # Smoke test one entry
        game_status = self.db.query(
            "select status from nba_downloads where game_id='0021700493'"
        )
        self.assertEqual(game_status.values[0], "Final")


if __name__ == "__main__":
    unittest.main()
