from sports.basketball.database.dbtransformer import DbTransformer
from sports.basketball.database.games import Games
from sports.basketball.scrape.nba.constants import NBA_KEY
from sports.basketball.database.season_utils import season_type
from sports.basketball.scrape.nba.endpoint import Endpoint


class Scoreboard(Endpoint, DbTransformer):
    """Downloads the scoreboard for a given day"""

    def __init__(self, day, league: str = NBA_KEY, **kwargs):
        self._day = day
        self._league = league
        Endpoint.__init__(self, **kwargs)
        if "database" in kwargs:
            DbTransformer.__init__(self, kwargs["database"])

    @property
    def _endpoint(self):
        return "http://stats.nba.com/stats/scoreboard"

    @property
    def _params(self):
        game_date_param = "{month:02d}/{day:02d}/{year}".format(
            month=self._day.month, day=self._day.day, year=self._day.year
        )
        return {"LeagueID": self._league, "GameDate": game_date_param, "DayOffset": 0}

    @property
    def _filename(self):
        game_date_file = "{year}{month:02d}{day:02d}".format(
            month=self._day.month, day=self._day.day, year=self._day.year
        )
        return "scoreboard_" + str(self._league) + "_" + game_date_file + ".json"

    @property
    def num_games(self):
        return len(self.dataframe.index)

    def orm(self):
        return Games()

    def transform(self):
        df = self.dataframe.rename(
            columns={"live_period": "quarters", "game_status_text": "status"}
        )
        df["quarters"].fillna(0, inplace=True)
        df["status"].fillna(0, inplace=True)
        df["game_sequence"].fillna(0, inplace=True)
        df["game_status_id"].fillna(0, inplace=True)
        df["league"] = eval(self._league)
        df["season_type"] = df.apply(season_type, axis=1) if len(df) > 0 else None
        return df[
            [
                "game_id",
                "game_date_est",
                "game_sequence",
                "game_status_id",
                "season",
                "home_team_id",
                "visitor_team_id",
                "quarters",
                "status",
                "season_type",
                "league",
            ]
        ]

    def __str__(self):
        return f"{self._day}: {self.num_games}"
