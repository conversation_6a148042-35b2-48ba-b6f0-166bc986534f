from sports.basketball.database.dbtransformer import DbTransformer
from sports.basketball.database.nba_downloads import NBADownloads
from sports.basketball.scrape.nba.scoreboard import Scoreboard
from sports.basketball.scrape.nba.boxscore import Boxscore
from sports.basketball.scrape.nba.play_by_play import PlayByPlay
from sports.basketball.scrape.nba.shotchart import Shot<PERSON>hart

import pandas as pd


class NBADownload(DbTransformer):
    """Keeps track of what files have been downloaded"""

    def __init__(
        self,
        scoreboard: Scoreboard = None,
        box_score: Boxscore = None,
        play_by_play: PlayByPlay = None,
        shotchart: ShotChart = None,
        **kwargs
    ):
        if scoreboard is not None:
            self._injecting_df = scoreboard.transform()
        if box_score is not None:
            self._injecting_df = pd.DataFrame(
                {
                    "game_id": [box_score.dataframe["game_id"][0]],
                    "num_box_score": [len(box_score.dataframe)],
                }
            )
        if play_by_play is not None:
            if len(play_by_play.dataframe) == 0:
                return
            self._injecting_df = pd.DataFrame(
                {
                    "game_id": [play_by_play.dataframe["game_id"][0]],
                    "num_play_by_play": [len(play_by_play.dataframe)],
                }
            )

        if shotchart is not None:
            if len(shotchart.dataframe) == 0:
                return
            self._injecting_df = pd.DataFrame(
                {
                    "game_id": [shotchart.dataframe["game_id"][0]],
                    "num_shot_chart": [len(shotchart.dataframe)],
                }
            )

        if "database" in kwargs:
            DbTransformer.__init__(self, kwargs["database"])

    def orm(self) -> NBADownloads:
        return NBADownloads()

    def transform(self) -> pd.DataFrame:
        return self._injecting_df
