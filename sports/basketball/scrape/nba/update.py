import warnings

warnings.filterwarnings('ignore')
from sports.basketball.scrape.nba.boxscore import Boxscore
from sports.basketball.scrape.nba.game_detail import GameDetail
from sports.basketball.scrape.nba.play_by_play import PlayByPlay
from sports.basketball.scrape.nba.scoreboard import Scoreboard
from sports.basketball.scrape.nba.shotchart import <PERSON><PERSON><PERSON>
from sports.basketball.scrape.nba.player import Player
from sports.basketball.scrape.nba.constants import PRESEASON_KEY, PLAYOFFS_KEY, NBA_KEY, WNBA_KEY, league_to_db
from sports.basketball.database.season_utils import game_date_to_season
from sports.basketball.database.database import Database
from slackwrapper.slack_wrapper import SlackWrapper
from datetime import date, datetime
import pandas as pd
import time

SLACK = SlackWrapper(bot_name="NBA.com Scraper", icon_emoji=":basketball:", channel="#scrape-updates")


def update_scoreboards(db, league):
    print('Updating Scoreboards/Games')
    # Check days that have any game that is not final
    incomplete_games = db.query(
        "select DISTINCT(game_date_est) from games where (league=" + league + " and status!='Final' and status!='CNCL' and status NOT LIKE 'Ignore%%')")
    for game_est in incomplete_games['game_date_est']:
        game_date = date(year=game_est.year, month=game_est.month, day=game_est.day)
        s = Scoreboard(day=game_date, league=league)
        if s.num_games > 0:
            SLACK.display("Scoreboard: ", s)
        time.sleep(2)

    # Check all days since the last game until today
    last = db.query("select MAX(game_date_est) as last from games where league=" + league)['last'][0]
    end_date = datetime.now().date()
    start_date = date(year=last.year, month=last.month, day=last.day)
    daterange = pd.date_range(start_date, end_date, closed='right').to_pydatetime().tolist()
    for dt in reversed(daterange):
        d = dt.date()
        # Skip if date not part of a season
        if game_date_to_season(league_to_db(league), d) is None:
            continue
        s = Scoreboard(day=d, league=league)
        if s.num_games > 0:
            SLACK.display("Scoreboard: ", s)
        time.sleep(2)


def update_boxscores(db, league):
    print('Updating Boxscores')
    sql = "SELECT DISTINCT(game_id_string), season FROM games WHERE (NOT EXISTS (SELECT 1 FROM boxscores WHERE boxscores.game_id = games.game_id) OR status!='Final') AND status!='CNCL' and status NOT LIKE 'Ignore%%' AND season>1985 and league=" + league
    missing_boxscores = db.query(sql)
    for i, x in enumerate(missing_boxscores.values):
        if league == NBA_KEY:
            b = Boxscore(game_id=x[0], season=x[1])
        else:
            b = GameDetail(game_id=x[0], season=x[1], league=league)
        SLACK.display("Boxscore: ", b)
        time.sleep(2)


def update_playbyplays(db, league):
    print('Updating Play by Plays')
    sql = "SELECT DISTINCT(game_id_string), season FROM games WHERE (NOT EXISTS (SELECT 1 FROM playbyplays WHERE playbyplays.game_id = games.game_id) OR status!='Final') AND (season_type!='preseason' or season>2017) AND season_type!='all-star' AND season>1998 AND status!='CNCL' and status NOT LIKE 'Ignore%%' and league=" + league
    missing_pbps = db.query(sql)
    for i, x in enumerate(missing_pbps.values):
        pbp = PlayByPlay(game_id=x[0], season=x[1])
        SLACK.display("Play-by-Play: ", pbp)
        time.sleep(2)


def update_shotcharts(db, league):
    print('Updating Shot Charts')
    sql = "SELECT DISTINCT(game_id_string), season FROM games WHERE (NOT EXISTS (SELECT 1 FROM shotcharts WHERE shotcharts.game_id = games.game_id) OR status!='Final') AND season_type!='preseason' AND season_type!='all-star' AND season>1998 AND status!='CNCL' and status NOT LIKE 'Ignore%%' and league=" + league
    missing_charts = db.query(sql)
    for i, x in enumerate(missing_charts.values):
        sc = ShotChart(game_id=x[0], season=x[1], league=league)
        if len(sc.dataframe) <= 1:
            # Try playoffs
            sc = ShotChart(game_id=x[0], season=x[1], season_type=PLAYOFFS_KEY)
        SLACK.display("Shotchart: ", sc)
        time.sleep(2)


def update_players(db):
    print('Updating Players')
    sql = "SELECT DISTINCT(player_id) FROM boxscores INNER JOIN teams on boxscores.team_id=teams.team_id WHERE NOT EXISTS (SELECT 1 FROM players WHERE players.player_id = boxscores.player_id)"
    missing_players = db.query(sql)
    for i, x in enumerate(missing_players.values):
        try:
            p = Player(player_id=str(x[0]))
            SLACK.display(p.dataframe[['first_name', 'last_name']])
        except:
            p = [{'player_id': x[0], 'first_name': 'unknown', 'last_name': 'unknown'}]
            df = pd.DataFrame(p)
            db.insert(df, table='players')
            SLACK.display('Raw insert')
            SLACK.display(df)
        time.sleep(2)


def update_game_stats(db, league):
    update_boxscores(db, league=league)
    update_playbyplays(db, league=league)
    if league == NBA_KEY:
        update_shotcharts(db, league=league)


def update_league(db, league):
    update_game_stats(db, league)
    update_scoreboards(db, league)
    update_game_stats(db, league)


if __name__ == '__main__':
    db = Database()
    SLACK.display("NBA")
    update_league(db, NBA_KEY)
    SLACK.display("WNBA")
    update_league(db, WNBA_KEY)

    update_players(db)
    SLACK.display("Done")
