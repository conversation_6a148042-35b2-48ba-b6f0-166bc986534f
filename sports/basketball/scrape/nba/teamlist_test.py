import unittest
import os
import shutil
import json
import requests_mock
from sports.basketball.scrape.nba.teamlist import Team<PERSON>ist
from sports.basketball.scrape.nba.constants import NBA_KEY
from sports.basketball.database.postgress_test_case import PostgressTestCase

TEST_BACKUP_PATH = "/tmp/teamlist_test/"
with open(os.path.dirname(__file__) + "/test_data/teamlist.json", "r") as input_file:
    JSON_RESPONSE = json.loads(input_file.read())


class TestTeamList(PostgressTestCase, unittest.TestCase):
    def setUp(self):
        super(TestTeamList, self).setUp()

    def test_default_extract(self):
        shutil.rmtree(TEST_BACKUP_PATH, ignore_errors=True)
        self.assertFalse(os.path.isdir(TEST_BACKUP_PATH))

        # Should fetch since no file
        with requests_mock.Mocker() as m:
            m.get(
                requests_mock.ANY,
                json=JSON_RESPONSE,
                status_code=200,
            )
            teamlist = TeamList(backup_path=TEST_BACKUP_PATH)

        self.assertTrue(os.path.isfile(teamlist.output_path))
        self.assertGreater(len(teamlist.json), 0)
        df = teamlist.dataframe
        self.assertEqual(df.shape[0], 45)
        self.assertEqual(df.shape[1], 5)

        # Should load since file exists
        teamlist2 = TeamList(backup_path=TEST_BACKUP_PATH)
        self.assertTrue(os.path.isfile(teamlist2.output_path))
        self.assertGreater(len(teamlist2.json), 0)
        df2 = teamlist2.dataframe
        self.assertEqual(df2.shape[0], 45)
        self.assertEqual(df2.shape[1], 5)

    def test_database_load(self):
        sql = "select count(*) from teams;"
        start_count = self.db.count(sql)
        self.assertEqual(start_count, 0)

        with requests_mock.Mocker() as m:
            m.get(
                requests_mock.ANY,
                json=JSON_RESPONSE,
                status_code=200,
            )
            teamlist = TeamList(database=self.db, backup_path=TEST_BACKUP_PATH)

        # Check we instered the correct number of rows
        end_count = self.db.count(sql)
        self.assertEqual(end_count, teamlist.dataframe.shape[0])
        # Smoke test one entry
        abb = self.db.query("select abbreviation from teams where team_id=1610612738")
        self.assertEqual(abb.values[0], "BOS")


if __name__ == "__main__":
    unittest.main()
