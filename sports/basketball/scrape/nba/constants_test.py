import unittest

import sports.basketball.scrape.nba.constants as constants
import sports.basketball.database.league_utils as league_utils


class TestConstants(unittest.TestCase):
    def test_league_to_db(self):
        self.assertEqual(constants.league_to_db("NBA"), league_utils.League.NBA)
        self.assertEqual(constants.league_to_db("WNBA"), league_utils.League.WNBA)

    def test_infer_season(self):
        self.assertEqual(constants.infer_season("0021900112"), 2019)
        self.assertEqual(constants.infer_season("0045600112"), 1956)

    @unittest.expectedFailure
    def test_failed_infer_season_bad_string(self):
        self.assertEqual(constants.infer_season("0090112"))

    @unittest.expectedFailure
    def test_failed_infer_season_unknown_season(self):
        self.assertEqual(constants.infer_season("0023500112"))

    def test_infer_season_type(self):
        self.assertEqual(
            constants.infer_season_type("0021900112"), constants.REGULAR_SEASON_KEY
        )
        self.assertEqual(
            constants.infer_season_type("0045600112"), constants.PLAYOFFS_KEY
        )
        self.assertEqual(
            constants.infer_season_type("0055600112"), constants.PLAY_IN_KEY
        )

    @unittest.expectedFailure
    def test_failed_infer_season_type_bad_string(self):
        self.assertEqual(constants.infer_season_type("0090112"))

    def test_infer_league(self):
        self.assertEqual(constants.infer_league("0021900112"), constants.NBA_KEY)
        self.assertEqual(constants.infer_league("1045600112"), constants.WNBA_KEY)

    @unittest.expectedFailure
    def test_failed_infer_league_bad_string(self):
        self.assertEqual(constants.infer_season("0090112"))

    @unittest.expectedFailure
    def test_failed_infer_league_bad_league_id(self):
        self.assertEqual(constants.infer_season("5121900112"))


if __name__ == "__main__":
    unittest.main()
