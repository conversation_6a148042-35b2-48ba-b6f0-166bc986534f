import pandas as pd

from sports.basketball.database.boxscores import Boxscores
from sports.basketball.database.dbtransformer import DbTrans<PERSON>
from sports.basketball.scrape.nba.constants import league_to_db
from sports.basketball.scrape.nba.endpoint import Endpoint


class GameDetail(Endpoint, DbTransformer):
    """Downloads the boxscore for a given game"""

    def __init__(self, game_id, season, league, **kwargs):
        self._game_id = game_id
        self._season = season
        self._league = league_to_db(league).name.lower()
        Endpoint.__init__(self, **kwargs)
        if "database" in kwargs:
            DbTransformer.__init__(self, kwargs["database"])

    @property
    def _endpoint(self):
        return "https://data.{league}.com/data/v2015/json/mobile_teams/{league}/{season}/scores/gamedetail/{game_id}_gamedetail.json".format(
            league=self._league, season=self._season, game_id=self._game_id
        )

    @property
    def dataframe(self):
        h = pd.DataFrame(self.json["g"]["hls"]["pstsg"])
        h["team_id"] = self.json["g"]["hls"]["tid"]
        h["game_id"] = self.json["g"]["gid"]
        v = pd.DataFrame(self.json["g"]["vls"]["pstsg"])
        v["team_id"] = self.json["g"]["vls"]["tid"]
        v["game_id"] = self.json["g"]["gid"]
        return h.append(v).reset_index()

    @property
    def _params(self):
        return None

    @property
    def _headers(self):
        return None

    @property
    def _filename(self):
        return "game_detail_{game_id}.json".format(game_id=str(self._game_id))

    def orm(self):
        return Boxscores()

    def transform(self):
        df = self.dataframe.rename(
            {
                "pid": "player_id",
                "pos": "start_position",
                "memo": "comment",
                "totsec": "seconds",
                "tpm": "fg3m",
                "tpa": "fg3a",
                "tov": "to",
                "pm": "plus_minus",
            },
            axis=1,
        )
        df["start_position"] = df["start_position"].replace(["PG", "SG"], "G")
        df["start_position"] = df["start_position"].replace(["SF", "PF"], "F")
        df.fillna(0, inplace=True)
        if df["seconds"].sum() < 1:
            return pd.DataFrame()

        return df[
            [
                "game_id",
                "team_id",
                "player_id",
                "start_position",
                "comment",
                "seconds",
                "fgm",
                "fga",
                "fg3m",
                "fg3a",
                "ftm",
                "fta",
                "oreb",
                "dreb",
                "reb",
                "ast",
                "stl",
                "blk",
                "to",
                "pf",
                "pts",
                "plus_minus",
            ]
        ]

    def __str__(self):
        home_team = self.json["g"]["hls"]["ta"]
        visior_team = self.json["g"]["vls"]["ta"]
        return "{} at {} : Rows={}   ({})".format(
            visior_team, home_team, len(self.dataframe.index), self._game_id
        )
