from sports.basketball.scrape.nba.constants import NBA_KEY, WNBA_KEY
from sports.basketball.scrape.nba.endpoint import Endpoint


class Scoreboard(Endpoint):
    """Downloads the scoreboard for a given day"""

    def __init__(self, day, league: str = NBA_KEY, **kwargs):
        self._day = day
        self._league = league
        Endpoint.__init__(self, **kwargs)

    @property
    def _endpoint(self):
        if self._league == WNBA_KEY:
            return "https://data.wnba.com/data/5s/v2015/json/mobile_teams/wnba/2024/scores/10_todays_scores.json"
            # return "https://data.wnba.com/data/5s/v2015/json/mobile_teams/wnba/2022/league/10_rolling_schedule.json"
        elif self._league == NBA_KEY:
            return "https://cdn.nba.com/static/json/liveData/scoreboard/todaysScoreboard_00.json"

    @property
    def _params(self):
        return None

    @property
    def _headers(self):
        return None

    @property
    def _filename(self):
        game_date_file = "{year}{month:02d}{day:02d}".format(
            month=self._day.month, day=self._day.day, year=self._day.year
        )
        return "todays_scoreboard_" + str(self._league) + "_" + game_date_file + ".json"

    @property
    def num_games(self):
        return len(self.dataframe.index)

    def orm(self):
        return Games()

    def transform(self):
        None

    def dataframe(self):
        return None

    def dataframe_at(self, index=0):
        return None

    def __str__(self):
        game_code = self.dataframe["gamecode"].values[0]
        game_id = self.dataframe["game_id"].values[0]
        game_status_text = self.dataframe["game_status_text"].values[0]
        return "{} - {}  ({})".format(game_code, game_status_text, game_id)
