import unittest
import os
import shutil
import pandas as pd
import json
import requests_mock
from sports.basketball.scrape.nba.boxscore import Boxscore
from sports.basketball.database.postgress_test_case import PostgressTestCase

GAME_ID = "0021700493"

TEST_BACKUP_PATH = "/tmp/boxscore_test/"
TEST_FILE_PATH = f"{os.path.dirname(__file__)}/test_data"
with open(f"{TEST_FILE_PATH}/boxscore_0021700493.json", "r") as input_file:
    JSON_RESPONSE = json.loads(input_file.read())


class TestBoxscore(PostgressTestCase, unittest.TestCase):
    def setUp(self):
        super(TestBoxscore, self).setUp()

    def test_default_extract(self):
        shutil.rmtree(TEST_BACKUP_PATH, ignore_errors=True)
        self.assertFalse(os.path.isdir(TEST_BACKUP_PATH))

        # Should fetch since no file
        with requests_mock.Mocker() as m:
            m.get(
                requests_mock.ANY,
                json=JSON_RESPONSE,
                status_code=200,
            )
            box = Boxscore(game_id=GAME_ID, backup_path=TEST_BACKUP_PATH)
        self.assertTrue(os.path.isfile(box.output_path))
        self.assertTrue(box.loaded_from_network)
        self.assertGreater(len(box.json), 0)
        df = box.dataframe
        self.assertEqual(df.shape[0], 26)
        self.assertEqual(df.shape[1], 29)

        # Should load since file exists
        box2 = Boxscore(game_id=GAME_ID, backup_path=TEST_BACKUP_PATH)
        self.assertTrue(box2.loaded_from_disk)
        self.assertGreater(len(box2.json), 0)
        df2 = box2.dataframe
        self.assertEqual(df2.shape[0], 26)
        self.assertEqual(df2.shape[1], 29)

    def test_force_fetch(self):
        shutil.rmtree(TEST_BACKUP_PATH, ignore_errors=True)
        self.assertFalse(os.path.isdir(TEST_BACKUP_PATH))

        with requests_mock.Mocker() as m:
            m.get(
                requests_mock.ANY,
                json=JSON_RESPONSE,
                status_code=200,
            )
            box = Boxscore(
                game_id=GAME_ID,
                force_fetch=True,
                backup_path=TEST_BACKUP_PATH,
            )
        self.assertTrue(os.path.isfile(box.output_path))
        self.assertTrue(box.loaded_from_network)
        self.assertGreater(len(box.json), 0)
        df = box.dataframe
        self.assertEqual(df.shape[0], 26)
        self.assertEqual(df.shape[1], 29)

    def test_force_load(self):
        # Fetch to make sure we have a saved file
        self.test_force_fetch()

        # Now force loading
        box = Boxscore(
            game_id=GAME_ID,
            force_load_from_disk=True,
            backup_path=TEST_BACKUP_PATH,
        )
        self.assertTrue(box.loaded_from_disk)
        self.assertGreater(len(box.json), 0)
        df = box.dataframe
        self.assertEqual(df.shape[0], 26)
        self.assertEqual(df.shape[1], 29)

    @unittest.expectedFailure
    def test_failed_load(self):
        shutil.rmtree(TEST_BACKUP_PATH, ignore_errors=True)
        self.assertFalse(os.path.isdir(TEST_BACKUP_PATH))

        box = Boxscore(
            game_id=GAME_ID,
            force_load_from_disk=True,
            backup_path=TEST_BACKUP_PATH,
        )

    def test_database_write(self):
        df = pd.DataFrame(
            {
                "game_id": ["0021700493"],
                "season": [2017],
                "home_team_id": [1],
                "visitor_team_id": [1],
                "league": [1],
            }
        )
        self.db.insert(df, table="games")
        sql = "select count(*) from boxscores;"
        start_count = self.db.count(sql)
        self.assertEqual(start_count, 0)

        with requests_mock.Mocker() as m:
            m.get(
                requests_mock.ANY,
                json=JSON_RESPONSE,
                status_code=200,
            )
            box = Boxscore(
                database=self.db,
                game_id=GAME_ID,
                backup_path=TEST_BACKUP_PATH,
            )
        # Check we inserted the correct number of rows
        end_count = self.db.count(sql)
        self.assertEqual(end_count, box.dataframe.shape[0])
        # Smoke test one entry
        pts = self.db.query("select pts from boxscores where player_id=203954")
        self.assertEqual(pts.values[0], 25)

    def test_game_id(self):
        with requests_mock.Mocker() as m:
            m.get(
                requests_mock.ANY,
                json=JSON_RESPONSE,
                status_code=200,
            )
            box = Boxscore(
                game_id=GAME_ID,
                force_fetch=True,
                backup_path=TEST_BACKUP_PATH,
            )
        self.assertEqual(box.game_id, GAME_ID)


if __name__ == "__main__":
    unittest.main()
