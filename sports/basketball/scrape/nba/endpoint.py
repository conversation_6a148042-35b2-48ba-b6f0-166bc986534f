import json
import os
import os.path
from abc import <PERSON><PERSON><PERSON>

from enum import Enum
from pandas import DataFrame
from pathlib import Path
from requests import get


class DataSource(Enum):
    NO_DATA = 1
    NETWORK = 2
    DISK = 3


class Endpoint(object):
    __metaclass__ = ABCMeta
    """Super class for all endpoint based objects"""

    def __init__(self, **kwargs):
        self._data_source = DataSource.NO_DATA
        self._json = None

        if "backup_path" in kwargs:
            self._backup_path = kwargs["backup_path"]
        else:
            self._backup_path = os.path.expanduser("~") + "/filmlab/scrape/nba/"

        if "force_fetch" in kwargs and kwargs["force_fetch"] is True:
            self.fetch_and_save_to_disk()
            return

        if "force_load_from_disk" in kwargs and kwargs["force_load_from_disk"] is True:
            self.load_from_disk()
            return

        self.load_from_disk_or_fetch()

    @property
    def json(self) -> dict:
        return self._json

    @property
    def data_source(self) -> DataSource:
        return self._data_source

    @property
    def loaded_from_disk(self) -> bool:
        return self._data_source == DataSource.DISK

    @property
    def loaded_from_network(self) -> bool:
        return self._data_source == DataSource.NETWORK

    @property
    def dataframe(self) -> DataFrame:
        return self._json_to_dataframe(0)

    def dataframe_at(self, index=0) -> DataFrame:
        return self._json_to_dataframe(index)

    def fetch(self) -> None:
        _get = get(
            self._endpoint, params=self._params, headers=self._headers, timeout=5.0
        )
        _get.raise_for_status()
        self._json = _get.json()
        self._data_source = DataSource.NETWORK

    def fetch_and_save_to_disk(self) -> None:
        self.fetch()
        self.save_to_disk()

    def save_to_disk(self) -> None:
        os.makedirs(self._backup_path, exist_ok=True)
        with open(self.output_path, "w") as outfile:
            json.dump(self._json, outfile)

    def load_from_disk(self) -> None:
        if not Path(self.output_path).exists():
            raise FileNotFoundError
        with open(self.output_path, "r") as input_file:
            self._json = json.loads(input_file.read())
        self._data_source = DataSource.DISK

    def load_from_disk_or_fetch(self) -> None:
        try:
            self.load_from_disk()
        except FileNotFoundError:
            self.fetch_and_save_to_disk()

    @property
    def output_path(self) -> str:
        return str(self._backup_path) + str(self._filename)

    @property
    def _endpoint(self) -> str:
        return "Should never get here"

    @property
    def _params(self):
        return "Should never get here"

    @property
    def _filename(self) -> str:
        return "Should never get here"

    @property
    def _headers(self) -> dict:
        return dict(
            {
                "Host": "stats.nba.com",
                "Connection": "keep-alive",
                "Accept": "application/json, text/plain, */*",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.97 Safari/537.36",
                "Referer": "https://stats.nba.com/",
                "Accept-Encoding": "gzip, deflate, br",
                "Accept-Language": "en-US,en;q=0.9",
            }
        )

    # Converts the janky dictionary that the api gives to a pandas data frame
    # index is which of the list of dictionaries we return.
    def _json_to_dataframe(self, index) -> DataFrame:
        try:
            headers = self._json["resultSets"][index]["headers"]
            values = self._json["resultSets"][index]["rowSet"]
        except KeyError:
            # This is so ugly but this is what you get when your data comes out
            # in not a standard format
            try:
                headers = self._json["resultSet"][ndx]["headers"]
                values = self._json["resultSet"][ndx]["rowSet"]
            except KeyError:
                # Added for results that only include one set (ex. LeagueLeaders)
                headers = json_data["resultSet"]["headers"]
                values = json_data["resultSet"]["rowSet"]
        headers = [x.lower() for x in headers]
        return DataFrame(values, columns=headers)
