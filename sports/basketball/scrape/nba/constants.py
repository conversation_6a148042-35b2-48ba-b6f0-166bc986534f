from datetime import date
import sports.basketball.database.league_utils as league_utils

NBA_KEY = "00"
WNBA_KEY = "10"

REGULAR_SEASON_KEY = "Regular Season"
PLAY_IN_KEY = "PlayIn"
PLAYOFFS_KEY = "Playoffs"
PRESEASON_KEY = "Preseason"
SUMMER_LEAGUE_KEY = "Summer"
ALL_STAR_KEY = "AllStar"

NBA_PRESEASON_SEASON_DATES = {2019: (date(day=30, month=9, year=2019), date(day=18, month=10, year=2019))}

NBA_REGULAR_SEASON_DATES = {
    1946: (date(day=1, month=11, year=1946), date(day=31, month=3, year=1947)),
    1947: (date(day=12, month=11, year=1947), date(day=21, month=3, year=1948)),
    1948: (date(day=1, month=11, year=1948), date(day=20, month=3, year=1949)),
    1949: (date(day=29, month=10, year=1949), date(day=19, month=3, year=1950)),
    1950: (date(day=31, month=10, year=1950), date(day=18, month=3, year=1951)),
    1951: (date(day=1, month=11, year=1951), date(day=16, month=3, year=1952)),
    1952: (date(day=31, month=10, year=1952), date(day=18, month=3, year=1953)),
    1953: (date(day=30, month=10, year=1953), date(day=14, month=3, year=1954)),
    1954: (date(day=30, month=10, year=1954), date(day=14, month=3, year=1955)),
    1955: (date(day=5, month=11, year=1955), date(day=14, month=3, year=1956)),
    1956: (date(day=27, month=10, year=1956), date(day=13, month=3, year=1957)),
    1957: (date(day=22, month=10, year=1957), date(day=12, month=3, year=1958)),
    1958: (date(day=19, month=10, year=1958), date(day=11, month=3, year=1959)),
    1959: (date(day=17, month=10, year=1959), date(day=10, month=3, year=1960)),
    1960: (date(day=19, month=10, year=1960), date(day=12, month=3, year=1961)),
    1961: (date(day=19, month=10, year=1961), date(day=14, month=3, year=1962)),
    1962: (date(day=16, month=10, year=1962), date(day=17, month=3, year=1963)),
    1963: (date(day=16, month=10, year=1963), date(day=18, month=3, year=1964)),
    1964: (date(day=16, month=10, year=1964), date(day=21, month=3, year=1965)),
    1965: (date(day=15, month=10, year=1965), date(day=22, month=3, year=1966)),
    1966: (date(day=15, month=10, year=1966), date(day=19, month=3, year=1967)),
    1967: (date(day=13, month=10, year=1967), date(day=24, month=3, year=1968)),
    1968: (date(day=15, month=10, year=1968), date(day=4, month=4, year=1969)),
    1969: (date(day=14, month=10, year=1969), date(day=15, month=4, year=1970)),
    1970: (date(day=13, month=10, year=1970), date(day=31, month=3, year=1971)),
    1971: (date(day=12, month=10, year=1971), date(day=29, month=3, year=1972)),
    1972: (date(day=10, month=10, year=1972), date(day=28, month=3, year=1973)),
    1973: (date(day=9, month=10, year=1973), date(day=28, month=3, year=1974)),
    1974: (date(day=17, month=10, year=1974), date(day=6, month=4, year=1975)),
    1975: (date(day=23, month=10, year=1975), date(day=11, month=4, year=1976)),
    1976: (date(day=21, month=10, year=1976), date(day=10, month=4, year=1977)),
    1977: (date(day=18, month=10, year=1977), date(day=9, month=4, year=1978)),
    1978: (date(day=13, month=10, year=1978), date(day=8, month=4, year=1979)),
    1979: (date(day=12, month=10, year=1979), date(day=30, month=3, year=1980)),
    1980: (date(day=10, month=10, year=1980), date(day=29, month=3, year=1981)),
    1981: (date(day=30, month=10, year=1981), date(day=18, month=4, year=1982)),
    1982: (date(day=29, month=10, year=1982), date(day=17, month=4, year=1983)),
    1983: (date(day=28, month=10, year=1983), date(day=15, month=4, year=1984)),
    1984: (date(day=26, month=10, year=1984), date(day=14, month=4, year=1985)),
    1985: (date(day=25, month=10, year=1985), date(day=13, month=4, year=1986)),
    1986: (date(day=31, month=10, year=1986), date(day=19, month=4, year=1987)),
    1987: (date(day=6, month=11, year=1987), date(day=24, month=4, year=1988)),
    1988: (date(day=4, month=11, year=1988), date(day=23, month=4, year=1989)),
    1989: (date(day=3, month=11, year=1989), date(day=22, month=4, year=1990)),
    1990: (date(day=2, month=11, year=1990), date(day=21, month=4, year=1991)),
    1991: (date(day=1, month=11, year=1991), date(day=19, month=4, year=1992)),
    1992: (date(day=6, month=11, year=1992), date(day=25, month=4, year=1993)),
    1993: (date(day=5, month=11, year=1993), date(day=24, month=4, year=1994)),
    1994: (date(day=4, month=11, year=1994), date(day=23, month=4, year=1995)),
    1995: (date(day=3, month=11, year=1995), date(day=21, month=4, year=1996)),
    1996: (date(day=1, month=11, year=1996), date(day=20, month=4, year=1997)),
    1997: (date(day=31, month=10, year=1997), date(day=19, month=4, year=1998)),
    1998: (date(day=5, month=2, year=1999), date(day=5, month=5, year=1999)),
    1999: (date(day=2, month=11, year=1999), date(day=19, month=4, year=2000)),
    2000: (date(day=31, month=10, year=2000), date(day=18, month=4, year=2001)),
    2001: (date(day=30, month=10, year=2001), date(day=17, month=4, year=2002)),
    2002: (date(day=29, month=10, year=2002), date(day=16, month=4, year=2003)),
    2003: (date(day=28, month=10, year=2003), date(day=14, month=4, year=2004)),
    2004: (date(day=2, month=11, year=2004), date(day=20, month=4, year=2005)),
    2005: (date(day=1, month=11, year=2005), date(day=19, month=4, year=2006)),
    2006: (date(day=31, month=10, year=2006), date(day=18, month=4, year=2007)),
    2007: (date(day=30, month=10, year=2007), date(day=16, month=4, year=2008)),
    2008: (date(day=28, month=10, year=2008), date(day=15, month=4, year=2009)),
    2009: (date(day=27, month=10, year=2009), date(day=14, month=4, year=2010)),
    2010: (date(day=26, month=10, year=2010), date(day=13, month=4, year=2011)),
    2011: (date(day=25, month=12, year=2011), date(day=26, month=4, year=2012)),
    2012: (date(day=30, month=10, year=2012), date(day=17, month=4, year=2013)),
    2013: (date(day=29, month=10, year=2013), date(day=16, month=4, year=2014)),
    2014: (date(day=28, month=10, year=2014), date(day=15, month=4, year=2015)),
    2015: (date(day=27, month=10, year=2015), date(day=13, month=4, year=2016)),
    2016: (date(day=25, month=10, year=2016), date(day=12, month=4, year=2017)),
    2017: (date(day=17, month=10, year=2017), date(day=11, month=4, year=2018)),
    2018: (date(day=16, month=10, year=2018), date(day=10, month=4, year=2019)),
    2019: (date(day=21, month=10, year=2019), date(day=14, month=8, year=2020)),
    2020: (date(day=22, month=12, year=2020), date(day=16, month=5, year=2021)),
    2021: (date(day=19, month=10, year=2021), date(day=10, month=4, year=2022)),
    2022: (date(day=18, month=10, year=2022), date(day=9, month=4, year=2023)),
}

NBA_PLAYOFF_SEASON_DATES = {
    1946: (date(day=2, month=4, year=1947), date(day=22, month=4, year=1947)),
    1947: (date(day=23, month=3, year=1948), date(day=21, month=4, year=1948)),
    1948: (date(day=22, month=3, year=1949), date(day=13, month=4, year=1949)),
    1949: (date(day=20, month=3, year=1950), date(day=23, month=4, year=1950)),
    1950: (date(day=20, month=3, year=1951), date(day=21, month=4, year=1951)),
    1951: (date(day=18, month=3, year=1952), date(day=25, month=4, year=1952)),
    1952: (date(day=17, month=3, year=1953), date(day=10, month=4, year=1953)),
    1953: (date(day=16, month=3, year=1954), date(day=12, month=4, year=1954)),
    1954: (date(day=15, month=3, year=1955), date(day=10, month=4, year=1955)),
    1955: (date(day=15, month=3, year=1956), date(day=7, month=4, year=1956)),
    1956: (date(day=14, month=3, year=1957), date(day=13, month=4, year=1957)),
    1957: (date(day=15, month=3, year=1958), date(day=12, month=4, year=1958)),
    1958: (date(day=13, month=3, year=1959), date(day=9, month=4, year=1959)),
    1959: (date(day=11, month=3, year=1960), date(day=9, month=4, year=1960)),
    1960: (date(day=14, month=3, year=1961), date(day=11, month=4, year=1961)),
    1961: (date(day=16, month=3, year=1962), date(day=18, month=4, year=1962)),
    1962: (date(day=19, month=3, year=1963), date(day=24, month=4, year=1963)),
    1963: (date(day=21, month=3, year=1964), date(day=26, month=4, year=1964)),
    1964: (date(day=24, month=3, year=1965), date(day=25, month=4, year=1965)),
    1965: (date(day=23, month=3, year=1966), date(day=28, month=4, year=1966)),
    1966: (date(day=21, month=3, year=1967), date(day=24, month=4, year=1967)),
    1967: (date(day=22, month=3, year=1968), date(day=4, month=5, year=1968)),
    1968: (date(day=26, month=3, year=1969), date(day=7, month=5, year=1969)),
    1969: (date(day=25, month=3, year=1970), date(day=25, month=5, year=1970)),
    1970: (date(day=24, month=3, year=1971), date(day=18, month=5, year=1971)),
    1971: (date(day=28, month=3, year=1972), date(day=20, month=5, year=1972)),
    1972: (date(day=30, month=3, year=1973), date(day=12, month=5, year=1973)),
    1973: (date(day=29, month=3, year=1974), date(day=12, month=5, year=1974)),
    1974: (date(day=4, month=4, year=1975), date(day=25, month=5, year=1975)),
    1975: (date(day=8, month=4, year=1976), date(day=6, month=6, year=1976)),
    1976: (date(day=12, month=4, year=1977), date(day=5, month=6, year=1977)),
    1977: (date(day=11, month=4, year=1978), date(day=7, month=6, year=1978)),
    1978: (date(day=10, month=4, year=1979), date(day=1, month=6, year=1979)),
    1979: (date(day=2, month=4, year=1980), date(day=16, month=5, year=1980)),
    1980: (date(day=31, month=3, year=1981), date(day=14, month=5, year=1981)),
    1981: (date(day=20, month=4, year=1982), date(day=8, month=6, year=1982)),
    1982: (date(day=19, month=4, year=1983), date(day=31, month=5, year=1983)),
    1983: (date(day=17, month=4, year=1984), date(day=12, month=6, year=1984)),
    1984: (date(day=17, month=4, year=1985), date(day=9, month=6, year=1985)),
    1985: (date(day=17, month=4, year=1986), date(day=8, month=6, year=1986)),
    1986: (date(day=23, month=4, year=1987), date(day=14, month=6, year=1987)),
    1987: (date(day=28, month=4, year=1988), date(day=21, month=6, year=1988)),
    1988: (date(day=27, month=4, year=1989), date(day=13, month=6, year=1989)),
    1989: (date(day=26, month=4, year=1990), date(day=14, month=6, year=1990)),
    1990: (date(day=25, month=4, year=1991), date(day=12, month=6, year=1991)),
    1991: (date(day=23, month=4, year=1992), date(day=14, month=6, year=1992)),
    1992: (date(day=29, month=4, year=1993), date(day=20, month=6, year=1993)),
    1993: (date(day=28, month=4, year=1994), date(day=22, month=6, year=1994)),
    1994: (date(day=27, month=4, year=1995), date(day=14, month=6, year=1995)),
    1995: (date(day=25, month=4, year=1996), date(day=16, month=6, year=1996)),
    1996: (date(day=24, month=4, year=1997), date(day=13, month=6, year=1997)),
    1997: (date(day=23, month=4, year=1998), date(day=14, month=6, year=1998)),
    1998: (date(day=8, month=5, year=1999), date(day=25, month=6, year=1999)),
    1999: (date(day=22, month=4, year=2000), date(day=19, month=6, year=2000)),
    2000: (date(day=21, month=4, year=2001), date(day=15, month=6, year=2001)),
    2001: (date(day=20, month=4, year=2002), date(day=12, month=6, year=2002)),
    2002: (date(day=19, month=4, year=2003), date(day=15, month=6, year=2003)),
    2003: (date(day=17, month=4, year=2004), date(day=15, month=6, year=2004)),
    2004: (date(day=23, month=4, year=2005), date(day=23, month=6, year=2005)),
    2005: (date(day=22, month=4, year=2006), date(day=20, month=6, year=2006)),
    2006: (date(day=21, month=4, year=2007), date(day=14, month=6, year=2007)),
    2007: (date(day=19, month=4, year=2008), date(day=17, month=6, year=2008)),
    2008: (date(day=18, month=4, year=2009), date(day=14, month=6, year=2009)),
    2009: (date(day=17, month=4, year=2010), date(day=17, month=6, year=2010)),
    2010: (date(day=16, month=4, year=2011), date(day=12, month=6, year=2011)),
    2011: (date(day=28, month=4, year=2012), date(day=21, month=6, year=2012)),
    2012: (date(day=20, month=4, year=2013), date(day=20, month=6, year=2013)),
    2013: (date(day=19, month=4, year=2014), date(day=15, month=6, year=2014)),
    2014: (date(day=18, month=4, year=2015), date(day=16, month=6, year=2015)),
    2015: (date(day=16, month=4, year=2016), date(day=19, month=6, year=2016)),
    2016: (date(day=15, month=4, year=2017), date(day=12, month=6, year=2017)),
    2017: (date(day=14, month=4, year=2018), date(day=8, month=6, year=2018)),
    2018: (date(day=13, month=4, year=2019), date(day=13, month=6, year=2019)),
    2019: (date(day=17, month=8, year=2020), date(day=11, month=10, year=2020)),
    2020: (date(day=22, month=5, year=2021), date(day=20, month=7, year=2021)),
    2021: (date(day=16, month=4, year=2022), date(day=19, month=6, year=2022)),
}

WNBA_REGULAR_SEASON_DATES = {
    2018: (date(day=18, month=5, year=2018), date(day=19, month=8, year=2018)),
    2019: (date(day=24, month=5, year=2019), date(day=8, month=9, year=2019)),
}

WNBA_PLAYOFF_SEASON_DATES = {
    2018: (date(day=21, month=8, year=2018), date(day=13, month=9, year=2018)),
    2019: (date(day=11, month=9, year=2019), date(day=8, month=10, year=2019)),
}


def league_to_db(league: str) -> league_utils.League:
    assert league == "NBA" or league == "WNBA"
    if league == "NBA":
        return league_utils.League.NBA

    if league == "WNBA":
        return league_utils.League.WNBA


def infer_league(game_id: str) -> str:
    assert len(game_id) == 10
    league_id = game_id[0:2]
    assert league_id == NBA_KEY or league_id == WNBA_KEY
    return league_id


def infer_season(game_id: str) -> int:
    assert len(game_id) == 10
    return [int(year) for year in list(NBA_REGULAR_SEASON_DATES.keys()) if str(year).endswith(game_id[3:5])][0]


def infer_season_type(game_id: str) -> str:
    assert len(game_id) == 10
    key_char = game_id[2]
    if key_char == "4":
        return PLAYOFFS_KEY
    elif key_char == "5":
        return PLAY_IN_KEY
    else:
        return REGULAR_SEASON_KEY
