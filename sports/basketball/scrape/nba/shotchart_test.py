import unittest
import os
import shutil
import pandas as pd
import json
import requests_mock
from sports.basketball.scrape.nba.shotchart import <PERSON><PERSON><PERSON>
from sports.basketball.database.postgress_test_case import PostgressTestCase

SEASON = 2017
GAME_ID = "0021700001"

TEST_BACKUP_PATH = "/tmp/shotchart_test/"
with open(os.path.dirname(__file__) + "/test_data/shotchart.json", "r") as input_file:
    JSON_RESPONSE = json.loads(input_file.read())


class TestShotChart(PostgressTestCase, unittest.TestCase):
    def setUp(self):
        super(TestShotChart, self).setUp()

    def test_default_extract(self):
        shutil.rmtree(TEST_BACKUP_PATH, ignore_errors=True)
        self.assertFalse(os.path.isdir(TEST_BACKUP_PATH))

        # Should fetch since no file
        with requests_mock.Mocker() as m:
            m.get(
                requests_mock.ANY,
                json=JSON_RESPONSE,
                status_code=200,
            )
            sc = ShotChart(game_id=GAME_ID, season=SEASON, backup_path=TEST_BACKUP_PATH)

        self.assertTrue(os.path.isfile(sc.output_path))
        self.assertGreater(len(sc.json), 0)
        df = sc.dataframe
        self.assertEqual(df.shape[0], 171)
        self.assertEqual(df.shape[1], 24)

        # Should load since file exists
        sc2 = ShotChart(game_id=GAME_ID, season=SEASON, backup_path=TEST_BACKUP_PATH)
        self.assertGreater(len(sc2.json), 0)
        df2 = sc2.dataframe
        self.assertEqual(df2.shape[0], 171)
        self.assertEqual(df2.shape[1], 24)

    def test_database_load(self):
        df = pd.DataFrame(
            {
                "game_id": ["0021700001"],
                "season": [2017],
                "home_team_id": [1],
                "visitor_team_id": [1],
                "league": [1],
            }
        )
        self.db.insert(df, table="games")
        sql = "select count(*) from shotcharts;"
        start_count = self.db.count(sql)
        self.assertEqual(start_count, 0)

        with requests_mock.Mocker() as m:
            m.get(
                requests_mock.ANY,
                json=JSON_RESPONSE,
                status_code=200,
            )
            sc = ShotChart(
                database=self.db,
                game_id=GAME_ID,
                season=SEASON,
                backup_path=TEST_BACKUP_PATH,
            )

        # Check we instered the correct number of rows
        end_count = self.db.count(sql)
        self.assertEqual(end_count, sc.dataframe.shape[0])
        # Smoke test one entry
        loc_x = self.db.query("select loc_x from shotcharts where game_event_id=7")
        self.assertEqual(loc_x.values[0], -1)

    def test_min_to_seconds(self):
        self.assertEqual(ShotChart.min_to_seconds(minutes=2, seconds=3), 123)


if __name__ == "__main__":
    unittest.main()
