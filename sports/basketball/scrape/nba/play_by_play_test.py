import unittest
import os
import shutil
import pandas as pd
import json
import requests_mock
from sports.basketball.scrape.nba.play_by_play import PlayByPlay
from sports.basketball.database.postgress_test_case import PostgressTestCase

GAME_ID = "0021700493"

TEST_BACKUP_PATH = "/tmp/play_by_play_test/"
with open(os.path.dirname(__file__) + "/test_data/playbyplay_0021700493.json", "r") as input_file:
    JSON_RESPONSE = json.loads(input_file.read())


class TestPlayByPlay(PostgressTestCase, unittest.TestCase):
    def setUp(self):
        super(TestPlayByPlay, self).setUp()

    def test_default_extract(self):
        shutil.rmtree(TEST_BACKUP_PATH, ignore_errors=True)
        self.assertFalse(os.path.isdir(TEST_BACKUP_PATH))

        # Should fetch since no file
        with requests_mock.Mocker() as m:
            m.get(
                requests_mock.ANY,
                json=JSON_RESPONSE,
                status_code=200,
            )
            pbp = PlayByPlay(game_id=GAME_ID, backup_path=TEST_BACKUP_PATH)

        self.assertTrue(os.path.isfile(pbp.output_path))
        self.assertGreater(len(pbp.json), 0)
        df = pbp.dataframe
        self.assertEqual(df.shape[0], 497)
        self.assertEqual(df.shape[1], 34)

        # Should load since file exists
        pbp2 = PlayByPlay(game_id=GAME_ID, backup_path=TEST_BACKUP_PATH)
        self.assertTrue(pbp2.loaded_from_disk)
        df2 = pbp2.dataframe
        self.assertEqual(df2.shape[0], 497)
        self.assertEqual(df2.shape[1], 34)

    def test_database_load(self):
        df = pd.DataFrame(
            {
                "game_id": ["0021700493"],
                "season": [2017],
                "home_team_id": [1],
                "visitor_team_id": [1],
                "league": [1],
            }
        )
        self.db.insert(df, table="games")
        sql = "select count(*) from playbyplays;"
        start_count = self.db.count(sql)
        self.assertEqual(start_count, 0)

        with requests_mock.Mocker() as m:
            m.get(
                requests_mock.ANY,
                json=JSON_RESPONSE,
                status_code=200,
            )
            pbp = PlayByPlay(
                database=self.db,
                game_id=GAME_ID,
                backup_path=TEST_BACKUP_PATH,
            )
        # Check we inserted the correct number of rows
        end_count = self.db.count(sql)
        self.assertEqual(end_count, pbp.dataframe.shape[0])
        # Smoke test one entry
        event_type = self.db.query("select event_msg_type from playbyplays where event_num=2")
        self.assertEqual(event_type.values[0], 12)


if __name__ == "__main__":
    unittest.main()
