import unittest
import os
import shutil
import json
import requests_mock
from sports.basketball.scrape.nba.player import Player
from sports.basketball.database.postgress_test_case import PostgressTestCase

PLAYER_ID = "1627852"
TEST_BACKUP_PATH = "/tmp/player_test/"
with open(os.path.dirname(__file__) + "/test_data/player.json", "r") as input_file:
    JSON_RESPONSE = json.loads(input_file.read())


class TestPlayer(PostgressTestCase, unittest.TestCase):
    def setUp(self):
        super(TestPlayer, self).setUp()

    def test_default_extract(self):
        shutil.rmtree(TEST_BACKUP_PATH, ignore_errors=True)
        self.assertFalse(os.path.isdir(TEST_BACKUP_PATH))

        # Should fetch since no file
        with requests_mock.Mocker() as m:
            m.get(
                requests_mock.ANY,
                json=JSON_RESPONSE,
                status_code=200,
            )
            player = Player(PLAYER_ID, backup_path=TEST_BACKUP_PATH)

        self.assertTrue(os.path.isfile(player.output_path))
        self.assertGreater(len(player.json), 0)
        df = player.dataframe
        self.assertEqual(df.shape[0], 1)
        self.assertEqual(df.shape[1], 32)

        # Should load since file exists
        player2 = Player(PLAYER_ID, backup_path=TEST_BACKUP_PATH)
        self.assertGreater(len(player2.json), 0)
        df2 = player2.dataframe
        self.assertEqual(df2.shape[0], 1)
        self.assertEqual(df2.shape[1], 32)

    def test_database_load(self):
        sql = "select count(*) from players;"
        start_count = self.db.count(sql)
        self.assertEqual(start_count, 0)

        with requests_mock.Mocker() as m:
            m.get(
                requests_mock.ANY,
                json=JSON_RESPONSE,
                status_code=200,
            )
            player = Player(
                database=self.db,
                player_id=PLAYER_ID,
                backup_path=TEST_BACKUP_PATH,
            )

        # Check we instered the correct number of rows
        end_count = self.db.count(sql)
        self.assertEqual(end_count, player.dataframe.shape[0])
        # Smoke test one entry
        first_name = self.db.query(
            "select first_name from players where player_id=1627852"
        )
        self.assertEqual(first_name.values[0], "Nicolas")


if __name__ == "__main__":
    unittest.main()
