import unittest
import os
import shutil
import json
import requests_mock

from datetime import date
from sports.basketball.scrape.nba.scoreboard import Scoreboard
from sports.basketball.database.postgress_test_case import PostgressTestCase

GAME_DAY = date(2017, 12, 25)

TEST_BACKUP_PATH = "/tmp/scoreboard_test/"
with open(os.path.dirname(__file__) + "/test_data/scoreboard_00_20171225.json", "r") as input_file:
    JSON_RESPONSE = json.loads(input_file.read())


class TestScoreboard(PostgressTestCase, unittest.TestCase):
    def setUp(self):
        super(TestScoreboard, self).setUp()

    def test_default_extract(self):
        shutil.rmtree(TEST_BACKUP_PATH, ignore_errors=True)
        self.assertFalse(os.path.isdir(TEST_BACKUP_PATH))

        # Should fetch since no file
        with requests_mock.Mocker() as m:
            m.get(
                requests_mock.ANY,
                json=JSON_RESPONSE,
                status_code=200,
            )
            sc = Scoreboard(day=GAME_DAY, backup_path=TEST_BACKUP_PATH)

        self.assertTrue(os.path.isfile(sc.output_path))
        self.assertGreater(len(sc.json), 0)
        df = sc.dataframe
        self.assertEqual(df.shape[0], 5)
        self.assertEqual(df.shape[1], 14)

        # Should load since file exists
        sc2 = Scoreboard(day=GAME_DAY, backup_path=TEST_BACKUP_PATH)
        self.assertGreater(len(sc2.json), 0)
        df2 = sc2.dataframe
        self.assertEqual(df2.shape[0], 5)
        self.assertEqual(df2.shape[1], 14)

    def test_database_load(self):
        sql = "select count(*) from games;"
        start_count = self.db.count(sql)
        self.assertEqual(start_count, 0)

        with requests_mock.Mocker() as m:
            m.get(
                requests_mock.ANY,
                json=JSON_RESPONSE,
                status_code=200,
            )
            sc = Scoreboard(database=self.db, day=GAME_DAY, backup_path=TEST_BACKUP_PATH)

        # Check we insterted the correct number of rows
        end_count = self.db.count(sql)
        self.assertEqual(end_count, sc.dataframe.shape[0])
        # Smoke test one entry
        game_status = self.db.query("select status from games where game_id='0021700493'")
        self.assertEqual(game_status.values[0], "Final")


if __name__ == "__main__":
    unittest.main()
