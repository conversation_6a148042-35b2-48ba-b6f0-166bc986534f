package(default_visibility = ["//visibility:public"])
#
#py_library(
#    name = "fantasy",
#    deps = [
#        ":roster_optimizer",
#        ":yahoo_daily",
#    ],
#)
#
#py_library(
#    name = "roster_optimizer",
#    srcs = ["roster_optimizer.py"],
#    deps = [
#        "//third_party:pandas",
#        "//third_party:pulp",
#    ],
#)
#
#py_library(
#    name = "yahoo_daily",
#    srcs = ["yahoo_daily.py"],
#    deps = [
#        ":roster_optimizer",
#        "//sports/basketball/database",
#        "//third_party:pandas",
#    ],
#)
#
#py_binary(
#    name = "run_optimizer",
#    srcs = ["run_optimizer.py"],
#    deps = [
#        ":fantasy",
#        "//sports/basketball/database",
#        "//third_party:pandas",
#    ],
#)
