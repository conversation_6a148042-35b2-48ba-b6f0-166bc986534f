from pulp import *
import pandas as pd


# Note: This class is tested via the yahoo daily tests
class RosterOptimizer(object):
    def __init__(self, salary_cap, positions):
        self._salary_cap = salary_cap
        self._positions = positions
        self._require_constraints = {}
        self._prob = None


    def optimze(self, dataframe):
        assert 'position' in dataframe
        assert 'name' in dataframe
        assert 'salary' in dataframe
        assert 'points' in dataframe

        players = dataframe[["position", "name", "salary", "points"]].groupby(["position", "name", "salary", "points"]).agg("count")
        players = players.reset_index()

        salaries = {}
        points = {}
        for pos in players.position.unique():
            available_pos = players[players.position == pos]
            salary = list(available_pos[["name","salary"]].set_index("name").to_dict().values())[0]
            point = list(available_pos[["name","points"]].set_index("name").to_dict().values())[0]
            salaries[pos] = salary
            points[pos] = point

        _vars = {k: LpVariable.dict(k, v, cat="Binary") for k, v in points.items()}

        self._prob = LpProblem("Fantasy", LpMaximize)

        rewards = []
        costs = []
        # Setting up the reward
        for k, v in _vars.items():
            if k in self._require_constraints:
                for i in v:
                    if i == self._require_constraints[k]:
                        costs += lpSum([salaries[k][i] * _vars[k][i]])
                        rewards += lpSum([points[k][i] * _vars[k][i]])
            else:
                costs += lpSum([salaries[k][i] * _vars[k][i] for i in v])
                rewards += lpSum([points[k][i] * _vars[k][i] for i in v])
            self._prob += lpSum([_vars[k][i] for i in v]) <= self._positions[k]
            if k=='PF':
                for p in v.keys():
                    self._prob += lpSum([_vars['F'][p], _vars['PF'][p], _vars['UTIL'][p]]) <= 1
            if k=='SF':
                for p in v.keys():
                    self._prob += lpSum([_vars['F'][p], _vars['SF'][p], _vars['UTIL'][p]]) <= 1
            if k=='PG':
                for p in v.keys():
                    self._prob += lpSum([_vars['G'][p], _vars['PG'][p], _vars['UTIL'][p]]) <= 1
            if k=='SG':
                for p in v.keys():
                    self._prob += lpSum([_vars['G'][p], _vars['SG'][p], _vars['UTIL'][p]]) <= 1
            if k=='C':
                for p in v.keys():
                    self._prob += lpSum([_vars['C'][p], _vars['UTIL'][p]]) <= 1

        self._prob += lpSum(rewards)
        self._prob += lpSum(costs) <= self._salary_cap

        self._prob.solve()

    def add_require_constraint(self, position, name):
        self._require_constraints[position] = name

    @property
    def constraints(self):
        return self._prob.constraints

    @property
    def objective(self):
        return self._prob.objective

    @property
    def variables(self):
        return self._prob.variables

    def result(self):
        constraints = [str(const) for const in self.constraints.values()]
        players = []
        pos = []
        for v in self.variables():
            if v.varValue != 0:
                pos.append(v.name.split('_')[0])
                players.append(v.name.split('_')[1])
        return pd.DataFrame.from_items([('name', players), ('position', pos)])
