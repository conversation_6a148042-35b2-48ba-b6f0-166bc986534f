from __future__ import division
from os.path import expanduser
import pandas as pd
from sports.basketball.database.database import Database
from sports.basketball.fantasy.roster_optimizer import RosterOptimizer
import random

CONTEST_PATH = expanduser("~") + '/filmlab/fantasy/'
SALARY_CAP = 200


class Contest(object):

    def __init__(self, contest_id):
        self._contest_id = contest_id
        self._raw_dataframe = pd.read_csv(CONTEST_PATH + "yahoo_df_" + str(self._contest_id) + ".csv")
        self._raw_dataframe = self._raw_dataframe.rename(columns=lambda x: x.lower().replace(" ", ""))
        self._dataframe = pd.DataFrame(self._raw_dataframe)
        self._optimizer = RosterOptimizer(SALARY_CAP, {"PG": 1, "SG": 1, "G": 1, "SF": 1, "PF": 1, "F": 1, "C": 1, "UTIL": 1})
        self._roster = None
        self.clean()

    def clean(self):
        self._dataframe = self._dataframe.loc[self._dataframe.injurystatus != 'INJ']
        self._dataframe = self._dataframe.loc[self._dataframe.injurystatus != 'O']
        self._dataframe['name'] = (self._dataframe.firstname + self._dataframe.lastname).str.replace('[^a-zA-Z]', '')

    def compute(self, sample_func, **kwargs):
        self._dataframe[['values', 'points']] = self._dataframe.apply(sample_func, **kwargs)
        G = self._dataframe[self._dataframe.position.isin(["PG", "SG"])].copy()
        G.position = "G"
        F = self._dataframe[self._dataframe.position.isin(["SF", "PF"])].copy()
        F.position = "F"
        UTIL = self._dataframe[self._dataframe.position.isin(["PG", "SG", "SF", "PF", "C"])].copy()
        UTIL.position = "UTIL"
        self._dataframe = pd.concat([self._dataframe, F, G, UTIL])
        self._optimizer.optimze(self._dataframe)
        self._roster = pd.merge(self.dataframe, self._optimizer.result(), how='inner', on=['name','position']).drop_duplicates(subset=['name'])[['firstname', 'lastname', 'position', 'values', 'salary', 'points']]
        return self.roster

    def exclude_player(self, first_name, last_name):
        self._dataframe = self._dataframe[
            ~((self._dataframe.firstname == first_name) & (self._dataframe.lastname == last_name))]

    def force_player(self, position, first_name, last_name):
        self._optimizer.add_require_constraint(position,first_name+last_name)

    def sample(self, num_samples=10000):
        def _random_total(dataframe):
            total = 0
            for index, row in dataframe.iterrows():
                total += random.choice(row['values'])
            return total

        totals = []
        for i in range(num_samples):
            totals.append(_random_total(self.roster))
        return totals

    @property
    def raw_dataframe(self):
        return self._raw_dataframe

    @property
    def dataframe(self):
        return self._dataframe

    @property
    def roster(self):
        return self._roster


def mean_games_at_perc(row, games, percentage, season):
    db = Database()
    sql = "select boxscores.player_id, first_name, last_name, pts, reb, ast, stl, blk, boxscores.to from boxscores join players on players.player_id=boxscores.player_id join games on games.game_id=boxscores.game_id where seconds > 0 and first_name='{}' and last_name='{}' and season_type='regular' and season={} order by game_date_est desc limit {}".format(
        row.firstname.replace("'", "\''"), row.lastname.replace("'", "\''"), season, games)
    p_scores = db.query(sql)
    if not p_scores.empty:
        p_scores['yfd'] = p_scores.apply(fantasy_points, axis=1)
        return pd.Series([p_scores['yfd'].values, p_scores.describe(percentiles=[percentage/100.0])['yfd'][str(percentage) + '%']])
    return pd.Series([[], 0.0])


def fantasy_points(row):
    assert 'pts' in row
    assert 'reb' in row
    assert 'ast' in row
    assert 'stl' in row
    assert 'blk' in row
    assert 'to' in row

    return (1.0 * row.pts) + (1.2 * row.reb) + (1.5 * row.ast) + (3.0 * row.stl) + (3.0 * row.blk) + (-1.0 * row.to)
