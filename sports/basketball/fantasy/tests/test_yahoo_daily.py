import unittest
import pandas as pd

from sports.basketball.fantasy.yahoo_daily import Contest, fantasy_points, mean_games_at_perc
from sports.basketball.database.database import Database

GAME_ID = '21700312'
PLAYER_ID_KEVIN_DURANT = '201142'


def test_function(row):
    db = Database()
    sql = "select boxscores.player_id, first_name, last_name, pts, reb, ast, stl, blk, boxscores.to from boxscores join players on players.player_id=boxscores.player_id join games on games.game_id=boxscores.game_id where seconds > 0 and first_name='{}' and last_name='{}' and season_type='regular' and season=2017 order by game_date_est desc limit {}".format(row.firstname.replace("'", "\''"), row.lastname.replace("'", "\''"), 10)
    p_scores = db.query(sql)
    if not p_scores.empty:
        p_scores['yfd'] = p_scores.apply(fantasy_points, axis=1)
        return pd.Series([p_scores['yfd'].values, p_scores.describe(percentiles=[50/100.0])['yfd'][str(50) + '%']])
    return pd.Series([[], 0.0])


class TestYahooDaily(unittest.TestCase):
    def test_fantasy_points(self):
        db = Database()
        self.durant = db.query("select * from boxscores where game_id={} and player_id={}".format(GAME_ID, PLAYER_ID_KEVIN_DURANT))
        df = pd.DataFrame()
        df['yfp'] = self.durant.apply(fantasy_points, axis=1)
        self.assertAlmostEqual(df['yfp'][0], 44.9, delta=0.01)

    def test_mean_games_at_perc(self):
        df = pd.DataFrame.from_dict({'firstname': ['Kevin'], 'lastname': ['Durant']})
        df[['values', 'points']] = df.apply(mean_games_at_perc, games=100, percentage=75, season=2016, axis=1)
        self.assertAlmostEqual(df['points'][0], 54.72, delta=0.01)
        df[['values', 'points']] = df.apply(mean_games_at_perc, games=100, percentage=75, season=2017, axis=1)
        self.assertAlmostEqual(df['points'][0], 54.92, delta=0.01)
        df[['values', 'points']] = df.apply(mean_games_at_perc, games=10, percentage=75, season=2017, axis=1)
        self.assertAlmostEqual(df['points'][0], 56.12, delta=0.01)
        df[['values', 'points']] = df.apply(mean_games_at_perc, games=10, percentage=37, season=2017, axis=1)
        self.assertAlmostEqual(df['points'][0], 46.15, delta=0.01)

    def test_contest(self):
        contest = Contest(3416145)
        roster = contest.compute(test_function, axis=1)
        self.assertEqual(len(roster), 8)
        self.assertEqual(roster.sum()['points'], 317.9)


if __name__ == '__main__':
    unittest.main()
