import warnings

warnings.filterwarnings("ignore")

from sports.basketball.linear_champion.update import run_update as linear_update
from sports.basketball.scrape.fivethirtyeight.nba_elo_download import (
    execute_download as elo_download,
)
from sports.basketball.database.database import Database

if __name__ == "__main__":
    print("Running download")
    if elo_download():
        print("Calculating linear champion")
        linear_update(Database())
