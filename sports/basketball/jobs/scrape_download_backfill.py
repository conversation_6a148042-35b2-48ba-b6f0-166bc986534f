from absl import app
from absl import flags
from absl import logging
import pandas as pd
import time
from typing import List
from retrying import retry

from slack_wrapper.slack_wrapper import SlackWrapper
from sports.basketball.scrape.nba.constants import (
    NBA_REGULAR_SEASON_DATES,
    NBA_PLAYOFF_SEASON_DATES,
)
from sports.basketball.scrape.nba.nba_download import NBADownload
from sports.basketball.scrape.nba.boxscore import Boxscore
from sports.basketball.scrape.nba.play_by_play import PlayByPlay
from sports.basketball.scrape.nba.shotchart import ShotChart
from sports.basketball.scrape.nba.scoreboard import Scoreboard
from sports.basketball.database.database import Database

FLAGS = flags.FLAGS
flags.DEFINE_list("seasons", [], "Seasons to download")
flags.DEFINE_string("type", "Scoreboard", "Type of file to download")


def get_nba_download_row_count(database: Database) -> int:
    return database.count("select count(*) from nba_downloads")


def download_scoreboards(seasons: List[int]) -> bool:
    slack = SlackWrapper(
        bot_name="NBA.com Scraper",
        icon_emoji=":basketball:",
        channel="#scrape-updates",
    )

    db = Database()
    for season in seasons:
        logging.info(f"Season: {season}")
        slack.display("Starting with rows: ", get_nba_download_row_count(db))
        daterange = pd.date_range(
            NBA_REGULAR_SEASON_DATES[season][0],
            NBA_PLAYOFF_SEASON_DATES[season][1],
        )
        for d in daterange:
            sb = Scoreboard(day=d)
            logging.info(sb)
            NBADownload(scoreboard=sb, database=db)
            if sb.loaded_from_network:
                time.sleep(5)
        slack.display("Ending with rows: ", get_nba_download_row_count(db))
    return True


@retry(
    stop_max_attempt_number=7,
    wait_exponential_multiplier=10000,
)
def fetch_boxscore(game_id) -> Boxscore:
    b = Boxscore(game_id=game_id)
    if len(b.dataframe)==0:
        b = Boxscore(game_id=game_id, force_fetch=True)
    return b


def download_boxscores(seasons: List[int]) -> bool:
    db = Database()
    for season in seasons:
        logging.info(f"Season: {season}")
        sql = f"SELECT DISTINCT(game_id), season FROM nba_downloads WHERE season={season} AND season>1985 AND game_status_id=3 and num_box_score IS NULL;"
        missing_boxscores = db.query(sql)
        for i, x in enumerate(missing_boxscores.values):
            b = fetch_boxscore(game_id=x[0])
            if len(b.dataframe)>0:
                logging.info(b)
                NBADownload(box_score=b, database=db)
            else:
                logging.info('Failed', x)
            if b.loaded_from_network:
                time.sleep(5)
    return True


@retry(
    stop_max_attempt_number=7,
    wait_exponential_multiplier=10000,
)
def fetch_play_by_play(game_id) -> PlayByPlay:
    return PlayByPlay(game_id=game_id)


def download_play_by_plays(seasons: List[int]) -> bool:
    db = Database()
    for season in seasons:
        logging.info(f"Season: {season}")
        sql = f"SELECT DISTINCT(game_id), season FROM nba_downloads WHERE season={season} AND season>1998 AND game_status_id=3 and num_play_by_play IS NULL;"
        missing_play_by_plays = db.query(sql)
        for i, x in enumerate(missing_play_by_plays.values):
            p = fetch_play_by_play(game_id=x[0])
            logging.info(p)
            NBADownload(play_by_play=p, database=db)
            if p.loaded_from_network:
                time.sleep(5)
    return True


@retry(
    stop_max_attempt_number=7,
    wait_exponential_multiplier=10000,
)
def fetch_shotchat(game_id) -> ShotChart:
    return ShotChart(game_id=game_id)


def download_shotcharts(seasons: List[int]) -> bool:
    db = Database()
    for season in seasons:
        logging.info(f"Season: {season}")
        sql = f"SELECT DISTINCT(game_id), season FROM nba_downloads WHERE season={season} AND season>1998 AND game_status_id=3 and num_shot_chart IS NULL;"
        missing_shotcharts = db.query(sql)
        for i, x in enumerate(missing_shotcharts.values):
            s = fetch_shotchat(game_id=x[0])
            logging.info(s)
            NBADownload(shotchart=s, database=db)
            if s.loaded_from_network:
                time.sleep(5)
    return True


def main(argv):
    del argv  # Unused.
    assert len(FLAGS.seasons) > 0
    download_scoreboards(list(map(int, FLAGS.seasons)))
    download_boxscores(list(map(int, FLAGS.seasons)))
    download_play_by_plays(list(map(int, FLAGS.seasons)))
    download_shotcharts(list(map(int, FLAGS.seasons)))


if __name__ == "__main__":
    app.run(main)
