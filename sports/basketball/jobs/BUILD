load("@pip//:requirements.bzl", "requirement")

package(default_visibility = ["//visibility:public"])

py_binary(
    name = "linear_champion_update",
    srcs = ["linear_champion_update.py"],
    main = "linear_champion_update.py",
    deps = [
        "//sports/basketball/database",
        "//sports/basketball/linear_champion:update",
        "//sports/basketball/scrape/fivethirtyeight:nba_elo_download",
    ],
)

py_binary(
    name = "scrape_download_backfill",
    srcs = ["scrape_download_backfill.py"],
    main = "scrape_download_backfill.py",
    deps = [
        "//slack_wrapper",
        "//sports/basketball/database",
        "//sports/basketball/scrape/nba:nba_download",
        requirement("absl-py"),
        requirement("pandas"),
        requirement("retrying"),
    ],
)
