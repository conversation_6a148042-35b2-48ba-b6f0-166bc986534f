import warnings
warnings.filterwarnings('ignore')
from sports.basketball.stats.player_boxscore_calcs import PlayerBoxscoreCalcs
import basketball.stats.advanced as advanced
from sports.basketball.database.database import Database
import pandas as pd
import colorama


def __off_def_ratings(row, team, opponent):
    row['offensive_rating'] = advanced.offensive_rating(row, team, opponent)
    row['defensive_rating'] = advanced.defensive_rating(row, team, opponent)
    row['efg_percentage'] = advanced.effective_fg_percentage(row)
    row['ts_percentage'] = advanced.true_shooting_percentage(row)
    row['fg3_attempt_rate'] = advanced.three_point_attempt_rate(row)
    row['ft_attempt_rate'] = advanced.free_throw_attempt_rate(row)
    row['oreb_percentage'] = advanced.offensive_rebound_percentage(row, team, opponent)
    row['dreb_percentage'] = advanced.defensive_rebound_percentage(row, team, opponent)
    row['reb_percentage'] = advanced.total_rebound_percentage(row, team, opponent)
    row['ast_percentage'] = advanced.assist_percentage(row, team)
    row['stl_percentage'] = advanced.steal_percentage(row, team, opponent)
    row['blk_percentage'] = advanced.block_percentage(row, team, opponent)
    row['to_percentage'] = advanced.turnover_percentage(row)
    row['usage_percentage'] = advanced.usage_percentage(row, team)
    return row


def update_calculated_stats(db):
    print(colorama.Fore.LIGHTBLUE_EX + 'Updating Stats')
    sql = 'SELECT * FROM boxscores WHERE NOT EXISTS (SELECT 1 FROM players_boxscores_calcs WHERE boxscores.game_id = players_boxscores_calcs.game_id)'
    missing_boxscores=db.query(sql)
    bx_split = [v for k, v in missing_boxscores.groupby('game_id')]
    df = None
    for idx, b in enumerate(bx_split):
        split_df = [v for k, v in b.groupby('team_id')]
        if len(split_df) != 2:
            continue
        team_a = split_df[0].sum()
        team_b = split_df[1].sum()
        g = pd.concat([split_df[0].apply(__off_def_ratings, team=team_a, opponent=team_b, axis=1),
                       split_df[1].apply(__off_def_ratings, team=team_b, opponent=team_a, axis=1)])
        if df is not None:
            df = pd.concat([df, g])
        else:
            df = g
    if df is not None:
        print(df)
        PlayerBoxscoreCalcs(df)


if __name__ == '__main__':
    db = Database()
    update_calculated_stats(db)
