import pandas as pd


def fg_percentage(row: pd.core.series.Series):
    if row.fga == 0:
        return 0.0
    return row.fgm / row.fga


def ft_percentage(row: pd.core.series.Series):
    if row.fta == 0:
        return 0.0
    return row.ftm / row.fta


def fg3_percentage(row: pd.core.series.Series):
    if row.fg3a == 0:
        return 0.0
    return row.fg3m / row.fg3a


def minutes_played(row: pd.core.series.Series):
    return row.seconds / 60.0


def minutes_str(row: pd.core.series.Series):
    m, s = divmod(row.seconds, 60)
    return "{}:{:02d}".format(m, s)


def fg_str(row: pd.core.series.Series):
    return "{}-{}".format(row.fgm, row.fga)


def fg3_str(row: pd.core.series.Series):
    return "{}-{}".format(row.fg3m, row.fg3a)


def ft_str(row: pd.core.series.Series):
    return "{}-{}".format(row.ftm, row.fta)


def first_last_name(row: pd.core.series.Series):
    return "{} {}".format(row.first_name, row.last_name)
