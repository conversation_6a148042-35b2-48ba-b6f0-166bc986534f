package(default_visibility = ["//visibility:public"])

load("@pip//:requirements.bzl", "requirement")

py_library(
    name = "stats",
    deps = [
        ":advanced",
        ":basic",
    ],
)

py_library(
    name = "advanced",
    srcs = ["advanced.py"],
    data = [
        "test_data/boxscore_0021700312.json",
    ],
    deps = [
        ":basic",
        requirement("pandas"),
    ],
)

py_test(
    name = "advanced_test",
    srcs = [
        "advanced_test.py",
    ],
    data = [
        "test_data/boxscore_0021700312.json",
    ],
    deps = [
        "//sports/basketball/database:postgress_test_case",
        "//sports/basketball/scrape/nba:boxscore",
        "//sports/basketball/stats:advanced",
    ],
)

py_library(
    name = "basic",
    srcs = ["basic.py"],
    deps = [
        requirement("pandas"),
    ],
)

py_test(
    name = "basic_test",
    size = "small",
    srcs = [
        "basic_test.py",
    ],
    data = [
        "test_data/boxscore_0021700312.json",
    ],
    deps = [
        "//sports/basketball/database:postgress_test_case",
        "//sports/basketball/scrape/nba:boxscore",
        "//sports/basketball/stats:basic",
    ],
)

py_library(
    name = "shotcharts",
    srcs = ["shotcharts.py"],
    deps = [
        ":stats",
        requirement("pandas"),
        requirement("plotly"),
    ],
)

py_library(
    name = "player_boxscore_calcs",
    srcs = ["player_boxscore_calcs.py"],
    deps = [
        "//sports/basketball/database:dbtransformer",
        "//sports/basketball/database:players_boxscores_calcs",
    ],
)

py_binary(
    name = "update_player_boxscore_calcs",
    srcs = ["update_player_boxscore_calcs.py"],
    deps = [
        ":player_boxscore_calcs",
        ":stats",
        "//sports/basketball/database",
        requirement("colorama"),
    ],
)
