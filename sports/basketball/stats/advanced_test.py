import unittest
import os

import sports.basketball.stats.advanced as advanced
from sports.basketball.scrape.nba.boxscore import Boxscore
from sports.basketball.database.postgress_test_case import PostgressTestCase
import pandas as pd

# Golden State v Lakers, 2017-11-29
GAME_ID = "0021700312"
SEASON = 2017
PLAYER_ID_DRAYMOND_GREEN = "203110"
PLAYER_ID_KEVIN_DURANT = "201142"
PLAYER_ID_ANDRE_IGUODALA = "2738"
TEAM_ID_WARRIORS = "1610612744"
TEAM_ID_LAKERS = "1610612747"

TEST_BACKUP_PATH = os.path.dirname(__file__) + "/test_data/"


class TestAdvanced(PostgressTestCase, unittest.TestCase):
    def setUp(self):
        super(TestAdvanced, self).setUp()
        df = pd.DataFrame(
            {
                "game_id": [GAME_ID],
                "season": [SEASON],
                "home_team_id": [1610612747],
                "visitor_team_id": [1610612744],
                "league": [1],
            }
        )
        self.db.insert(df, table="games")
        Boxscore(
            game_id=GAME_ID,
            season=SEASON,
            backup_path=TEST_BACKUP_PATH,
            database=self.db,
            force_load_from_disk=True,
        )

        self.assertEqual(self.db.count("select count(*) from boxscores;"), 26)

        self.warriors = self.db.query(
            f"select * from boxscores where game_id='{GAME_ID}' and team_id={TEAM_ID_WARRIORS}"
        ).sum()
        self.lakers = self.db.query(
            f"select * from boxscores where game_id='{GAME_ID}' and team_id={TEAM_ID_LAKERS}"
        ).sum()
        self.draymond = self.db.query(
            f"select * from boxscores where game_id='{GAME_ID}' and player_id={PLAYER_ID_DRAYMOND_GREEN}"
        )
        self.durant = self.db.query(
            f"select * from boxscores where game_id='{GAME_ID}' and player_id={PLAYER_ID_KEVIN_DURANT}"
        )
        self.iguodala = self.db.query(
            f"select * from boxscores where game_id='{GAME_ID}' and player_id={PLAYER_ID_ANDRE_IGUODALA}"
        )

    def test_effective_fg_percentage(self):
        df = pd.DataFrame()
        df["efg%"] = self.draymond.apply(advanced.effective_fg_percentage, axis=1)
        self.assertAlmostEqual(df["efg%"][0], 0.550, delta=0.01)

    def test_true_shooting_percentage(self):
        df = pd.DataFrame()
        df["ts%"] = self.draymond.apply(advanced.true_shooting_percentage, axis=1)
        self.assertAlmostEqual(df["ts%"][0], 0.593, delta=0.01)

    def test_game_score(self):
        df = pd.DataFrame()
        df["gs"] = self.draymond.apply(advanced.game_score, axis=1)
        self.assertAlmostEqual(df["gs"][0], 19.6, delta=0.01)

    def test_three_point_attempt_rate(self):
        df = pd.DataFrame()
        df["3par"] = self.draymond.apply(advanced.three_point_attempt_rate, axis=1)
        self.assertAlmostEqual(df["3par"][0], 0.3, delta=0.01)

    def test_free_throw_attempt_rate(self):
        df = pd.DataFrame()
        df["ftr"] = self.draymond.apply(advanced.free_throw_attempt_rate, axis=1)
        self.assertAlmostEqual(df["ftr"][0], 0.6, delta=0.01)

    def test_offensive_rebound_percentage(self):
        orb = self.draymond.apply(
            advanced.offensive_rebound_percentage,
            team=self.warriors,
            opponent=self.lakers,
            axis=1,
        )
        self.assertAlmostEqual(orb[0], 14.18, delta=0.01)

    def test_defensive_rebound_percentage(self):
        drb = self.draymond.apply(
            advanced.defensive_rebound_percentage,
            team=self.warriors,
            opponent=self.lakers,
            axis=1,
        )
        self.assertAlmostEqual(drb[0], 22.00, delta=0.01)

    def test_total_rebound_percentage(self):
        trb = self.draymond.apply(
            advanced.total_rebound_percentage,
            team=self.warriors,
            opponent=self.lakers,
            axis=1,
        )
        self.assertAlmostEqual(trb[0], 18.32, delta=0.01)

    def test_assist_percentage(self):
        ass = self.draymond.apply(
            advanced.assist_percentage, team=self.warriors, axis=1
        )
        self.assertAlmostEqual(ass[0], 31.04, delta=0.01)

    def test_steal_percentage(self):
        stl = self.draymond.apply(
            advanced.steal_percentage, team=self.warriors, opponent=self.lakers, axis=1
        )
        self.assertAlmostEqual(stl[0], 4.9, delta=0.01)

    def test_block_percentage(self):
        blk = self.durant.apply(
            advanced.block_percentage, team=self.warriors, opponent=self.lakers, axis=1
        )
        self.assertAlmostEqual(blk[0], 1.76, delta=0.01)

    def test_turnover_percentage(self):
        to = self.draymond.apply(advanced.turnover_percentage, axis=1)
        self.assertAlmostEqual(to[0], 24.03, delta=0.01)

    def test_usage_percentage(self):
        usage = self.draymond.apply(
            advanced.usage_percentage, team=self.warriors, axis=1
        )
        self.assertAlmostEqual(usage[0], 17.93, delta=0.01)

    def test_offensive_rating(self):
        ortg = self.draymond.apply(
            advanced.offensive_rating, team=self.warriors, opponent=self.lakers, axis=1
        )
        self.assertAlmostEqual(ortg[0], 119.94, delta=0.01)

    def test_offensive_rating_no_ft_percentage(self):
        ortg = self.iguodala.apply(
            advanced.offensive_rating, team=self.warriors, opponent=self.lakers, axis=1
        )
        self.assertAlmostEqual(ortg[0], 113.77, delta=0.01)

    def test_defensive_rating(self):
        drtg = self.draymond.apply(
            advanced.defensive_rating, team=self.warriors, opponent=self.lakers, axis=1
        )
        self.assertAlmostEqual(drtg[0], 102.43, delta=0.01)

    def test_team_offensive_rating(self):
        ortg = advanced.team_offensive_rating(self.warriors, self.lakers)
        self.assertAlmostEqual(ortg, 112.61, delta=0.01)

    def test_team_defensive_rating(self):
        drtg = advanced.team_defensive_rating(self.warriors, self.lakers)
        self.assertAlmostEqual(drtg, 109.06, delta=0.01)


if __name__ == "__main__":
    unittest.main()
