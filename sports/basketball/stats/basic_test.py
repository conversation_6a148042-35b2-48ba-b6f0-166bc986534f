import unittest
import os

import sys

print(sys.version)

import sports.basketball.stats.basic as basic
from sports.basketball.scrape.nba.boxscore import Boxscore
from sports.basketball.database.postgress_test_case import PostgressTestCase
import pandas as pd

# Golden State v Lakers, 2017-11-29
GAME_ID = "0021700312"
SEASON = 2017
PLAYER_ID_DRAYMOND_GREEN = "203110"
PLAYER_ID_KEVIN_DURANT = "201142"
PLAYER_ID_COREY_BREWER = "201147"
TEAM_ID_WARRIORS = "1610612744"
TEAM_ID_LAKERS = "1610612747"

TEST_BACKUP_PATH = os.path.dirname(__file__) + "/test_data/"


class TestBasic(PostgressTestCase, unittest.TestCase):
    def setUp(self):
        super(TestBasic, self).setUp()
        df = pd.DataFrame(
            {
                "game_id": [GAME_ID],
                "season": [SEASON],
                "home_team_id": [1610612747],
                "visitor_team_id": [1610612744],
                "league": [1],
            }
        )
        self.db.insert(df, table="games")
        Boxscore(
            game_id=GAME_ID,
            season=SEASON,
            backup_path=TEST_BACKUP_PATH,
            database=self.db,
            force_load_from_disk=True,
        )

        self.assertEqual(self.db.count("select count(*) from boxscores;"), 26)

        self.warriors = self.db.query(
            f"select * from boxscores where game_id='{GAME_ID}' and team_id={TEAM_ID_WARRIORS}"
        ).sum()
        self.lakers = self.db.query(
            f"select * from boxscores where game_id='{GAME_ID}' and team_id={TEAM_ID_LAKERS}"
        ).sum()
        self.draymond = self.db.query(
            f"select * from boxscores where game_id='{GAME_ID}' and player_id={PLAYER_ID_DRAYMOND_GREEN}"
        )
        self.durant = self.db.query(
            f"select * from boxscores where game_id='{GAME_ID}' and player_id={PLAYER_ID_KEVIN_DURANT}"
        )
        self.brewer = self.db.query(
            f"select * from boxscores where game_id='{GAME_ID}' and player_id={PLAYER_ID_COREY_BREWER}"
        )

    def test_fg_percentage(self):
        df = pd.DataFrame()
        df["fg%"] = self.draymond.apply(basic.fg_percentage, axis=1)
        self.assertAlmostEqual(df["fg%"][0], 0.5, delta=0.01)

    def test_ft_percentage(self):
        df = pd.DataFrame()
        df["ft%"] = self.draymond.apply(basic.ft_percentage, axis=1)
        self.assertAlmostEqual(df["ft%"][0], 0.67, delta=0.01)

    def test_fg3_percentage(self):
        df = pd.DataFrame()
        df["3pt%"] = self.draymond.apply(basic.fg3_percentage, axis=1)
        self.assertAlmostEqual(df["3pt%"][0], 0.33, delta=0.01)

    def test_minutes_played(self):
        df = pd.DataFrame()
        df["mp"] = self.draymond.apply(basic.minutes_played, axis=1)
        self.assertAlmostEqual(df["mp"][0], 38.33, delta=0.01)

    def test_minutes_str(self):
        df = pd.DataFrame()
        df["mp"] = self.draymond.apply(basic.minutes_str, axis=1)
        self.assertEqual(df["mp"][0], "38:20")

    def test_minutes_str_padding_on_seconds(self):
        df = pd.DataFrame()
        df["mp"] = self.brewer.apply(basic.minutes_str, axis=1)
        self.assertEqual(df["mp"][0], "12:04")

    def test_fg_str(self):
        df = pd.DataFrame()
        df["fg"] = self.durant.apply(basic.fg_str, axis=1)
        self.assertEqual(df["fg"][0], "12-25")
        self.assertEqual(self.durant["fgm"][0], 12)
        self.assertEqual(self.durant["fga"][0], 25)

    def test_fg3_str(self):
        df = pd.DataFrame()
        df["fg3"] = self.durant.apply(basic.fg3_str, axis=1)
        self.assertEqual(df["fg3"][0], "3-7")
        self.assertEqual(self.durant["fg3m"][0], 3)
        self.assertEqual(self.durant["fg3a"][0], 7)

    def test_ft_str(self):
        df = pd.DataFrame()
        df["ft"] = self.durant.apply(basic.ft_str, axis=1)
        self.assertEqual(df["ft"][0], "2-4")
        self.assertEqual(self.durant["ftm"][0], 2)
        self.assertEqual(self.durant["fta"][0], 4)


if __name__ == "__main__":
    unittest.main()
