import numpy as np
import pandas as pd

from sports.basketball.stats.basic import minutes_played, ft_percentage


# These calculations are mostly based on the formulas as written on
# basketball reference.
# https://www.basketball-reference.com/about/glossary.html


def effective_fg_percentage(row: pd.core.series.Series):
    try:
        return (row.fgm + 0.5 * row.fg3m) / row.fga
    except ZeroDivisionError:
        return np.nan


def true_shooting_attempts(row: pd.core.series.Series):
    try:
        return row.fga + 0.44 * row.fta
    except ZeroDivisionError:
        return np.nan


def true_shooting_percentage(row: pd.core.series.Series):
    try:
        tsa = true_shooting_attempts(row)
        return row.pts / (2.0 * tsa)
    except ZeroDivisionError:
        return np.nan

def game_score(row):
    return row.pts + 0.4 * row.fgm - 0.7 * row.fga - 0.4 * (row.fta - row.ftm) \
           + 0.7 * row.oreb + 0.3 * row.dreb + row.stl + 0.7 * row.ast + 0.7 \
           * row.blk - 0.4 * row.pf - row.to


def three_point_attempt_rate(row: pd.core.series.Series):
    try:
        return row.fg3a / row.fga
    except ZeroDivisionError:
        return np.nan


def free_throw_attempt_rate(row: pd.core.series.Series):
    try:
        return row.fta / row.fga
    except ZeroDivisionError:
        return np.nan


def offensive_rebound_percentage(row: pd.core.series.Series, team: pd.core.series.Series, opponent: pd.core.series.Series):
    try:
        return 100.0 * \
               (row.oreb * (minutes_played(team) / 5.0)) \
               / (minutes_played(row) * (team.oreb + opponent.dreb))
    except ZeroDivisionError:
        return np.nan


def defensive_rebound_percentage(row: pd.core.series.Series, team: pd.core.series.Series, opponent: pd.core.series.Series):
    try:
        return 100.0 * \
               (row.dreb * (minutes_played(team) / 5.0)) \
               / (minutes_played(row) * (team.dreb + opponent.oreb))
    except ZeroDivisionError:
        return np.nan


def total_rebound_percentage(row: pd.core.series.Series, team: pd.core.series.Series, opponent: pd.core.series.Series):
    try:
        return 100.0 * \
               (row.reb * (minutes_played(team) / 5.0)) \
               / (minutes_played(row) * (team.reb + opponent.reb))
    except ZeroDivisionError:
        return np.nan


def assist_percentage(row: pd.core.series.Series, team: pd.core.series.Series):
    try:
        return 100.0 * \
               row.ast / (((minutes_played(row) / (minutes_played(team) / 5.0)) * team.fgm) - row.fgm)
    except ZeroDivisionError:
        return np.nan


def steal_percentage(row: pd.core.series.Series, team: pd.core.series.Series, opponent: pd.core.series.Series):
    try:
        return 100.0 * \
               (row.stl * (minutes_played(team) / 5.0)) \
               / (minutes_played(row) * possessions(opponent, team))
    except ZeroDivisionError:
        return np.nan


def block_percentage(row: pd.core.series.Series, team: pd.core.series.Series, opponent: pd.core.series.Series):
    try:
        return 100.0 * \
               (row.blk * (minutes_played(team) / 5.0)) / \
               (minutes_played(row) * (opponent.fga - opponent.fg3a))
    except ZeroDivisionError:
        return np.nan


def turnover_percentage(row: pd.core.series.Series):
    try:
        return 100.0 * row.to / (row.fga + 0.44 * row.fta + row.to)
    except ZeroDivisionError:
        return np.nan


def usage_percentage(row: pd.core.series.Series, team: pd.core.series.Series):
    try:
        return 100 * \
               ((row.fga + 0.44 * row.fta + row.to) * (minutes_played(team) / 5.0)) / \
               (minutes_played(row) * (team.fga + 0.44 * team.fta + team.to))
    except ZeroDivisionError:
        return np.nan


def possessions(team: pd.core.series.Series, opponent: pd.core.series.Series):
    try:
        return 0.5 * ((team.fga + 0.4 * team.fta - 1.07 * (team.oreb / (team.oreb + opponent.dreb)) * (
                team.fga - team.fgm) + team.to) + (opponent.fga + 0.4 * opponent.fta - 1.07 * (
                opponent.oreb / (opponent.oreb + team.dreb)) * (opponent.fga - opponent.fgm) + opponent.to))
    except ZeroDivisionError:
        return np.nan


# I never realised how ridiculous offensive and defensive rating are until I
# actually coded it. I had read the paper but now it seems pretty extravagant.
def offensive_rating(row: pd.core.series.Series, team: pd.core.series.Series, opponent: pd.core.series.Series):
    try:
        min_played = minutes_played(row)
        team_min_played = minutes_played(team)
        q_ast = ((min_played / (team_min_played / 5)) * (1.14 * ((team.ast - row.ast) / team.fgm))) + (
                (((team.ast / team_min_played) * min_played * 5 - row.ast) / (
                            (team.fgm / team_min_played) * min_played * 5 - row.fgm)) * (
                        1 - (min_played / (team_min_played / 5))))
        fg_part = row.fgm * (1.0 - 0.5 * ((row.pts - row.ftm) / (2.0 * row.fga)) * q_ast)
        ast_part = 0.5 * (((team.pts - team.ftm) - (row.pts - row.ftm)) / (2.0 * (team.fga - row.fga))) * row.ast
        ft_per = ft_percentage(row)
        ft_part = (1.0 - pow(1.0 - ft_per, 2.0)) * 0.4 * row.fta
        team_scoring_poss = team.fgm + (1.0 - pow(1.0 - (team.ftm / team.fta), 2.0)) * team.fta * 0.4
        team_orb_per = team.oreb / (team.oreb + (opponent.reb - opponent.oreb))
        team_play_per = team_scoring_poss / (team.fga + team.fta * 0.4 + team.to)
        team_orb_weight = ((1.0 - team_orb_per) * team_play_per) / (
                (1.0 - team_orb_per) * team_play_per + team_orb_per * (1 - team_play_per))
        orb_part = row.oreb * team_orb_weight * team_play_per
        score_poss = (fg_part + ast_part + ft_part) * \
                     (1 - (team.oreb / team_scoring_poss) * team_orb_weight * team_play_per) + orb_part
        fg_x_poss = (row.fga - row.fgm) * (1.0 - 1.07 * team_orb_per)
        ft_x_poss = (pow(1.0 - ft_per, 2.0)) * 0.4 * row.fta
        total_poss = score_poss + fg_x_poss + ft_x_poss + row.to

        pprod_fg_part = 2.0 * (row.fgm + 0.5 * row.fg3m) * (1.0 - 0.5 * ((row.pts - row.ftm) / (2.0 * row.fga)) * q_ast)
        pprod_ast_part = 2.0 * ((team.fgm - row.fgm + 0.5 * (team.fg3m - row.fg3m)) / (team.fgm - row.fgm)) * 0.5 * (
                ((team.pts - team.ftm) - (row.pts - row.ftm)) / (2.0 * (team.fga - row.fga))) * row.ast
        pprod_orb_part = row.oreb * team_orb_weight * team_play_per * (
                team.pts / (team.fgm + (1.0 - pow(1.0 - (team.ftm / team.fta), 2.0)) * 0.4 * team.fta))
        pprod = (pprod_fg_part + pprod_ast_part + row.ftm) * (
                1.0 - (team.oreb / team_scoring_poss) * team_orb_weight * team_play_per) + pprod_orb_part

        return 100.0 * (pprod / total_poss)
    except ZeroDivisionError:
        return np.nan


def defensive_rating(row: pd.core.series.Series, team: pd.core.series.Series, opponent: pd.core.series.Series):
    try:
        min_played = minutes_played(row)
        team_min_played = minutes_played(team)
        opponent_min_played = minutes_played(opponent)
        team_possessions = possessions(team, opponent)

        dor_per = opponent.oreb / (opponent.oreb + team.dreb)
        dfg_per = opponent.fgm / opponent.fga
        fmwt = (dfg_per * (1.0 - dor_per)) / (dfg_per * (1.0 - dor_per) + (1.0 - dfg_per) * dor_per)
        stops_1 = row.stl + row.blk * fmwt * (1.0 - 1.07 * dor_per) + row.dreb * (1.0 - fmwt)
        stops_2 = (((opponent.fga - opponent.fgm - team.blk) / team_min_played) * fmwt * (1.0 - 1.07 * dor_per) + (
                (opponent.to - team.stl) / team_min_played)) * min_played + (row.pf / team.pf) * 0.4 * opponent.fta * pow(
            1.0 - (opponent.ftm / opponent.fta), 2.0)
        stops = stops_1 + stops_2

        stop_per = (stops * opponent_min_played) / (team_possessions * min_played)
        team_defensive_rating = 100.0 * (opponent.pts / team_possessions)
        d_pts_per_sc_poss = opponent.pts / (
                opponent.fgm + (1.0 - pow(1.0 - (opponent.ftm / opponent.fta), 2.0)) * opponent.fta * 0.4)
        return team_defensive_rating + 0.2 * (100.0 * d_pts_per_sc_poss * (1.0 - stop_per) - team_defensive_rating)
    except ZeroDivisionError:
        return np.nan


def team_offensive_rating(team: pd.core.series.Series, opponent: pd.core.series.Series):
    try:
        return 100.0 * team.pts / possessions(team, opponent)
    except ZeroDivisionError:
        return np.nan


def team_defensive_rating(team: pd.core.series.Series, opponent: pd.core.series.Series):
    try:
        return 100.0 * opponent.pts / possessions(team, opponent)
    except ZeroDivisionError:
        return np.nan
