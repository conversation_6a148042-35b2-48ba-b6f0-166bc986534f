import plotly.graph_objs as go
import pandas as pd
import plotly.figure_factory as ff
from plotly.offline import download_plotlyjs, init_notebook_mode, plot as pplot, iplot
from sports.basketball.stats.basic import minutes_str
import numpy as np
import scipy.stats as st


def construct_court():
    court_shapes = []

    outer_lines_shape = dict(
        type='rect',
        xref='x',
        yref='y',
        x0='-250',
        y0='-47.5',
        x1='250',
        y1='422.5',
        line=dict(
            color='rgba(169, 169, 169, 1)',
            width=2
        ),
        fillcolor= 'rgba(240, 240, 240, 0.5)',
    )

    court_shapes.append(outer_lines_shape)

    hoop_shape = dict(
        type='circle',
        xref='x',
        yref='y',
        x0='7.5',
        y0='7.5',
        x1='-7.5',
        y1='-7.5',
        line=dict(
            color='rgba(169, 169, 169, 1)',
            width=2
        )
    )

    court_shapes.append(hoop_shape)

    backboard_shape = dict(
        type='rect',
        xref='x',
        yref='y',
        x0='-30',
        y0='-7.5',
        x1='30',
        y1='-6.5',
        line=dict(
            color='rgba(169, 169, 169, 1)',
            width=2
        ),
        fillcolor='rgba(169, 169, 169, 1)'
    )

    court_shapes.append(backboard_shape)

    outer_three_sec_shape = dict(
        type='rect',
        xref='x',
        yref='y',
        x0='-80',
        y0='-47.5',
        x1='80',
        y1='143.5',
        line=dict(
            color='rgba(169, 169, 169, 1)',
            width=2
        )
    )

    court_shapes.append(outer_three_sec_shape)

    outer_three_sec_shape = dict(
        type='rect',
        xref='x',
        yref='y',
        x0='-80',
        y0='-47.5',
        x1='80',
        y1='143.5',
        line=dict(
            color='rgba(169, 169, 169, 1)',
            width=2
        )
    )

    court_shapes.append(outer_three_sec_shape)

    inner_three_sec_shape = dict(
        type='rect',
        xref='x',
        yref='y',
        x0='-60',
        y0='-47.5',
        x1='60',
        y1='143.5',
        line=dict(
            color='rgba(169, 169, 169, 1)',
            width=2
        )
    )

    court_shapes.append(inner_three_sec_shape)

    left_line_shape = dict(
        type='line',
        xref='x',
        yref='y',
        x0='-220',
        y0='-47.5',
        x1='-220',
        y1='92.5',
        line=dict(
            color='rgba(169, 169, 169, 1)',
            width=2
        )
    )

    court_shapes.append(left_line_shape)

    right_line_shape = dict(
        type='line',
        xref='x',
        yref='y',
        x0='220',
        y0='-47.5',
        x1='220',
        y1='92.5',
        line=dict(
            color='rgba(169, 169, 169, 1)',
            width=2
        )
    )

    court_shapes.append(right_line_shape)

    three_point_arc_shape = dict(
        type='path',
        xref='x',
        yref='y',
        path='M -220 92.5 C -130 288, 130 288, 220 92.5',
        line=dict(
            color='rgba(169, 169, 169, 1)',
            width=2
        )
    )

    court_shapes.append(three_point_arc_shape)

    center_lower_circle_shape = dict(
        type='path',
        xref='x',
        yref='y',
        path='M -60 422.5 C -52 343, 52 343, 60 422.5',
        line=dict(
            color='rgba(169, 169, 169, 1)',
            width=2,
        )
    )
    court_shapes.append(center_lower_circle_shape)

    free_throw_top_circle_shape = dict(
        type='path',
        xref='x',
        yref='y',
        path='M -60 140 C -52 219.5, 52 219.5, 60 140',
        line=dict(
            color='rgba(169, 169, 169, 1)',
            width=2,
        )
    )
    court_shapes.append(free_throw_top_circle_shape)

    free_throw_bottom_circle_shape = dict(
        type='path',
        xref='x',
        yref='y',
        path='M -60 140 C -52 60.5, 52 60.5, 60 140',
        line=dict(
            color='rgba(169, 169, 169, 1)',
            width=2,
            dash='dash'
        )
    )
    court_shapes.append(free_throw_bottom_circle_shape)

    res_arc_shape = dict(
        type='path',
        xref='x',
        yref='y',
        path='M -40 0 C -35 54, 35 54, 40 0',
        line=dict(
            color='rgba(169, 169, 169, 1)',
            width=2,
        )
    )
    court_shapes.append(res_arc_shape)
    return court_shapes

def hover_string(row: pd.core.series.Series):
    return "{}<br>Q{} {}<br>{}ft {}".format(row.player_name, row.period, row.minutes, row.shot_distance, row.action_type)

def construct_scatter_plots(shot_dataframe):
    makes_df = shot_dataframe[shot_dataframe.shot_made_flag==1]
    makes_df['minutes'] = makes_df.apply(minutes_str, axis=1)
    makes_df['hover'] = makes_df.apply(hover_string, axis=1)

    misses_df = shot_dataframe[shot_dataframe.shot_made_flag==0]
    misses_df['minutes'] = misses_df.apply(minutes_str, axis=1)
    misses_df['hover'] = misses_df.apply(hover_string, axis=1)

    makes = go.Scatter(
        x = -makes_df.loc_x,
        y = makes_df.loc_y,
        mode = 'markers',
        marker = dict(
            size = 7.5,
            color = 'rgba(0, 200, 0, .5)',
            line = dict(
                color = 'rgb(40, 40, 40)',
                width = 1
            )
        ),
        text= makes_df.hover,
        hoverinfo='text',
        name = 'Made',
    )

    misses = go.Scatter(
        x = -misses_df.loc_x,
        y = misses_df.loc_y,
        mode = 'markers',
        marker = dict(
            size = 7.5,
            color = 'rgba(200, 0, 0, .5)',
            line = dict(
                color = 'rgb(40, 40, 40)',
                width = 1
            )
        ),
        text= misses_df.hover,
        hoverinfo='text',
        name = 'Missed',
    )

    return makes, misses

def kde(vals1, vals2, a,b, c,d, N ):
    x=np.linspace(a,b,N)
    y=np.linspace(c,d,N)
    X,Y=np.meshgrid(x,y)
    positions = np.vstack([X.ravel(), Y.ravel()])
    values = np.vstack([vals1, vals2])
    kernel = st.gaussian_kde(values)
    Z = np.reshape(kernel(positions).T, X.shape)
    return [x, y, Z]

def construct_contour(shot_dataframe, levels):
    x, y, Z = kde(-shot_dataframe.loc_x, shot_dataframe.loc_y, -250, 250, -50, 423.5, levels)
    return go.Heatmap(
        z=Z,
        x=x,
        y=y,
        name = 'Shot Distribution',
        colorscale='Greys',
        reversescale=True,
        opacity=1.0,
        showscale=False,
        hoverinfo = 'skip',
        zsmooth = 'best',
    )

def construct_annotations(title:str = None, subtitle:str = None):
    annotations = [
       dict(
            x=150,
            y=-40,
            xref='x',
            yref='y',
            text='<b>@michaeljquinlan</b>',
            showarrow=False,
            font=dict(
                size=12,
                color='rgba(169, 169, 169, 0.4)',
            ),
        ),
        dict(
            x=-150,
            y=-40,
            xref='x',
            yref='y',
            text='Data source: nba.com',
            showarrow=False,
            xanchor='center',
            font=dict(
                size=10,
                color='rgba(169, 169, 169, 0.4)',
            ),
        )
    ]
    if title:
        annotations.append(
            dict(
                x=-253,
                y=432,
                xref='x',
                yref='y',
                text='<b>{}</b>'.format(title),
                showarrow=False,
                xanchor='left',
                font=dict(
                    size=16,
                    color='rgba(169, 169, 169, 1)',
                ),
            )
        )
        if subtitle:
            annotations.append(
                dict(
                    x=253,
                    y=432,
                    xref='x',
                    yref='y',
                    text='{}'.format(subtitle),
                    showarrow=False,
                    xanchor='right',
                    font=dict(
                        size=14,
                        color='rgba(169, 169, 169, 1)',
                    ),
                )
            )
    return annotations

def construct_figure(shot_dataframe, title=None, subtitle=None, output_file=None, kde_levels=100):
    shot_dataframe = shot_dataframe[shot_dataframe.loc_y < 423]
    makes, misses = construct_scatter_plots(shot_dataframe)
    contours = construct_contour(shot_dataframe, levels=kde_levels)
    data = [misses, makes, contours]
    court = construct_court()

    layout = go.Layout(
        autosize=True,
        hovermode = 'closest',
        xaxis=dict(
            showgrid=False,
            showline=False,
            zeroline=False,
            showticklabels=False,
        ),
        yaxis=dict(
            showgrid=False,
            scaleanchor='x',
            scaleratio=1.0,
            showline=False,
            zeroline=False,
            showticklabels=False,
        ),
        margin=go.layout.Margin(
            l=0, #left margin
            r=0, #right margin
            b=0, #bottom margin
            t=0, #top margin
            pad=0,
        ),
        annotations=construct_annotations(title, subtitle),
        showlegend=False,
        shapes=court,
    )

    return go.Figure(data=data, layout=layout)


def plot(shot_dataframe, title:str = None, subtitle:str = None, output_file:str = None, output_div = False, kde_levels:int =50):
    fig = construct_figure(shot_dataframe, title, subtitle, kde_levels)
    iplot(fig, config=dict(displaylogo=False, displayModeBar=False))
    if output_file:
        fig.write_image(output_file)
    if output_div:
        print(pplot(fig, include_plotlyjs=False, config=dict(displaylogo=False, displayModeBar=False), output_type='div'))

# Keeping old non-ploty version commented out for time
#
# import matplotlib.pyplot as plt
# from matplotlib.patches import Circle, Rectangle, Arc
# import matplotlib.style as style
# import seaborn as sns
#
# def draw_court(ax=None, color='darkgray', lw=2, outer_lines=True):
#     # If an axes object isn't provided to plot onto, just get current one
#     if ax is None:
#         ax = plt.gca()
#
#     # Hide grid lines
#     ax.grid(False)
#     ax.get_xaxis().set_visible(False)
#     ax.get_yaxis().set_visible(False)
#
#     # Create the basketball hoop
#     # Diameter of a hoop is 18" so it has a radius of 9", which is a value
#     # 7.5 in our coordinate system
#     hoop = Circle((0, 0), radius=7.5, linewidth=lw, color=color, fill=False)
#
#     # Create backboard
#     backboard = Rectangle((-30, -7.5), 60, -1, linewidth=lw, color=color)
#
#     # The paint
#     # Create the outer box 0f the paint, width=16ft, height=19ft
#     outer_box = Rectangle((-80, -47.5), 160, 190, linewidth=lw, color=color,
#                           fill=False)
#     # Create the inner box of the paint, widt=12ft, height=19ft
#     inner_box = Rectangle((-60, -47.5), 120, 190, linewidth=lw, color=color,
#                           fill=False)
#
#     # Create free throw top arc
#     top_free_throw = Arc((0, 142.5), 120, 120, theta1=0, theta2=180,
#                          linewidth=lw, color=color, fill=False)
#     # Create free throw bottom arc
#     bottom_free_throw = Arc((0, 142.5), 120, 120, theta1=180, theta2=0,
#                             linewidth=lw, color=color, linestyle='dashed')
#     # Restricted Zone, it is an arc with 4ft radius from center of the hoop
#     restricted = Arc((0, 0), 80, 80, theta1=0, theta2=180, linewidth=lw,
#                      color=color)
#
#     # Three point line
#     # Create the side 3pt lines, they are 14ft long before they begin to arc
#     corner_three_a = Rectangle((-220, -47.5), 0, 140, linewidth=lw,
#                                color=color)
#     corner_three_b = Rectangle((220, -47.5), 0, 140, linewidth=lw, color=color)
#     # 3pt arc - center of arc will be the hoop, arc is 23'9" away from hoop
#     # I just played around with the theta values until they lined up with the
#     # threes
#     three_arc = Arc((0, 0), 475, 475, theta1=22, theta2=158, linewidth=lw,
#                     color=color)
#
#     # Center Court
#     center_outer_arc = Arc((0, 422.5), 120, 120, theta1=180, theta2=0,
#                            linewidth=lw, color=color)
#     center_inner_arc = Arc((0, 422.5), 40, 40, theta1=180, theta2=0,
#                            linewidth=lw, color=color)
#
#     # List of the court elements to be plotted onto the axes
#     court_elements = [hoop, backboard, outer_box, inner_box, top_free_throw,
#                       bottom_free_throw, restricted, corner_three_a,
#                       corner_three_b, three_arc, center_outer_arc]
#
#     if outer_lines:
#         # Draw the half court line, baseline and side out bound lines
#         outer_lines = Rectangle((-250, -47.5), 500, 470, linewidth=lw,
#                                 color=color, fill=False)
#         court_elements.append(outer_lines)
#
#     # Add the court elements onto the axes
#     for element in court_elements:
#         ax.add_patch(element)
#
#     return ax
#
#
# def draw_shot_scatter(ax, shot_df, format='Normal'):
#     assert format in ['Normal', 'Minimal']
#     red_green = [(0.988235294117, 0.5529411764705, 0.384313725490), (0.4, 0.760784313725, 0.647058823529)]
#     marker_sizes = {'Normal': 80, 'Minimal':20}
#     marker_alphas = {'Normal': 0.75, 'Minimal':0.1}
#     sns.scatterplot(-shot_df.loc_x, shot_df.loc_y, hue=shot_df.shot_made_flag, palette=sns.color_palette(red_green),
#                     s=marker_sizes[format], ax=ax, legend=False, alpha=marker_alphas[format])
#
#
# def plot(shot_dataframe,title=None, subtitle=None, output_file=None, color_map = "Purples_d", scatter_format='Normal', kde_levels=100):
#     plt.figure(figsize=(12, 11))
#     ax = sns.kdeplot(-shot_dataframe.loc_x, shot_dataframe.loc_y, n_levels=kde_levels, shade=True, cmap=color_map)
#     if color_map.endswith("_d"):
#         ax.set_facecolor((53/255.0, 52/255.0, 54/255.0))
#     ax.set(aspect="equal")
#     draw_court(ax)
#     if scatter_format is not None:
#         draw_shot_scatter(ax, shot_dataframe, format=scatter_format)
#     plt.xlim(-280, 280)
#     plt.ylim(-80, 450)
#     plt.text(250.0, -71.0, '@michaeljquinlan', fontsize=20, color='gray', ha='right', va='bottom', alpha=0.5)
#     plt.text(250.0, -80.0, 'Source: nba.com', fontsize=10, color='gray', ha='right', va='bottom', alpha=0.5)
#     if title:
#         plt.text(-250.0, 430.0, title, fontsize=20, color='white', alpha=0.5)
#     if subtitle:
#         plt.text(250.0, 430.0, subtitle, fontsize=12, color='white', ha='right', alpha=0.5)
#     if output_file:
#         plt.savefig(output_file, bbox_inches='tight', pad_inches=0)
#     plt.show()
