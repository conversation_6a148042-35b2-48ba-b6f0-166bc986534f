from sports.basketball.database.dbtransformer import DbTransformer
from sports.basketball.database.players_boxscores_calcs import PlayersBoxscoresCalcs


class PlayerBoxscoreCalcs(DbTransformer):
    def __init__(self, df):
        self.dataframe = df
        DbTransformer.__init__(self)

    def orm(self):
        return PlayersBoxscoresCalcs()

    def transform(self):
        df = self.dataframe
        return df[['game_id', 'player_id', 'offensive_rating', 'defensive_rating', 'efg_percentage', 'ts_percentage',
                   'fg3_attempt_rate', 'ft_attempt_rate', 'oreb_percentage', 'dreb_percentage', 'reb_percentage',
                   'ast_percentage', 'stl_percentage', 'blk_percentage', 'to_percentage', 'usage_percentage']]
