import math


class Calculator(object):

    def __init__(self, k: float = 20, home_court: float = 100):
        self._k = k
        self._home_court = home_court

    def calculate(self, elo1: float, elo2: float, score1: float, score2: float):
        elo1 = elo1 + self._home_court
        ea1 = 1.0 / (1 + math.pow(10, (elo2 - elo1) / 400.0))
        ea2 = 1.0 / (1 + math.pow(10, (elo1 - elo2) / 400.0))
        if score1 > score2:
            mov = Calculator.__margin_of_victory_multiplier(elo1 - elo2, score1 - score2)
            return elo1 - self._home_court + self._k * mov * (1 - ea1), elo2 + self._k * mov * (0 - ea2)
        else:
            mov = Calculator.__margin_of_victory_multiplier(elo2 - elo1, score2 - score1)
            return elo1 - self._home_court + self._k * mov * (0 - ea1), elo2 + self._k * mov * (1 - ea2)

    @staticmethod
    def __margin_of_victory_multiplier(elo_diff: float, score_diff: float):
        return math.pow((score_diff + 3.0), 0.8) / (7.5 + .006 * (elo_diff))

    @staticmethod
    def year_carry_over(elo):
        return (0.75 * elo) + (0.25 * 1505)
