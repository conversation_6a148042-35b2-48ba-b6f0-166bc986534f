package(default_visibility = ["//visibility:public"])

load("@pip//:requirements.bzl", "requirement")

py_library(
    name = "elo",
    deps = [
        ":calculator",
        "//sports/basketball/scrape/fivethirtyeight:constants",
    ],
)

py_library(
    name = "calculator",
    srcs = ["calculator.py"],
    deps = [
        "//sports/basketball/database",
        "//sports/basketball/scrape/fivethirtyeight:constants",
        requirement("pandas"),
    ],
)

py_test(
    name = "calculator_test",
    size = "small",
    srcs = [
        "calculator_test.py",
    ],
    deps = [
        "//sports/basketball/elo:calculator",
    ],
)
