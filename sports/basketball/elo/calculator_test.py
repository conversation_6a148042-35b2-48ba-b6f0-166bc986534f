import unittest

from sports.basketball.elo.calculator import Calculator


class TestLinearChampion(unittest.TestCase):
    def setUp(self):
        self.calculator = Calculator()

    def test_team1_win(self):
        elo1, elo2 = self.calculator.calculate(1300, 1300, 68, 66)
        self.assertAlmostEqual(elo1, 1303.220661338132, 5)
        self.assertAlmostEqual(elo2, 1296.779338661868, 5)

    def test_team2_win(self):
        elo1, elo2 = self.calculator.calculate(1300, 1300, 66, 68)
        self.assertAlmostEqual(elo1, 1293.2767232566812, 5)
        self.assertAlmostEqual(elo2, 1306.7232767433188, 5)


if __name__ == '__main__':
    unittest.main()
