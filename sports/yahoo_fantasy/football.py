from __future__ import annotations

from sports.yahoo_fantasy.sport import Sport
from sports.yahoo_fantasy.data_objects import Team, Matchup, MatchupTeam, StatCategory


class Football(Sport):
    def __init__(self, league_id: str = None):
        Sport.__init__(self, sport_code="nfl", league_id=league_id)

    def matchups(self, week: int = None) -> list[Matchup]:
        matchups = self._league.matchups(week=week)

        matchups_list = []
        for key, value in matchups["fantasy_content"]["league"][1]["scoreboard"]["0"]["matchups"].items():
            first_team = MatchupTeam(team=Team())
            second_team = MatchupTeam(team=Team())
            try:
                first_team.team.key = value["matchup"]["0"]["teams"]["0"]["team"][0][0]["team_key"]
                second_team.team.key = value["matchup"]["0"]["teams"]["1"]["team"][0][0]["team_key"]
            except:
                continue

            first_team.team.name = value["matchup"]["0"]["teams"]["0"]["team"][0][2]["name"]
            second_team.team.name = value["matchup"]["0"]["teams"]["1"]["team"][0][2]["name"]

            first_team_stats = value["matchup"]["0"]["teams"]["0"]["team"][1]
            second_team_stats = value["matchup"]["0"]["teams"]["1"]["team"][1]

            first_team.team_points = float(first_team_stats["team_points"]["total"])
            second_team.team_points = float(second_team_stats["team_points"]["total"])

            first_team.win_probability = float(first_team_stats["win_probability"])
            second_team.win_probability = float(second_team_stats["win_probability"])

            matchups_list.append(Matchup(first_team=first_team, second_team=second_team))

        return matchups_list
