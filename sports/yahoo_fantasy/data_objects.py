from __future__ import annotations

from dataclasses import dataclass
import pandas as pd
from dataclasses import asdict


@dataclass
class Team:
    key: str = None
    name: str = None


@dataclass
class MatchupTeam:
    team: Team
    team_points: float = 0
    win_probability: float = None
    stats: List[StatCategory] = None


@dataclass
class Matchup:
    first_team: MatchupTeam
    second_team: MatchupTeam


@dataclass
class StatCategory:
    value: float = 0
    name: str = None
    key: int = None


@dataclass
class Player:
    name: str
    key: int
    eligible_positions: list[str] = None
    percent_owned: int = None
    status: str = None


@dataclass
class DraftPick:
    pick: int
    round: int
    player: Player
    team: Team
    cost: int = None


def dataclasses_to_dataframe(func) -> callable:
    def wrapper(*args) -> pd.DataFrame:
        return pd.json_normalize(list(asdict(obj) for obj in func(*args)))

    return wrapper
