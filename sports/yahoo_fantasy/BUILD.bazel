package(default_visibility = ["//visibility:public"])

load("@pip//:requirements.bzl", "requirement")

filegroup(
    name = "token_file",
    srcs = ["token.txt"],
)

py_library(
    name = "yahoo_fantasy",
    deps = [
        ":basketball",
        ":football",
    ],
)

py_library(
    name = "yahoo_token",
    srcs = ["yahoo_token.py"],
    data = [
        "//sports/yahoo_fantasy:token_file",
    ],
    deps = [
        requirement("yahoo-fantasy-api"),
    ],
)

py_test(
    name = "yahoo_token_test",
    size = "small",
    srcs = [
        "yahoo_token_test.py",
    ],
    deps = [
        ":yahoo_token",
    ],
)

py_library(
    name = "data_objects",
    srcs = ["data_objects.py"],
    deps = [
        requirement("pandas"),
    ],
)

py_library(
    name = "sport",
    srcs = ["sport.py"],
    deps = [
        ":data_objects",
        ":yahoo_token",
        requirement("yahoo-fantasy-api"),
        requirement("pandas"),
    ],
)

py_library(
    name = "basketball",
    srcs = ["basketball.py"],
    deps = [
        ":data_objects",
        ":sport",
    ],
)

py_test(
    name = "basketball_test",
    size = "medium",
    srcs = [
        "basketball_test.py",
    ],
    deps = [
        ":basketball",
    ],
)

py_library(
    name = "football",
    srcs = ["football.py"],
    deps = [
        ":data_objects",
        ":sport",
    ],
)

py_test(
    name = "football_test",
    size = "medium",
    srcs = [
        "football_test.py",
    ],
    deps = [
        ":football",
    ],
)
