import unittest

import sports.yahoo_fantasy.basketball as yahoo_basketball
from sports.yahoo_fantasy.data_objects import (
    Matchup,
    MatchupTeam,
    StatCategory,
    Team,
    Draft<PERSON>ick,
    Player,
)


class TestBasketball(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls._basketball = yahoo_basketball.Basketball(league_id="410.l.22889")

    def test_league_name(self):
        self.assertEqual("World Expo v2", self._basketball.league_name)

    def test_num_teams(self):
        self.assertEqual(12, self._basketball.num_teams)

    def test_current_week(self):
        self.assertEqual(22, self._basketball.current_week)

    def test_playoff_start_week(self):
        self.assertEqual(20, self._basketball.playoff_start_week)

    def test_end_week(self):
        self.assertEqual(22, self._basketball.end_week)

    def test_is_finished(self):
        self.assertTrue(self._basketball.is_finished)

    def test_my_team_key(self):
        self.assertEqual("410.l.22889.t.7", self._basketball.my_team_key)

    def test_my_team(self):
        self.assertEqual(Team(key="410.l.22889.t.7", name="Explodo Heado"), self._basketball.my_team)

    def test_teams(self):
        expected_teams = [
            Team(key="410.l.22889.t.1", name="bigesser"),
            Team(key="410.l.22889.t.2", name="______Manny's Super Team"),
            Team(key="410.l.22889.t.3", name="allen's Unrivaled Team"),
            Team(key="410.l.22889.t.4", name="Andy's Team"),
            Team(key="410.l.22889.t.5", name="Charles's Team"),
            Team(key="410.l.22889.t.6", name="Double Sabonis"),
            Team(key="410.l.22889.t.7", name="Explodo Heado"),
            Team(key="410.l.22889.t.8", name="Flushing Noodles"),
            Team(key="410.l.22889.t.10", name="Ick Poo Dung"),
            Team(key="410.l.22889.t.11", name="Jake's Team"),
            Team(key="410.l.22889.t.13", name="Ryan's Pleasant Team"),
            Team(key="410.l.22889.t.15", name="simple jack"),
        ]
        actual_teams = self._basketball.teams()
        self.assertListEqual(expected_teams, actual_teams)
        self.assertEqual(len(actual_teams), self._basketball.num_teams)

    def test_teams_as_dataframe(self):
        self.assertEqual(len(self._basketball.teams_as_dataframe()), len(self._basketball.teams()))

    def test_matchups(self):
        expected_matchups = [
            Matchup(
                first_team=MatchupTeam(
                    team=Team(key="410.l.22889.t.1", name="bigesser"),
                    team_points=6,
                    stats=[
                        StatCategory(value=0.467, name="FG%", key=5),
                        StatCategory(value=95.0, name="FTM", key=7),
                        StatCategory(value=0.792, name="FT%", key=8),
                        StatCategory(value=60.0, name="3PTM", key=10),
                        StatCategory(value=561.0, name="PTS", key=12),
                        StatCategory(value=45.0, name="OREB", key=13),
                        StatCategory(value=165.0, name="DREB", key=14),
                        StatCategory(value=134.0, name="AST", key=16),
                        StatCategory(value=37.0, name="ST", key=17),
                        StatCategory(value=32.0, name="BLK", key=18),
                        StatCategory(value=79.0, name="TO", key=19),
                        StatCategory(value=75.0, name="PF", key=21),
                        StatCategory(value=1.0, name="TECH", key=23),
                    ],
                ),
                second_team=MatchupTeam(
                    team=Team(key="410.l.22889.t.3", name="allen's Unrivaled Team"),
                    team_points=6,
                    stats=[
                        StatCategory(value=0.461, name="FG%", key=5),
                        StatCategory(value=78.0, name="FTM", key=7),
                        StatCategory(value=0.772, name="FT%", key=8),
                        StatCategory(value=69.0, name="3PTM", key=10),
                        StatCategory(value=587.0, name="PTS", key=12),
                        StatCategory(value=46.0, name="OREB", key=13),
                        StatCategory(value=188.0, name="DREB", key=14),
                        StatCategory(value=99.0, name="AST", key=16),
                        StatCategory(value=41.0, name="ST", key=17),
                        StatCategory(value=27.0, name="BLK", key=18),
                        StatCategory(value=65.0, name="TO", key=19),
                        StatCategory(value=81.0, name="PF", key=21),
                        StatCategory(value=1.0, name="TECH", key=23),
                    ],
                ),
            ),
            Matchup(
                first_team=MatchupTeam(
                    team=Team(key="410.l.22889.t.2", name="______Manny's Super Team"),
                    team_points=5,
                    stats=[
                        StatCategory(value=0.468, name="FG%", key=5),
                        StatCategory(value=52.0, name="FTM", key=7),
                        StatCategory(value=0.765, name="FT%", key=8),
                        StatCategory(value=45.0, name="3PTM", key=10),
                        StatCategory(value=401.0, name="PTS", key=12),
                        StatCategory(value=43.0, name="OREB", key=13),
                        StatCategory(value=152.0, name="DREB", key=14),
                        StatCategory(value=71.0, name="AST", key=16),
                        StatCategory(value=28.0, name="ST", key=17),
                        StatCategory(value=18.0, name="BLK", key=18),
                        StatCategory(value=61.0, name="TO", key=19),
                        StatCategory(value=68.0, name="PF", key=21),
                        StatCategory(value=1.0, name="TECH", key=23),
                    ],
                ),
                second_team=MatchupTeam(
                    team=Team(key="410.l.22889.t.11", name="Jake's Team"),
                    team_points=8,
                    stats=[
                        StatCategory(value=0.445, name="FG%", key=5),
                        StatCategory(value=69.0, name="FTM", key=7),
                        StatCategory(value=0.821, name="FT%", key=8),
                        StatCategory(value=64.0, name="3PTM", key=10),
                        StatCategory(value=479.0, name="PTS", key=12),
                        StatCategory(value=39.0, name="OREB", key=13),
                        StatCategory(value=129.0, name="DREB", key=14),
                        StatCategory(value=81.0, name="AST", key=16),
                        StatCategory(value=35.0, name="ST", key=17),
                        StatCategory(value=19.0, name="BLK", key=18),
                        StatCategory(value=63.0, name="TO", key=19),
                        StatCategory(value=63.0, name="PF", key=21),
                        StatCategory(value=2.0, name="TECH", key=23),
                    ],
                ),
            ),
            Matchup(
                first_team=MatchupTeam(
                    team=Team(key="410.l.22889.t.4", name="Andy's Team"),
                    team_points=12,
                    stats=[
                        StatCategory(value=0.454, name="FG%", key=5),
                        StatCategory(value=79.0, name="FTM", key=7),
                        StatCategory(value=0.782, name="FT%", key=8),
                        StatCategory(value=55.0, name="3PTM", key=10),
                        StatCategory(value=512.0, name="PTS", key=12),
                        StatCategory(value=38.0, name="OREB", key=13),
                        StatCategory(value=150.0, name="DREB", key=14),
                        StatCategory(value=99.0, name="AST", key=16),
                        StatCategory(value=33.0, name="ST", key=17),
                        StatCategory(value=29.0, name="BLK", key=18),
                        StatCategory(value=43.0, name="TO", key=19),
                        StatCategory(value=60.0, name="PF", key=21),
                        StatCategory(value=3.0, name="TECH", key=23),
                    ],
                ),
                second_team=MatchupTeam(
                    team=Team(key="410.l.22889.t.10", name="Ick Poo Dung"),
                    team_points=1,
                    stats=[
                        StatCategory(value=0.442, name="FG%", key=5),
                        StatCategory(value=44.0, name="FTM", key=7),
                        StatCategory(value=0.721, name="FT%", key=8),
                        StatCategory(value=41.0, name="3PTM", key=10),
                        StatCategory(value=387.0, name="PTS", key=12),
                        StatCategory(value=27.0, name="OREB", key=13),
                        StatCategory(value=142.0, name="DREB", key=14),
                        StatCategory(value=87.0, name="AST", key=16),
                        StatCategory(value=26.0, name="ST", key=17),
                        StatCategory(value=24.0, name="BLK", key=18),
                        StatCategory(value=54.0, name="TO", key=19),
                        StatCategory(value=69.0, name="PF", key=21),
                        StatCategory(value=1.0, name="TECH", key=23),
                    ],
                ),
            ),
            Matchup(
                first_team=MatchupTeam(
                    team=Team(key="410.l.22889.t.5", name="Charles's Team"),
                    team_points=7,
                    stats=[
                        StatCategory(value=0.452, name="FG%", key=5),
                        StatCategory(value=60.0, name="FTM", key=7),
                        StatCategory(value=0.8, name="FT%", key=8),
                        StatCategory(value=62.0, name="3PTM", key=10),
                        StatCategory(value=518.0, name="PTS", key=12),
                        StatCategory(value=33.0, name="OREB", key=13),
                        StatCategory(value=140.0, name="DREB", key=14),
                        StatCategory(value=133.0, name="AST", key=16),
                        StatCategory(value=29.0, name="ST", key=17),
                        StatCategory(value=21.0, name="BLK", key=18),
                        StatCategory(value=62.0, name="TO", key=19),
                        StatCategory(value=62.0, name="PF", key=21),
                        StatCategory(value=0.0, name="TECH", key=23),
                    ],
                ),
                second_team=MatchupTeam(
                    team=Team(key="410.l.22889.t.8", name="Flushing Noodles"),
                    team_points=6,
                    stats=[
                        StatCategory(value=0.461, name="FG%", key=5),
                        StatCategory(value=88.0, name="FTM", key=7),
                        StatCategory(value=0.846, name="FT%", key=8),
                        StatCategory(value=48.0, name="3PTM", key=10),
                        StatCategory(value=498.0, name="PTS", key=12),
                        StatCategory(value=45.0, name="OREB", key=13),
                        StatCategory(value=132.0, name="DREB", key=14),
                        StatCategory(value=102.0, name="AST", key=16),
                        StatCategory(value=34.0, name="ST", key=17),
                        StatCategory(value=19.0, name="BLK", key=18),
                        StatCategory(value=48.0, name="TO", key=19),
                        StatCategory(value=68.0, name="PF", key=21),
                        StatCategory(value=2.0, name="TECH", key=23),
                    ],
                ),
            ),
            Matchup(
                first_team=MatchupTeam(
                    team=Team(key="410.l.22889.t.6", name="Double Sabonis"),
                    team_points=7,
                    stats=[
                        StatCategory(value=0.5, name="FG%", key=5),
                        StatCategory(value=100.0, name="FTM", key=7),
                        StatCategory(value=0.806, name="FT%", key=8),
                        StatCategory(value=35.0, name="3PTM", key=10),
                        StatCategory(value=543.0, name="PTS", key=12),
                        StatCategory(value=41.0, name="OREB", key=13),
                        StatCategory(value=162.0, name="DREB", key=14),
                        StatCategory(value=106.0, name="AST", key=16),
                        StatCategory(value=25.0, name="ST", key=17),
                        StatCategory(value=19.0, name="BLK", key=18),
                        StatCategory(value=59.0, name="TO", key=19),
                        StatCategory(value=76.0, name="PF", key=21),
                        StatCategory(value=2.0, name="TECH", key=23),
                    ],
                ),
                second_team=MatchupTeam(
                    team=Team(key="410.l.22889.t.7", name="Explodo Heado"),
                    team_points=6,
                    stats=[
                        StatCategory(value=0.413, name="FG%", key=5),
                        StatCategory(value=54.0, name="FTM", key=7),
                        StatCategory(value=0.783, name="FT%", key=8),
                        StatCategory(value=51.0, name="3PTM", key=10),
                        StatCategory(value=389.0, name="PTS", key=12),
                        StatCategory(value=59.0, name="OREB", key=13),
                        StatCategory(value=158.0, name="DREB", key=14),
                        StatCategory(value=125.0, name="AST", key=16),
                        StatCategory(value=35.0, name="ST", key=17),
                        StatCategory(value=28.0, name="BLK", key=18),
                        StatCategory(value=79.0, name="TO", key=19),
                        StatCategory(value=87.0, name="PF", key=21),
                        StatCategory(value=1.0, name="TECH", key=23),
                    ],
                ),
            ),
            Matchup(
                first_team=MatchupTeam(
                    team=Team(key="410.l.22889.t.13", name="Ryan's Pleasant Team"),
                    team_points=8,
                    stats=[
                        StatCategory(value=0.479, name="FG%", key=5),
                        StatCategory(value=45.0, name="FTM", key=7),
                        StatCategory(value=0.789, name="FT%", key=8),
                        StatCategory(value=47.0, name="3PTM", key=10),
                        StatCategory(value=390.0, name="PTS", key=12),
                        StatCategory(value=29.0, name="OREB", key=13),
                        StatCategory(value=125.0, name="DREB", key=14),
                        StatCategory(value=100.0, name="AST", key=16),
                        StatCategory(value=47.0, name="ST", key=17),
                        StatCategory(value=21.0, name="BLK", key=18),
                        StatCategory(value=58.0, name="TO", key=19),
                        StatCategory(value=65.0, name="PF", key=21),
                        StatCategory(value=1.0, name="TECH", key=23),
                    ],
                ),
                second_team=MatchupTeam(
                    team=Team(key="410.l.22889.t.15", name="simple jack"),
                    team_points=5,
                    stats=[
                        StatCategory(value=0.44, name="FG%", key=5),
                        StatCategory(value=65.0, name="FTM", key=7),
                        StatCategory(value=0.602, name="FT%", key=8),
                        StatCategory(value=43.0, name="3PTM", key=10),
                        StatCategory(value=498.0, name="PTS", key=12),
                        StatCategory(value=44.0, name="OREB", key=13),
                        StatCategory(value=165.0, name="DREB", key=14),
                        StatCategory(value=121.0, name="AST", key=16),
                        StatCategory(value=36.0, name="ST", key=17),
                        StatCategory(value=11.0, name="BLK", key=18),
                        StatCategory(value=85.0, name="TO", key=19),
                        StatCategory(value=76.0, name="PF", key=21),
                        StatCategory(value=2.0, name="TECH", key=23),
                    ],
                ),
            ),
        ]
        matchups = self._basketball.matchups(week=1)
        self.assertListEqual(matchups, expected_matchups)

    def test_my_matchup(self):
        expected_matchup = Matchup(
            first_team=MatchupTeam(
                team=Team(key="410.l.22889.t.6", name="Double Sabonis"),
                team_points=7,
                stats=[
                    StatCategory(value=0.5, name="FG%", key=5),
                    StatCategory(value=100.0, name="FTM", key=7),
                    StatCategory(value=0.806, name="FT%", key=8),
                    StatCategory(value=35.0, name="3PTM", key=10),
                    StatCategory(value=543.0, name="PTS", key=12),
                    StatCategory(value=41.0, name="OREB", key=13),
                    StatCategory(value=162.0, name="DREB", key=14),
                    StatCategory(value=106.0, name="AST", key=16),
                    StatCategory(value=25.0, name="ST", key=17),
                    StatCategory(value=19.0, name="BLK", key=18),
                    StatCategory(value=59.0, name="TO", key=19),
                    StatCategory(value=76.0, name="PF", key=21),
                    StatCategory(value=2.0, name="TECH", key=23),
                ],
            ),
            second_team=MatchupTeam(
                team=Team(key="410.l.22889.t.7", name="Explodo Heado"),
                team_points=6,
                stats=[
                    StatCategory(value=0.413, name="FG%", key=5),
                    StatCategory(value=54.0, name="FTM", key=7),
                    StatCategory(value=0.783, name="FT%", key=8),
                    StatCategory(value=51.0, name="3PTM", key=10),
                    StatCategory(value=389.0, name="PTS", key=12),
                    StatCategory(value=59.0, name="OREB", key=13),
                    StatCategory(value=158.0, name="DREB", key=14),
                    StatCategory(value=125.0, name="AST", key=16),
                    StatCategory(value=35.0, name="ST", key=17),
                    StatCategory(value=28.0, name="BLK", key=18),
                    StatCategory(value=79.0, name="TO", key=19),
                    StatCategory(value=87.0, name="PF", key=21),
                    StatCategory(value=1.0, name="TECH", key=23),
                ],
            ),
        )
        matchup = self._basketball.my_matchup(week=1)
        self.assertEqual(matchup, expected_matchup)

    def test_stat_categories(self):
        expected_stat_categories = [
            StatCategory(value=0, name="FG%", key=5),
            StatCategory(value=0, name="FTM", key=7),
            StatCategory(value=0, name="FT%", key=8),
            StatCategory(value=0, name="3PTM", key=10),
            StatCategory(value=0, name="PTS", key=12),
            StatCategory(value=0, name="OREB", key=13),
            StatCategory(value=0, name="DREB", key=14),
            StatCategory(value=0, name="AST", key=16),
            StatCategory(value=0, name="ST", key=17),
            StatCategory(value=0, name="BLK", key=18),
            StatCategory(value=0, name="TO", key=19),
            StatCategory(value=0, name="PF", key=21),
            StatCategory(value=0, name="TECH", key=23),
        ]
        categories = self._basketball.stat_categories()
        self.assertEqual(categories, expected_stat_categories)

    def test_stat_categories_as_dataframe(self):
        self.assertEqual(len(self._basketball.stat_categories_as_dataframe()), len(self._basketball.stat_categories()))

    def test_draft_results(self):
        expected_top_5_picks = [
            DraftPick(
                pick=1,
                round=1,
                player=Player(name="Nikola Jokic", key=5352),
                team=Team(key="410.l.22889.t.13", name="Ryan's Pleasant Team"),
                cost="77",
            ),
            DraftPick(
                pick=2,
                round=1,
                player=Player(name="Giannis Antetokounmpo", key=5185),
                team=Team(key="410.l.22889.t.1", name="bigesser"),
                cost="57",
            ),
            DraftPick(
                pick=3,
                round=1,
                player=Player(name="Trae Young", key=6016),
                team=Team(key="410.l.22889.t.11", name="Jake's Team"),
                cost="44",
            ),
            DraftPick(
                pick=4,
                round=1,
                player=Player(name="Stephen Curry", key=4612),
                team=Team(key="410.l.22889.t.3", name="allen's Unrivaled Team"),
                cost="67",
            ),
            DraftPick(
                pick=5,
                round=1,
                player=Player(name="Luka Doncic", key=6014),
                team=Team(key="410.l.22889.t.13", name="Ryan's Pleasant Team"),
                cost="62",
            ),
        ]
        draft_results = self._basketball.draft_results()
        self.assertEqual(144, len(draft_results))
        self.assertListEqual(expected_top_5_picks, draft_results[:5])

    def test_draft_results_as_dataframe(self):
        self.assertEqual(len(self._basketball.draft_results_as_dataframe()), len(self._basketball.draft_results()))

    @unittest.skip("For some reason past free agent lists seem to change")
    def test_free_agents(self):
        free_agents = self._basketball.free_agents("C")
        self.assertEqual(81, len(free_agents))
        # There are two many free agents to test so lets just pick the one at position 6
        self.assertEqual(
            Player(
                name="Serge Ibaka", key=4486, eligible_positions=["PF", "F", "C", "Util"], percent_owned=6, status=""
            ),
            free_agents[6],
        )

    def test_free_agents_as_dataframe(self):
        self.assertEqual(len(self._basketball.free_agents_as_dataframe("C")), len(self._basketball.free_agents("C")))

    def test_waivers(self):
        # Not a good test, but this old leagues don't have waivers. At best this validates the call but not the parsing.
        self.assertEqual(0, len(self._basketball.waivers()))

    def test_waivers_as_dataframe(self):
        # Not a good test, but this old leagues don't have waivers. At best this validates the call but not the parsing.
        self.assertEqual(0, len(self._basketball.waivers_as_dataframe()))


if __name__ == "__main__":
    unittest.main()
