import os
import shutil

from yahoo_oauth import OAuth2


def fetch_token() -> OAuth2:
    temp_token = "/tmp/yahoo_token"
    if not os.path.exists(temp_token):
        shutil.copyfile(
            os.path.dirname(__file__) + "/token.txt",
            temp_token,
        )
    try:
        sc = OAuth2(
            None,
            None,
            from_file=temp_token,
            browser_callback=False,
        )
        if not sc.token_is_valid():
            sc.refresh_access_token()
    except TypeError:
        os.remove(temp_token)

    return sc
