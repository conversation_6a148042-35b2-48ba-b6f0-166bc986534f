{"cells": [{"cell_type": "code", "execution_count": null, "id": "cc58c4f2", "metadata": {}, "outputs": [], "source": ["import sports.yahoo_fantasy.basketball as yahoo_basketball\n", "from sports.yahoo_fantasy.data_objects import Matchup, MatchupTeam, Team\n"]}, {"cell_type": "code", "execution_count": null, "id": "a96d4bc0", "metadata": {}, "outputs": [], "source": ["import yahoo_fantasy_api as yfa"]}, {"cell_type": "code", "execution_count": null, "id": "6ff2eef4", "metadata": {}, "outputs": [], "source": ["basketball = yahoo_basketball.Basketball(league_id=\"410.l.22889\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "9bcde34b", "metadata": {}, "outputs": [], "source": ["basketball.teams()"]}, {"cell_type": "code", "execution_count": null, "id": "1a822213", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.2"}}, "nbformat": 4, "nbformat_minor": 5}