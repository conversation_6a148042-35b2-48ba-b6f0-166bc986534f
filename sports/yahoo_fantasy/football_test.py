import unittest

import sports.yahoo_fantasy.football as yahoo_football
from sports.yahoo_fantasy.data_objects import (
    Matchup,
    MatchupTeam,
    StatCategory,
    Team,
    Draft<PERSON>ick,
    Player,
)


class TestFootball(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls._football = yahoo_football.Football(league_id="406.l.463378")

    def test_lookup_league_ids(self):
        expected_ids = ["406.l.1378263", "406.l.463378"]
        self.assertListEqual(expected_ids, self._football.lookup_league_ids(2021))

    def test_league_name(self):
        self.assertEqual("New Football League", self._football.league_name)

    def test_num_teams(self):
        self.assertEqual(12, self._football.num_teams)

    def test_current_week(self):
        self.assertEqual(17, self._football.current_week)

    def test_playoff_start_week(self):
        self.assertEqual(15, self._football.playoff_start_week)

    def test_end_week(self):
        self.assertEqual(17, self._football.end_week)

    def test_is_finished(self):
        self.assertTrue(self._football.is_finished)

    def test_my_team_key(self):
        self.assertEqual("406.l.463378.t.9", self._football.my_team_key)

    def test_my_team(self):
        self.assertEqual(Team(key="406.l.463378.t.9", name="Explodo Heado"), self._football.my_team)

    def test_teams(self):
        expected_teams = [
            Team(key="406.l.463378.t.1", name="Gweilo Gorillas"),
            Team(key="406.l.463378.t.2", name="4-a-days"),
            Team(key="406.l.463378.t.3", name="Big Blue Balls"),
            Team(key="406.l.463378.t.4", name="bigesser"),
            Team(key="406.l.463378.t.5", name="CEDENO"),
            Team(key="406.l.463378.t.6", name="Charles's Team"),
            Team(key="406.l.463378.t.7", name="ThunderfromDownUnder"),
            Team(key="406.l.463378.t.8", name="dougsofresh"),
            Team(key="406.l.463378.t.9", name="Explodo Heado"),
            Team(key="406.l.463378.t.10", name="What's good fam"),
            Team(key="406.l.463378.t.11", name="1st and 10 AHHHHHHHH"),
            Team(key="406.l.463378.t.12", name="Lasagnaisthebestfood"),
        ]
        actual_teams = self._football.teams()
        self.assertListEqual(expected_teams, actual_teams)
        self.assertEqual(len(actual_teams), self._football.num_teams)

    def test_teams_as_dataframe(self):
        self.assertEqual(len(self._football.teams_as_dataframe()), len(self._football.teams()))

    def test_matchups(self):
        expected_matchups = [
            Matchup(
                first_team=MatchupTeam(
                    team=Team(key="406.l.463378.t.1", name="Gweilo Gorillas"),
                    team_points=116.98,
                    win_probability=0.0,
                    stats=None,
                ),
                second_team=MatchupTeam(
                    team=Team(key="406.l.463378.t.2", name="4-a-days"),
                    team_points=122.08,
                    win_probability=1.0,
                    stats=None,
                ),
            ),
            Matchup(
                first_team=MatchupTeam(
                    team=Team(key="406.l.463378.t.3", name="Big Blue Balls"),
                    team_points=114.74,
                    win_probability=0.0,
                    stats=None,
                ),
                second_team=MatchupTeam(
                    team=Team(key="406.l.463378.t.11", name="1st and 10 AHHHHHHHH"),
                    team_points=134.72,
                    win_probability=1.0,
                    stats=None,
                ),
            ),
            Matchup(
                first_team=MatchupTeam(
                    team=Team(key="406.l.463378.t.4", name="bigesser"),
                    team_points=118.42,
                    win_probability=1.0,
                    stats=None,
                ),
                second_team=MatchupTeam(
                    team=Team(key="406.l.463378.t.10", name="What's good fam"),
                    team_points=109.6,
                    win_probability=0.0,
                    stats=None,
                ),
            ),
            Matchup(
                first_team=MatchupTeam(
                    team=Team(key="406.l.463378.t.5", name="CEDENO"),
                    team_points=103.86,
                    win_probability=0.0,
                    stats=None,
                ),
                second_team=MatchupTeam(
                    team=Team(key="406.l.463378.t.9", name="Explodo Heado"),
                    team_points=117.76,
                    win_probability=1.0,
                    stats=None,
                ),
            ),
            Matchup(
                first_team=MatchupTeam(
                    team=Team(key="406.l.463378.t.6", name="Charles's Team"),
                    team_points=80.8,
                    win_probability=0.0,
                    stats=None,
                ),
                second_team=MatchupTeam(
                    team=Team(key="406.l.463378.t.8", name="dougsofresh"),
                    team_points=94.7,
                    win_probability=1.0,
                    stats=None,
                ),
            ),
            Matchup(
                first_team=MatchupTeam(
                    team=Team(key="406.l.463378.t.7", name="ThunderfromDownUnder"),
                    team_points=159.46,
                    win_probability=1.0,
                    stats=None,
                ),
                second_team=MatchupTeam(
                    team=Team(key="406.l.463378.t.12", name="Lasagnaisthebestfood"),
                    team_points=103.18,
                    win_probability=0.0,
                    stats=None,
                ),
            ),
        ]
        matchups = self._football.matchups(week=1)
        self.assertListEqual(matchups, expected_matchups)

    def test_my_matchup(self):
        expected_matchup = Matchup(
            first_team=MatchupTeam(
                team=Team(key="406.l.463378.t.5", name="CEDENO"), team_points=103.86, win_probability=0.0, stats=None
            ),
            second_team=MatchupTeam(
                team=Team(key="406.l.463378.t.9", name="Explodo Heado"),
                team_points=117.76,
                win_probability=1.0,
                stats=None,
            ),
        )
        matchup = self._football.my_matchup(week=1)
        self.assertEqual(matchup, expected_matchup)

    def test_stat_categories(self):
        expected_stat_categories = [
            StatCategory(value=0, name="Pass Yds", key=4),
            StatCategory(value=0, name="Pass TD", key=5),
            StatCategory(value=0, name="Int", key=33),
            StatCategory(value=0, name="Rush Yds", key=9),
            StatCategory(value=0, name="Rush TD", key=10),
            StatCategory(value=0, name="Rec", key=11),
            StatCategory(value=0, name="Rec Yds", key=12),
            StatCategory(value=0, name="Rec TD", key=13),
            StatCategory(value=0, name="Ret TD", key=49),
            StatCategory(value=0, name="2-PT", key=16),
            StatCategory(value=0, name="Fum Lost", key=18),
            StatCategory(value=0, name="Fum Ret TD", key=57),
            StatCategory(value=0, name="FG 0-19", key=19),
            StatCategory(value=0, name="FG 20-29", key=20),
            StatCategory(value=0, name="FG 30-39", key=21),
            StatCategory(value=0, name="FG 40-49", key=22),
            StatCategory(value=0, name="FG 50+", key=23),
            StatCategory(value=0, name="FGM 0-19", key=24),
            StatCategory(value=0, name="FGM 20-29", key=25),
            StatCategory(value=0, name="FGM 30-39", key=26),
            StatCategory(value=0, name="FGM 40-49", key=27),
            StatCategory(value=0, name="FGM 50+", key=28),
            StatCategory(value=0, name="PAT Made", key=29),
            StatCategory(value=0, name="PAT Miss", key=30),
            StatCategory(value=0, name="Sack", key=32),
            StatCategory(value=0, name="Int", key=33),
            StatCategory(value=0, name="Fum Rec", key=34),
            StatCategory(value=0, name="TD", key=35),
            StatCategory(value=0, name="Safe", key=36),
            StatCategory(value=0, name="Blk Kick", key=37),
            StatCategory(value=0, name="Ret TD", key=49),
            StatCategory(value=0, name="Pts Allow 0", key=50),
            StatCategory(value=0, name="Pts Allow 1-6", key=51),
            StatCategory(value=0, name="Pts Allow 7-13", key=52),
            StatCategory(value=0, name="Pts Allow 14-20", key=53),
            StatCategory(value=0, name="Pts Allow 21-27", key=54),
            StatCategory(value=0, name="Pts Allow 28-34", key=55),
            StatCategory(value=0, name="Pts Allow 35+", key=56),
            StatCategory(value=0, name="XPR", key=82),
        ]
        categories = self._football.stat_categories()
        self.assertEqual(categories, expected_stat_categories)

    def test_stat_categories_as_dataframe(self):
        self.assertEqual(len(self._football.stat_categories_as_dataframe()), len(self._football.stat_categories()))

    def test_draft_results(self):
        expected_top_5_picks = [
            DraftPick(
                pick=1,
                round=1,
                player=Player(name="Christian McCaffrey", key=30121),
                team=Team(key="406.l.463378.t.2", name="4-a-days"),
                cost=None,
            ),
            DraftPick(
                pick=2,
                round=1,
                player=Player(name="Dalvin Cook", key=30154),
                team=Team(key="406.l.463378.t.9", name="Explodo Heado"),
                cost=None,
            ),
            DraftPick(
                pick=3,
                round=1,
                player=Player(name="Ezekiel Elliott", key=29238),
                team=Team(key="406.l.463378.t.8", name="dougsofresh"),
                cost=None,
            ),
            DraftPick(
                pick=4,
                round=1,
                player=Player(name="Derrick Henry", key=29279),
                team=Team(key="406.l.463378.t.4", name="bigesser"),
                cost=None,
            ),
            DraftPick(
                pick=5,
                round=1,
                player=Player(name="Alvin Kamara", key=30180),
                team=Team(key="406.l.463378.t.3", name="Big Blue Balls"),
                cost=None,
            ),
        ]

        draft_results = self._football.draft_results()
        self.assertEqual(156, len(draft_results))
        self.assertListEqual(expected_top_5_picks, draft_results[:5])

    def test_draft_results_as_dataframe(self):
        self.assertEqual(len(self._football.draft_results_as_dataframe()), len(self._football.draft_results()))

    def test_free_agents(self):
        # Not a good test, but this league has no free agents. At best this validates the call but not the parsing.
        self.assertEqual(0, len(self._football.free_agents("RB")))

    def test_free_agents_as_dataframe(self):
        # Not a good test, but this league has no free agents. At best this validates the call but not the parsing.
        self.assertEqual(0, len(self._football.free_agents_as_dataframe("RB")))

    @unittest.skip("Fetches too many players (i.e makes 500+ calls")
    def test_waivers(self):
        waivers = self._football.waivers()
        self.assertEqual(568, len(waivers))
        self.assertEqual(
            Player(name="Josh Johnson", key=8937, eligible_positions=["QB"], percent_owned=1, status=""), waivers[6]
        )

    @unittest.skip("Fetches too many players (i.e makes 500+ calls")
    def test_waivers_as_dataframe(self):
        self.assertEqual(len(self._football.waivers_as_dataframe()), len(self._football.waivers()))


if __name__ == "__main__":
    unittest.main()
