from __future__ import annotations
from abc import ABC, abstractmethod
from functools import cached_property
from sports.yahoo_fantasy.yahoo_token import fetch_token
from sports.yahoo_fantasy.data_objects import (
    dataclasses_to_dataframe,
    Matchup,
    StatCategory,
    Team,
    <PERSON><PERSON><PERSON>,
    Player,
)
import yahoo_fantasy_api as yfa
import pandas as pd


class Sport(ABC):
    def __init__(self, sport_code: str = None, league_id: str = None):
        self._token = fetch_token()
        self._game = yfa.Game(self._token, sport_code)
        if league_id is not None:
            self._league = self._game.to_league(league_id)
            self._league._cache_stats_id_map(self._game.game_id())

    def lookup_league_ids(self, year: int) -> list[str]:
        return self._game.league_ids(year)

    @property
    def settings(self) -> dict:
        return self._league.settings()

    @property
    def league_name(self) -> str:
        return self.settings["name"]

    @property
    def num_teams(self) -> int:
        return int(self.settings["num_teams"])

    @property
    def current_week(self) -> int:
        return int(self.settings["current_week"])

    @property
    def playoff_start_week(self) -> int:
        return int(self.settings["playoff_start_week"])

    @property
    def end_week(self) -> int:
        return int(self.settings["end_week"])

    @property
    def is_finished(self) -> bool:
        if "is_finished" in self.settings:
            return bool(self.settings["is_finished"])
        else:
            return False

    @cached_property
    def my_team_key(self):
        return self._league.team_key()

    @property
    def my_team(self) -> Team:
        return [x for x in self.teams() if x.key == self.my_team_key][0]

    def teams(self) -> list[Team]:
        return [
            Team(k, v["name"]) for k, v in sorted(self._league.teams().items(), key=lambda k_v: int(k_v[1]["team_id"]))
        ]

    @dataclasses_to_dataframe
    def teams_as_dataframe(self) -> pd.DataFrame:
        return self.teams()

    @abstractmethod
    def matchups(self, week: int = None) -> list[Matchup]:
        pass

    def my_matchup(self, week: int = None) -> Matchup:
        team_key = self.my_team_key
        matchups = self.matchups(week)
        for matchup in matchups:
            if matchup.first_team.team.key == team_key or matchup.second_team.team.key == team_key:
                return matchup
        return None

    def stat_categories(self) -> list[StatCategory]:
        inv_cat_map = dict([(value, key) for key, value in self._league.stats_id_map.items()])
        categories = []
        for c in self._league.stat_categories():
            categories.append(StatCategory(name=c["display_name"], key=inv_cat_map[c["display_name"]]))
        return categories

    @dataclasses_to_dataframe
    def stat_categories_as_dataframe(self) -> pd.DataFrame:
        return self.stat_categories()

    def draft_results(self) -> list[DraftPick]:
        team_map = {e.key: e for e in self.teams()}
        dr = self._league.draft_results()
        ids = [e["player_id"] for e in dr]
        self._league.player_details(ids)  # Prime the player detail cache
        picks = []
        for dp in dr:
            player = self._league.player_details(dp["player_id"])
            picks.append(
                DraftPick(
                    round=dp["round"],
                    pick=dp["pick"],
                    cost=dp["cost"] if "cost" in dp else None,
                    team=team_map[dp["team_key"]],
                    player=Player(name=player[0]["name"]["full"], key=dp["player_id"]),
                )
            )
        return picks

    @dataclasses_to_dataframe
    def draft_results_as_dataframe(self) -> pd.DataFrame:
        return self.draft_results()

    def free_agents(self, position: str) -> list[Player]:
        free_agents = []
        for player in self._league.free_agents(position=position):
            free_agents.append(
                Player(
                    name=player["name"],
                    key=player["player_id"],
                    eligible_positions=player["eligible_positions"],
                    percent_owned=player["percent_owned"],
                    status=player["status"],
                )
            )
        return free_agents

    @dataclasses_to_dataframe
    def free_agents_as_dataframe(self, position: str) -> pd.DataFrame:
        return self.free_agents(position)

    def waivers(self) -> list[Player]:
        free_agents = []
        for player in self._league.waivers():
            free_agents.append(
                Player(
                    name=player["name"],
                    key=player["player_id"],
                    eligible_positions=player["eligible_positions"],
                    percent_owned=player["percent_owned"],
                    status=player["status"],
                )
            )
        return free_agents

    @dataclasses_to_dataframe
    def waivers_as_dataframe(self) -> pd.DataFrame:
        return self.waivers()

    def my_team_object(self):
        return self._league.to_team(self.my_team_key)
